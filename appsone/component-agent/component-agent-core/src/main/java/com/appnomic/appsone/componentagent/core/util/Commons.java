package com.appnomic.appsone.componentagent.core.util;

import com.appnomic.appsone.common.exception.AppsOneException;
import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.appnomic.appsone.common.util.ConfProperties;
import com.appnomic.appsone.common.util.ResourceBundleUtils;
import com.appnomic.appsone.componentagent.common.ComponentAgentException;
import com.appnomic.appsone.componentagent.common.beans.*;
import com.appnomic.appsone.componentagent.common.beans.intermediate.AccountDetail;
import com.appnomic.appsone.componentagent.common.enums.ErrorCodes;
import com.appnomic.appsone.componentagent.common.enums.KpiDataType;
import com.appnomic.appsone.componentagent.common.enums.KpiType;
import com.appnomic.appsone.componentagent.common.enums.KpiValueType;
import com.appnomic.appsone.componentagent.common.intf.EndianParser;
import com.appnomic.appsone.componentagent.common.intf.ICollectionBase;
import com.appnomic.appsone.componentagent.common.util.CommonUtil;
import com.appnomic.appsone.componentagent.common.util.NetAgentProcessor;
import com.appnomic.appsone.componentagent.core.Constants;
import com.appnomic.appsone.componentagent.core.DataQueue;
import com.appnomic.appsone.componentagent.core.beans.*;
import com.appnomic.appsone.componentagent.core.cache.ComponentAgentCache;
import com.appnomic.appsone.componentagent.core.cache.DataCache;
import com.appnomic.appsone.componentagent.core.cache.ForensicCache;
import mousio.etcd4j.EtcdClient;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.util.encoders.Base64;
import org.quartz.CronExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.net.URI;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.nio.file.attribute.BasicFileAttributes;
import java.security.*;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.GZIPOutputStream;

/**
 * Created by prasad on 5/5/16.
 */
public class Commons {
    private static final Logger log = LoggerFactory.getLogger(Commons.class);
    private static final String CRON_TIME_ZONE = ConfProperties.getString(Constants.CRON_TIME_ZONE);
    // For logging the scripts that are breaching configured threshold and convert into milli seconds
    private static final int execThreshold = 1000 * ConfProperties.getInt(Constants.CA_PRODUCERS_EXEC_TIME_THRESHOLD, Constants.CA_PRODUCERS_EXEC_TIME_THRESHOLD_DEFAULT);

    /**
     * Loads data collector class and instantiates
     *
     * @param className - Name of the class to be loaded and instantiated
     * @return - data collector instance
     */
    public static ICollectionBase instantiate(String className, Map<String, String> attributes, List<Parameter> parameters,
                                              Set<KpiDetails> kpiDetails, String producerName, String instanceName) throws ComponentAgentException {
        ICollectionBase collector;

        try {
            log.trace("Loading class {} for producer {} of component instance {}.", className, producerName, instanceName);
            ClassLoader currentClassLoader = Thread.currentThread().getContextClassLoader();
            Class collectorClass = currentClassLoader.loadClass(className);
            collector = (ICollectionBase) collectorClass.newInstance();
            collector.setAttributes(attributes);
            collector.setParameters(parameters);
            collector.setKPIs(kpiDetails);
            collector.setExecutionName(producerName);
            collector.setInstanceName(instanceName);
            log.trace("Instantiated class {} for producer {} of component instance {}.", className, producerName, instanceName);
        } catch (ClassNotFoundException e) {
            throw new ComponentAgentException(e, ErrorCodes.CLASS_NOT_FOUND.getCode(),
                    ResourceBundleUtils.getString(ErrorCodes.CLASS_NOT_FOUND.getCode(), className));
        } catch (IllegalAccessException e) {
            throw new ComponentAgentException(e, ErrorCodes.ILLEGAL_ACCESS_EXCEPTION.getCode(),
                    ResourceBundleUtils.getString(ErrorCodes.ILLEGAL_ACCESS_EXCEPTION.getCode(), className));
        } catch (Exception e) {
            throw new ComponentAgentException(e, ErrorCodes.INSTANTIATION_EXCEPTION.getCode(),
                    ResourceBundleUtils.getString(ErrorCodes.INSTANTIATION_EXCEPTION.getCode(), className));
        } catch (Throwable e) {
            throw new ComponentAgentException(ErrorCodes.INSTANTIATION_EXCEPTION.getCode(),
                    ResourceBundleUtils.getString(ErrorCodes.INSTANTIATION_EXCEPTION.getCode(), className));
        }
        return collector;
    }

    public static boolean isActive(int status) {
        return 1 == status;
    }

    public static boolean monitor(int monitorFlag) {
        return 1 == monitorFlag;
    }

    public static boolean isEligibleForScheduling(int interValSec, Long pastRunningTime, Long currentRunningTime) {
        if (pastRunningTime == null) {
            return true;
        } else {
            return (currentRunningTime - pastRunningTime - 5) >= ((interValSec - 5) * 1000L);
        }
    }

    public static boolean isEligibleForScheduling(String cronExpStr, Date collectionTime, String producerName,
                                                  String compInstName) throws ComponentAgentException {
        if(cronExpStr == null || cronExpStr.trim().isEmpty()) {
            return true;
        }
        try {

            CronExpression cronExpression = new CronExpression(cronExpStr);
            if(CRON_TIME_ZONE != null && !CRON_TIME_ZONE.trim().isEmpty()) {
                cronExpression.setTimeZone(TimeZone.getTimeZone(CRON_TIME_ZONE));
            }
            boolean isSatisfied = cronExpression.isSatisfiedBy(collectionTime);
            log.trace("Collection time:{}, Cron expression:{}, producer name:{}, comp instance:{}, isSatisfied:{}, cron timezone:{}", collectionTime, cronExpStr, producerName, compInstName, isSatisfied, CRON_TIME_ZONE);
            return isSatisfied;
        } catch (Exception e) {
            throw new ComponentAgentException(e, ErrorCodes.INVALID_CRON_EXPRESSION.getCode(),
                    ResourceBundleUtils.getString(ErrorCodes.INVALID_CRON_EXPRESSION.getCode(),
                            cronExpStr, producerName, compInstName, ErrorCodes.INVALID_CRON_EXPRESSION.getCode()));
        }
    }

    public static boolean isProducerEligibleToExecute(Producer producer, String dataCollectionMode, String instanceIdentifier) {
        log.trace("Data collection mode set is {}", dataCollectionMode);
        try {
            if(dataCollectionMode == null || dataCollectionMode.trim().isEmpty()) {
                return true;
            }

            if(dataCollectionMode.trim().equalsIgnoreCase("ALL")) {
                return true;
            }
            log.trace("Producer kpi details {}", producer.getKpiDetails());
            boolean kpiFound = producer.getKpiDetails().stream().anyMatch(a -> dataCollectionMode.toLowerCase().contains(a.getImportanceType().toLowerCase()));
            log.trace("KPI with data collection mode {} present {} for producer {}.", dataCollectionMode, kpiFound, producer.getName());
            return kpiFound;
        } catch (Exception e) {
            log.error("Error occurred while checking the producer eligibility for data collection mode:{}, instanceIdentifier:{}, producer:{}", dataCollectionMode, instanceIdentifier, producer.getName(), e);
            return false;
        }
    }

    public static String getCronExpression(Set<KpiDetails> kpiDetailsSet) {
        List<KpiDetails> kpiDetailsList = new ArrayList<>(kpiDetailsSet);
        return kpiDetailsList.get(0).getCronExpression();
    }

    public static String getHashCode(int producerId, String compInstId, int interval) {
        return producerId+"_"+compInstId+"_"+interval;
    }

    public static byte[] getFileContentAndPutIntoCache(String scriptName) throws ComponentAgentException {
        //Get file content from cache if exists.
        byte[] fileContent = ComponentAgentCache.getInstance().getScriptContent(scriptName);
        if(fileContent == null) {
            fileContent = getUpdatedFileContent(scriptName);
            String strFileContent = new String(fileContent, StandardCharsets.UTF_8);
            fileContent = CommonUtil.setEnvData(strFileContent, com.appnomic.appsone.componentagent.common.beans.Constants.ENV_SEPARATOR_VARIABLE_VALUE,
                    com.appnomic.appsone.componentagent.common.beans.Constants.ENV_SEPARATOR_VARIABLE_NAME);
            ComponentAgentCache.getInstance().addScriptContent(scriptName, fileContent);
            log.debug("Added the {} file into cache.", scriptName);
        }
        return fileContent;
    }

    public static String replaceLibrary(String content) throws ComponentAgentException {
        String regex = ".*#@@@.*#@@@.*";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        while(matcher.find()){
            String grp = matcher.group();
            String libFileName = getFileName(grp);
            String value = CommonUtil.getFileContent(libFileName);
            content = content.replace(grp, value);
        }
        return content;
    }

    public static String getFileName(String content) {
        String regex = "#@@@.*#@@@";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        String fileName = "";
        while(matcher.find()){
            String grp = matcher.group();
            fileName = grp.replace("#@@@", "");
        }
        return fileName;
    }

    public static byte[] getUpdatedFileContent(String scriptName) throws ComponentAgentException {
        String fileContent = CommonUtil.getFileContent(scriptName);
        return replaceLibrary(fileContent).getBytes(StandardCharsets.UTF_8);
    }

    public static boolean verifySignature(byte[] content, PublicKey publicKey, byte[] signature) throws
            SignatureException, InvalidKeyException, NoSuchAlgorithmException, NoSuchProviderException {
        Signature ecdsaVerify = Signature.getInstance("SHA256withECDSA", "BC");
        ecdsaVerify.initVerify(publicKey);
        ecdsaVerify.update(content);
        return ecdsaVerify.verify(signature);
    }

    public static boolean isValidSign(int accountId, String scriptFile, String signature, boolean isFullPath) throws ComponentAgentException {

        String publicKey = ComponentAgentCache.getInstance().getAgentBean().getAccountDetails()
                .stream().filter(detail -> detail.getId() == accountId).map(AccountDetail::getPublicKey)
                .findAny().orElse(null);

        if(StringUtils.isEmpty(publicKey)){
            return isValidSign(Commons.getPublicKeyFileName(accountId), scriptFile, signature, isFullPath);
        }
        else{
            return isValidSign(publicKey.getBytes(StandardCharsets.UTF_8), scriptFile, signature, isFullPath);
        }
    }

    public static boolean isValidSign(byte[] encodedPublicKey, String scriptFile, String signature, boolean isFullPath) throws ComponentAgentException {
        try {
            PublicKey publicKey = com.appnomic.appsone.common.util.Commons.getPublicKey(Base64.decode(encodedPublicKey));
            byte[] scriptContent = CommonUtil.getFileContentInBytes(scriptFile, isFullPath);
            byte[] signatureInBytes = Base64.decode(signature.getBytes(StandardCharsets.UTF_8));
            return verifySignature(scriptContent, publicKey, signatureInBytes);
        } catch (ComponentAgentException e) {
            throw e;
        } catch (Exception e) {
            throw new ComponentAgentException(e, ErrorCodes.SIGNATURE_VERIFICATION_ERROR.getCode(),
                                              ResourceBundleUtils.getString(ErrorCodes.SIGNATURE_VERIFICATION_ERROR.getCode(), scriptFile));
        }
    }

    public static boolean isValidSign(String publicKeyFile, String scriptFile, String signature, boolean isFullPath) throws ComponentAgentException {
        try {
            byte[] encodedPublicKey = CommonUtil.getFileContentInBytes(publicKeyFile, false);
            return isValidSign(encodedPublicKey, scriptFile, signature, isFullPath);
        } catch (ComponentAgentException e) {
          throw e;
        } catch (Exception e) {
            throw new ComponentAgentException(e, ErrorCodes.SIGNATURE_VERIFICATION_ERROR.getCode(),
                    ResourceBundleUtils.getString(ErrorCodes.SIGNATURE_VERIFICATION_ERROR.getCode(), scriptFile));
        }
    }

    public static String getPublicKeyFileName(int accountId) {
        return Constants.CA_KEY_FILES_PATH +
                accountId +
                Constants.CA_PUBLIC_KEY_FILE_EXTENSION;
    }


    public static String getAttributeValue(String attributeType, String attributeValue) throws ComponentAgentException {
        if(!com.appnomic.appsone.componentagent.common.beans.Constants.PASSWORD_TYPE.equals(attributeType)) {
            return attributeValue;
        }

        if(attributeValue == null || attributeValue.trim().isEmpty()) {
            return attributeValue;
        }
        String decryptValue;
        try {
            decryptValue = com.appnomic.appsone.common.util.Commons.decrypt(attributeValue);
        } catch (Exception e) {
            throw new ComponentAgentException(e, ErrorCodes.DECRYPTION_ERROR.getCode(),
                    ResourceBundleUtils.getString(ErrorCodes.DECRYPTION_ERROR.getCode(), e.getMessage()));
        }
        return decryptValue;
    }

    public static String getQuery(String isQueryEncryptedStr, String query) throws ComponentAgentException {
        try {
            if(isQueryEncryptedStr == null || !Boolean.getBoolean(isQueryEncryptedStr)) {
                return query;
            }
            return com.appnomic.appsone.common.util.Commons.decrypt(query);
        } catch (Exception e) {
            throw new ComponentAgentException(e, ErrorCodes.DECRYPTION_ERROR.getCode(),
                    ResourceBundleUtils.getString(ErrorCodes.DECRYPTION_ERROR.getCode(), e.getMessage()));
        }
    }

    /*
    1. If config property value is not set, return default value
    2. If config property value is less than min value, return min value.
    3. If config property value is more than max value, return max value.
    4. If config property value is between min value and max values, return config value.
    5. For offline deactivated if config value is -1, then return config value.
    6. If config property value is invalid (Non numeric), return default value.

    For data sender if config val is 1<= val <= minval then return min val
    If config val is less than zero than return default val
     */
    public static long getConfigValue(String property, int defaultVal, int minVal, int maxVal, ErrorCodes minErrorCodes, ErrorCodes maxErrorCodes){
        String value = ConfProperties.getString(property);
        if(value == null || value.isEmpty()){
            log.debug("{}={}", property, defaultVal);
            return defaultVal;
        }
        long result;

        if (!Pattern.matches("-?\\d+",value)) {
            result = ConfProperties.getInt(property, defaultVal);
            return result;
        }

        result = ConfProperties.getInt(property, minVal);
        if(property.equals(Constants.CA_CONFIG_OFFLINE_MODE_DEACTIVATOR_FREQUENCY_DEF) && result == -1){
            log.debug("{}={}", property, result);
            log.info("Offline de-activator will never wakeup.");
            return result;
        }
        if(property.equals(Constants.CA_CONFIG_DATA_PUSH_FREQUENCY_DEF)){
            if(0 < result && result < minVal){
                log.debug("{}={}", property, minVal);
                return result;
            }
            if(result < 0){
                log.debug("{}={}", property, defaultVal);
                return result;
            }
        }
        if(result < minVal){
            log.error(ResourceBundleUtils.getString(minErrorCodes.getCode(),
                    ConfProperties.getString(property), minVal));
            return minVal;
        }else if(result > maxVal){
            log.error(ResourceBundleUtils.getString(maxErrorCodes.getCode(),
                    ConfProperties.getString(property), maxVal));
            return maxVal;
        }else {
            log.debug("{}={}", property, result);
            return result;
        }

    }

    public static EtcdClient getEtcdClient() throws AppsOneException {
        String etcdNodes = com.appnomic.appsone.common.util.Commons.getEtcdNodes();
        String[] etcdNodesArray = etcdNodes.split(",");
        List<URI> etcdURIs = new ArrayList<>();
        for (String node : etcdNodesArray) {
            etcdURIs.add(URI.create(node));
        }

        URI[] uris = new URI[etcdURIs.size()];
        return new EtcdClient(etcdURIs.toArray(uris));
    }

    public static boolean checkHighAvailability(String agentIdentifier) throws Exception {
        log.trace("Inside checkHighAvailability(agentIdentifier:{}) method.", agentIdentifier);
        boolean isHAenabled = false;
        int haEnableFlag = ConfProperties.getInt(Constants.CA_HA_ENABLED, Constants.CA_HA_ENABLED_DEFAULT);
        if(haEnableFlag == 1) {
            isHAenabled = true;
            int ttlInMins = ConfProperties.getInt(Constants.CA_HA_TTL_TIME, Constants.CA_HA_TTL_TIME_DEFAULT);
            int lockCheckInMin = ConfProperties.getInt(Constants.CA_HA_LOCK_CHECK_TIME, Constants.CA_HA_LOCK_CHECK_TIME_DEFAULT);
            boolean isAcquired = false;
            while (!isAcquired) {
                isAcquired = ComponentAgentCache.getInstance().acquireLock(agentIdentifier, ttlInMins);

                if(!isAcquired) {
                    Thread.sleep(lockCheckInMin * 60 * 1000L);
                }
            }
        }
        log.trace("Completed checkHighAvailability(agentIdentifier:{}) method.", agentIdentifier);
        return isHAenabled;
    }

    public static int getMinuteSchedulerPoolSize() {
        int schedulerPoolSize = ConfProperties.getInt(Constants.CA_PRODUCERS_MINUTE_THREAD_POOL_SIZE, Constants.CA_PRODUCERS_MINUTE_THREAD_POOL_DEFAULT);
        float schedulerPoolFactor = ConfProperties.getFloat(Constants.CA_PRODUCERS_MINUTE_THREAD_POOL_FACTOR, Constants.CA_PRODUCERS_MINUTE_THREAD_POOL_FACTOR_DEFAULT);
        int processorCount = ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors();

        // Calculate the maximum thread pool size
        if(schedulerPoolFactor > 0) {
            schedulerPoolSize = (int) Math.ceil(processorCount * schedulerPoolFactor);
            // Log into diagnostic log
            Diagnostics.getLogger().info("Minute thread pool size calculated: {}, number of processors: {}, factor: {}",
                    schedulerPoolSize, processorCount, schedulerPoolFactor);
        }

        // Warn about too high configuration of maximum thread pool count/factor
        float maxPoolWarnFactor = ConfProperties.getFloat(Constants.CA_PRODUCERS_MINUTE_THREAD_POOL_WARN_FACTOR, Constants.CA_PRODUCERS_MINUTE_THREAD_POOL_WARN_FACTOR_DEFAULT);
        if(maxPoolWarnFactor > 0) {
            float calcValue = (float) schedulerPoolSize / processorCount;
            if (calcValue > maxPoolWarnFactor) {
                Diagnostics.getLogger().warn("Minute thread pool size is too high {} for the number of processors {}",
                        schedulerPoolSize, processorCount);
            }
        }

        return schedulerPoolSize;
    }

    public static int getMinuteSchedulerPoolQueueSize() {
        int schedulerPoolQueueSize = ConfProperties.getInt(Constants.CA_PRODUCERS_MINUTE_THREAD_POOL_QUEUE_SIZE, Constants.CA_PRODUCERS_MINUTE_THREAD_QUEUE_SIZE);
        /* 2 per second rate items queued into thread pool */
        if(schedulerPoolQueueSize < Constants.CA_PRODUCERS_MINUTE_THREAD_QUEUE_MIN_SIZE) {
            Diagnostics.getLogger().warn("Minute thread pool internal queue size configured is too low. Queue size: {}",
                    schedulerPoolQueueSize);
        }

        if(schedulerPoolQueueSize > Constants.CA_PRODUCERS_MINUTE_THREAD_QUEUE_MAX_SIZE) {
            Diagnostics.getLogger().warn("Minute thread pool internal queue size configured is too high. Queue size: {}",
                    schedulerPoolQueueSize);
        }

        return schedulerPoolQueueSize;
    }

    public static int getMaxMinuteThreadPoolSize() {
        float maxSizeMultiplyFactor = ConfProperties.getFloat(Constants.CA_PRODUCERS_MINUTE_THREAD_POOL_MAX_FACTOR, Constants.CA_PRODUCERS_MINUTE_THREAD_POOL_MAX_FACTOR_DEFAULT);
        if(maxSizeMultiplyFactor <= 0) {
            Diagnostics.getLogger().warn("Minute thread pool max size factor is not configured, So auto sizing will be displayed. Multiplier factor {}",
                    maxSizeMultiplyFactor);
            return 0;
        }

        int maxPoolSize = (int) (ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors() * maxSizeMultiplyFactor);
        int poolSize = getMinuteSchedulerPoolSize();
        if(maxPoolSize <= poolSize) {
            Diagnostics.getLogger().warn("Maximum size pool cant be less than or equal pool size, so auto sizing will be disabled. Multiplier factor {}, Max pool size:{}, Core pool size:{}", maxSizeMultiplyFactor, maxPoolSize, poolSize);
            return 0;
        }
        return maxPoolSize;
    }

    public static void checkAndAutoSizeThreadPool() {
        try {
            int maxSizeLimit = getMaxMinuteThreadPoolSize();
            //Auto sizing is disabled.
            if (maxSizeLimit == 0) {
                return;
            }
            ThreadPoolExecutor executor = ThreadPool.INSTANCE.minuteExecutor;
            if (executor.getCorePoolSize() < maxSizeLimit &&
                    executor.getQueue().size() > executor.getCorePoolSize()) {
                executor.setMaximumPoolSize(executor.getMaximumPoolSize() + 1);
                executor.setCorePoolSize(executor.getCorePoolSize() + 1);
                ThreadPool.INSTANCE.logMinuteExecutorStats();
                Diagnostics.getLogger().info("Minute thread pool sizes got updated.");
            }
        } catch (Exception e) {
            log.error("Error occurred while auto sizing the thread pool.", e);
        }
    }

    public static void resetMinuteThreadPool() {
        try {
            int maxSizeLimit = getMaxMinuteThreadPoolSize();
            //Auto sizing is disabled.
            if (maxSizeLimit == 0) {
                return;
            }

            ThreadPoolExecutor executor = ThreadPool.INSTANCE.minuteExecutor;
            int schedulerPoolSize = Commons.getMinuteSchedulerPoolSize();

            //If Queue size is 0 and current pool size is greater than default pool size, then reset the pool size to default.
            if (executor.getQueue().isEmpty() && executor.getActiveCount() <= schedulerPoolSize) {
                int currentPoolSize = executor.getCorePoolSize();
                int activeSize = executor.getActiveCount();
                executor.setCorePoolSize(schedulerPoolSize);
                executor.setMaximumPoolSize(schedulerPoolSize);
                Diagnostics.getLogger().info("Minute thread pool sizes got reset to defaults. Current Pool size:{}, Active size:{}, Default/Updated Pool size:{}", currentPoolSize, activeSize, schedulerPoolSize);
            } else if (!executor.getQueue().isEmpty()) {
                Diagnostics.getLogger().debug("Minute thread pool size is not eligible for reset to defaults. Queue size:{}, Pool size: {}", executor.getQueue().size(), executor.getCorePoolSize());
            }
        } catch (Exception e) {
            log.error("Error occurred while reset the minute thread pool.", e);
        }
    }

    public static int getLongRunningSchedulerPoolSize() {
        int schedulerPoolSize = ConfProperties.getInt(Constants.CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_SIZE,
                Constants.CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_DEFAULT);
        float schedulerPoolFactor = ConfProperties.getFloat(Constants.CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_FACTOR,
                Constants.CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_FACTOR_DEFAULT);
        int processorCount = ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors();

        // Calculate the maximum thread pool size
        if(schedulerPoolFactor > 0) {
            schedulerPoolSize = (int) Math.ceil(processorCount * schedulerPoolFactor);
            // Log into diagnostic log
            Diagnostics.getLogger().info("Long running thread pool size calculated: {}, number of processors: {}, factor: {}",
                    schedulerPoolSize, processorCount, schedulerPoolFactor);
        }

        // Warn about too high configuration of maximum thread pool count/factor
        float maxPoolWarnFactor = ConfProperties.getFloat(Constants.CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_WARN_FACTOR,
                Constants.CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_WARN_FACTOR_DEFAULT);
        if(maxPoolWarnFactor > 0) {
            float calcValue = (float) schedulerPoolSize / processorCount;
            if (calcValue > maxPoolWarnFactor) {
                Diagnostics.getLogger().warn("Long running thread pool size is too high {} for the number of processors {}",
                        schedulerPoolSize, processorCount);
            }
        }

        return schedulerPoolSize;
    }

    public static int getLongRunningSchedulerPoolQueueSize() {
        int schedulerPoolQueueSize = ConfProperties.getInt(Constants.CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_QUEUE_SIZE,
                Constants.CA_PRODUCERS_LONG_RUNNING_THREAD_QUEUE_SIZE);
        /* 2 per second rate items queued into thread pool */
        if(schedulerPoolQueueSize < Constants.CA_PRODUCERS_LONG_RUNNING_THREAD_QUEUE_MIN_SIZE) {
            Diagnostics.getLogger().warn("Long running thread pool internal queue size configured is too low. Queue size: {}",
                    schedulerPoolQueueSize);
        }

        if(schedulerPoolQueueSize > Constants.CA_PRODUCERS_LONG_RUNNING_THREAD_QUEUE_MAX_SIZE) {
            Diagnostics.getLogger().warn("Long running thread pool internal queue size configured is too high. Queue size: {}",
                    schedulerPoolQueueSize);
        }

        return schedulerPoolQueueSize;
    }

    public static int getSecondSchedulerPoolSize() {
        int schedulerPoolSize = ConfProperties.getInt(Constants.CA_PRODUCERS_SECOND_THREAD_POOL_SIZE, Constants.CA_PRODUCERS_SECOND_THREAD_POOL_DEFAULT);
        float schedulerPoolFactor = ConfProperties.getFloat(Constants.CA_PRODUCERS_SECOND_THREAD_POOL_FACTOR, Constants.CA_PRODUCERS_SECOND_THREAD_POOL_FACTOR_DEFAULT);
        int processorCount = ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors();

        if(schedulerPoolFactor > 0) {
            // Calculate the maximum thread pool size
            schedulerPoolSize = (int) Math.ceil(ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors() * schedulerPoolFactor);
            //
            Diagnostics.getLogger().info("Second thread pool size calculated: {}, number of processors: {}, factor: {}",
                    schedulerPoolSize, ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors(), schedulerPoolFactor);
        }

        // Warn about too high configuration of maximum thread pool count/factor
        float maxPoolWarnFactor = ConfProperties.getFloat(Constants.CA_PRODUCERS_SECOND_THREAD_POOL_WARN_FACTOR, Constants.CA_PRODUCERS_SECOND_THREAD_POOL_WARN_FACTOR_DEFAULT);
        if(maxPoolWarnFactor > 0) {
            float calcValue = (float) schedulerPoolSize / processorCount;
            if (calcValue > maxPoolWarnFactor) {
                Diagnostics.getLogger().warn("Second thread pool size is too high {} for the number of processors {}",
                        schedulerPoolSize, processorCount);
            }
        }

        return schedulerPoolSize;
    }

    public static int getSecondSchedulerPoolQueueSize() {
        int schedulerPoolQueueSize = ConfProperties.getInt(Constants.CA_PRODUCERS_SECOND_THREAD_POOL_QUEUE_SIZE, Constants.CA_PRODUCERS_SECOND_THREAD_QUEUE_SIZE);
        /* 2 per second rate items queued into thread pool */
        if(schedulerPoolQueueSize < Constants.CA_PRODUCERS_SECOND_THREAD_QUEUE_MIN_SIZE) {
            Diagnostics.getLogger().warn("Second thread pool internal queue size configured is too low. Queue size: {}",
                    schedulerPoolQueueSize);
        }

        if(schedulerPoolQueueSize > Constants.CA_PRODUCERS_SECOND_THREAD_QUEUE_MAX_SIZE) {
            Diagnostics.getLogger().warn("Second thread pool internal queue size configured is too high. Queue size: {}",
                    schedulerPoolQueueSize);
        }

        return schedulerPoolQueueSize;
    }

    public static int getForensicsSchedulerPoolSize() {
        int schedulerPoolSize = ConfProperties.getInt(Constants.CA_FORENSICS_THREAD_POOL_SIZE, Constants.CA_FORENSICS_THREAD_POOL_DEFAULT);
        float schedulerPoolFactor = ConfProperties.getFloat(Constants.CA_FORENSICS_THREAD_POOL_FACTOR, Constants.CA_FORENSICS_THREAD_POOL_FACTOR_DEFAULT);
        int processorCount = ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors();

        if(schedulerPoolFactor > 0) {
            // Calculate the maximum thread pool size
            schedulerPoolSize = (int) Math.ceil(ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors() * schedulerPoolFactor);
            //
            Diagnostics.getLogger().info("Forensics thread pool size calculated: {}, number of processors: {}, factor: {}",
                    schedulerPoolSize, ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors(), schedulerPoolFactor);
        }

        // Warn about too high configuration of maximum thread pool count/factor
        float maxPoolWarnFactor = ConfProperties.getFloat(Constants.CA_FORENSICS_THREAD_POOL_WARN_FACTOR, Constants.CA_FORENSICS_THREAD_POOL_WARN_FACTOR_DEFAULT);
        if(maxPoolWarnFactor > 0) {
            float calcValue = (float) schedulerPoolSize / processorCount;
            if (calcValue > maxPoolWarnFactor) {
                Diagnostics.getLogger().warn("Forensics thread pool size is too high {} for the number of processors {}",
                        schedulerPoolSize, processorCount);
            }
        }

        return schedulerPoolSize;
    }

    public static int getForensicsSchedulerPoolQueueSize() {
        int schedulerPoolQueueSize = ConfProperties.getInt(Constants.CA_FORENSICS_THREAD_POOL_QUEUE_SIZE, Constants.CA_FORENSICS_THREAD_QUEUE_SIZE);
        /* 2 per rate items queued into thread pool */
        if(schedulerPoolQueueSize < Constants.CA_FORENSICS_THREAD_QUEUE_MIN_SIZE) {
            Diagnostics.getLogger().warn("Forensics thread pool internal queue size configured is too low. Queue size: {}",
                    schedulerPoolQueueSize);
        }

        if(schedulerPoolQueueSize > Constants.CA_FORENSICS_THREAD_QUEUE_MAX_SIZE) {
            Diagnostics.getLogger().warn("Forensics thread pool internal queue size configured is too high. Queue size: {}",
                    schedulerPoolQueueSize);
        }

        return schedulerPoolQueueSize;
    }

    public static boolean isActiveJDBCConnection(Connection connection, int timeOutInSec, String sqlTestQuery) {
        boolean isExecuted = false;
        PreparedStatement ps = null;
        ResultSet rset = null;
        try {
            ps = connection.prepareStatement(sqlTestQuery);
            ps.setQueryTimeout(timeOutInSec);
            rset = ps.executeQuery();

            if(rset.next()) {
                isExecuted = true;
            }
        } catch (Exception e) {
            log.error("Error occurred while running jdbc test query, connection:{}, timeOutInSec:{}, sqlTestQuery:{}", connection, timeOutInSec, sqlTestQuery);
        } finally {
            try {
                if (rset != null) {
                    rset.close();
                }
            } catch (Exception e) {
                // do nothing
            }
            try {
                if (ps != null) {
                    ps.close();
                }
            } catch (Exception e) {
                // do nothing
            }
        }
        return isExecuted;

    }

    public static Object getKpiValue(KpiDataType dataType, String kpiValue) {
        Object value = null;
        if (dataType.equals(KpiDataType.TEXT)) {
            return kpiValue;
        }

        try {
            value = Double.valueOf(kpiValue.trim());
        } catch (NumberFormatException e) {
            // do nothing
        }
        return value;
    }

    public static Object getDeltaKpiValue(KpiDataType dataType, DataValue oldValue, DataValue newValue, int resetDeltaValue, int deltaPerSec, DataKey dataKey) {
        Object value = null;

        try {

            double deltaValue = Double.parseDouble(newValue.getKpiValue().toString().trim()) - Double.parseDouble(oldValue.getKpiValue().toString().trim());
            if(deltaValue < 0 && resetDeltaValue == 1) {
                log.warn("Resetting the KPI value to new value because of difference is negative, DeltaValue:{}, old:{}, new:{}, dataKey:{}", deltaValue, oldValue.getKpiValue(), newValue.getKpiValue(), dataKey);
                deltaValue = Double.parseDouble(newValue.getKpiValue().toString().trim());
            }
            if(deltaPerSec == 0) {
                return deltaValue;
            }
            int diffInSecs = getDiffInSec(oldValue.getTimeGMT(), newValue.getTimeGMT());
            if(diffInSecs <= 0) {
                log.warn("Diff in seconds:{}, old:{}, new:{}, dataKey:{}", diffInSecs, oldValue.getTimeGMT(), newValue.getTimeGMT(),dataKey);
                return null;
            }
            value = 60 * ((deltaValue) / diffInSecs);
        } catch (NumberFormatException e) {
            log.error("Error occurred while converting the data type:{}, old value:{}, new value:{}, dataKey:{}", dataType, oldValue, newValue, dataKey, e);
        }
        return value;
    }

    public static KPIAgentMessageProtos.KPIAgentMessage.KpiData getNonGroupKpiData(String compInstId, KpiData kpiData) {
        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpi = null;
        DataKey dataKey = new DataKey(compInstId, String.valueOf(kpiData.getId()), "ALL", kpiData.getValueType(), kpiData.getDataType());

        // If error code present or snapshot data points then send to gRPC
        if(KpiValueType.SNAPSHOT.compareTo(kpiData.getValueType()) == 0 ||
                (kpiData.getErrorCode() != null && !kpiData.getErrorCode().trim().isEmpty())) {
            return KPIAgentMessageProtos.KPIAgentMessage.KpiData.newBuilder()
                    .setKpiUid(kpiData.getId())
                    .setTimeInGMT(kpiData.getTimeInGMT())
                    .setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.valueOf(kpiData.getKpiType().getDisplayName()))
                    .setKpiName(kpiData.getKpiName())
                    .setVal(kpiData.getValue())
                    .setErrorCode(kpiData.getErrorCode())
                    .setIsKpiGroup(kpiData.isKpiGroup())
                    .setCollectionInterval(kpiData.getCollectionInterval())
                    .build();
        }

        //If no error code and its a delta then check the previous data point exists
        if(!DataCache.INSTANCE.isDataExists(dataKey)) {
            //No previous point exists then add into cache.
            DataCache.INSTANCE.updateKpiData(compInstId, kpiData, "ALL");
        } else {
            //Get the delta of kpi data point
            String dataPoint = DataCache.INSTANCE.getKpiDataPoint(compInstId, String.valueOf(kpiData.getId()),
                    kpiData.getDataType(), kpiData.getValueType(), kpiData.getKpiType(), "ALL",
                    kpiData.getValue(), kpiData.getTimeInGMT(), kpiData.getResetDelta(), kpiData.getDeltaPerSec());

            //Add current value in cache.
            DataCache.INSTANCE.updateKpiData(compInstId, kpiData, "ALL");

            kpi = KPIAgentMessageProtos.KPIAgentMessage.KpiData.newBuilder()
                    .setKpiUid(kpiData.getId())
                    .setTimeInGMT(kpiData.getTimeInGMT())
                    .setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.valueOf(kpiData.getKpiType().getDisplayName()))
                    .setKpiName(kpiData.getKpiName())
                    .setVal(dataPoint)
                    .setErrorCode(kpiData.getErrorCode())
                    .setIsKpiGroup(kpiData.isKpiGroup())
                    .setCollectionInterval(kpiData.getCollectionInterval())
                    .build();
        }
        return kpi;
    }

    public static KPIAgentMessageProtos.KPIAgentMessage.KpiData getGroupKpiData(String compInstId, KpiData kpiData) {

        //If not group kpi returns null
        if(!kpiData.isKpiGroup()) {
            return null;
        }

        KPIAgentMessageProtos.KPIAgentMessage.KpiData.Builder builder = KPIAgentMessageProtos.KPIAgentMessage.KpiData.newBuilder();

        if(kpiData.getGroupKpiKeyVal() != null && !kpiData.getGroupKpiKeyVal().isEmpty()) {
            builder.setGroupKpi(Commons.buildGroup(kpiData.getGroupKpiKeyVal()));
        }

        if(kpiData.getConfigKpiKeyVal() != null && !kpiData.getConfigKpiKeyVal().isEmpty()) {
            builder.setWatcherKpiValue(Commons.buildConfigKpi(kpiData.getConfigKpiKeyVal()));
        }

        if(kpiData.getErrorCode() != null) {
            builder.setErrorCode(kpiData.getErrorCode());
        }

        // If value type is snapshot return the kpi data
        if(KpiValueType.SNAPSHOT.compareTo(kpiData.getValueType()) == 0 || KpiType.CORE.compareTo(kpiData.getKpiType()) != 0) {
            return builder
                    .setKpiUid(kpiData.getId())
                    .setTimeInGMT(kpiData.getTimeInGMT())
                    .setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.valueOf(kpiData.getKpiType().getDisplayName()))
                    .setKpiName(kpiData.getKpiName())
                    .setIsKpiGroup(kpiData.isKpiGroup())
                    .setCollectionInterval(kpiData.getCollectionInterval())
                    .build();
        }


        Map<String, String> filteredData = new HashMap<>();

        //Loops all the group kpi data
        for (Map.Entry<String, String> entry: kpiData.getGroupKpiKeyVal().entrySet()) {
                DataKey dataKey = new DataKey(compInstId, String.valueOf(kpiData.getId()), entry.getKey(), kpiData.getValueType(), kpiData.getDataType());
                KpiData attributeKpiData = new KpiData(kpiData.getKpiName(), entry.getValue(), entry.getKey(), true, kpiData.getErrorCode(),
                    kpiData.getKpiType(), kpiData.getFileName(), kpiData.getLastUpdated());
                attributeKpiData.setTimeInGMT(kpiData.getTimeInGMT());
                attributeKpiData.setId(kpiData.getId());
                attributeKpiData.setDataType(kpiData.getDataType());
                attributeKpiData.setValueType(kpiData.getValueType());

                //If no error code and its a delta then check the previous data point exists
                if(!DataCache.INSTANCE.isDataExists(dataKey)) {
                    //No previous point exists then add into cache.
                    DataCache.INSTANCE.updateKpiData(compInstId, attributeKpiData, entry.getKey());
                } else {
                    //Get the delta of kpi data point
                    String dataPoint = DataCache.INSTANCE.getKpiDataPoint(compInstId, String.valueOf(kpiData.getId()),
                            kpiData.getDataType(), kpiData.getValueType(), kpiData.getKpiType(), entry.getKey(),
                            entry.getValue(), kpiData.getTimeInGMT(), kpiData.getResetDelta(), kpiData.getDeltaPerSec());

                    //Add current value in cache.
                    DataCache.INSTANCE.updateKpiData(compInstId, attributeKpiData, entry.getKey());
                    filteredData.put(entry.getKey(), dataPoint);
                }
        }

        if(filteredData.isEmpty()) {
            return null;
        }

        return builder
                .setKpiUid(kpiData.getId())
                .setTimeInGMT(kpiData.getTimeInGMT())
                .setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.valueOf(kpiData.getKpiType().getDisplayName()))
                .setKpiName(kpiData.getKpiName())
                .setKpiGroupName(kpiData.getKpiGroupName())
                .setIsKpiGroup(kpiData.isKpiGroup())
                .setGroupKpi(buildGroup(filteredData))
                .setCollectionInterval(kpiData.getCollectionInterval())
                .build();
    }

    /**
     *
     * @param map, used for building group kpi key value pair.
     * @return
     * Build group kpi for MessageProtos
     */
    public static KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi buildGroup(Map<String, String> map){
        return KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.newBuilder()
                .putAllPairs(map)
                .build();
    }

    /**
     *
     * @param mapOfMap, used for building watcher kpi value.
     * @return
     * Build watcher kpi for MessageProtos
     */
    public static KPIAgentMessageProtos.KPIAgentMessage.KpiData.WatcherKpiValue buildConfigKpi(Map<String, Map<String, String>> mapOfMap){
        Map<String, KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi> groupKpiMap = new HashMap<>();
        KPIAgentMessageProtos.KPIAgentMessage.KpiData.WatcherKpiValue watcherKpiValue;
        for(Map.Entry<String, Map<String, String>> entry : mapOfMap.entrySet()){
            groupKpiMap.put(entry.getKey(), buildGroup(entry.getValue()));
        }
        watcherKpiValue = KPIAgentMessageProtos.KPIAgentMessage.KpiData.WatcherKpiValue.newBuilder()
                .putAllKeyValuePair(groupKpiMap)
                .build();
        return watcherKpiValue;
    }

    private static int getDiffInSec(String startDate, String endDate) {
        final DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        int diffInSeconds = 0;
        try {

            // parse method is used to parse
            // the text from a string to
            // produce the date
            Date d1 = dateFormat.parse(startDate);
            Date d2 = dateFormat.parse(endDate);

            // Calculate time difference
            // in milliseconds
            long differenceInTime
                    = d2.getTime() - d1.getTime();

            // Calculate time difference in
            // seconds, minutes, hours, years,
            // and days
            diffInSeconds
                    = (int) (differenceInTime
                                        / 1000);
        } catch (ParseException e) {
            log.error("Error while calculating difference between times. Start:{}, end:{}", startDate, endDate, e);
        }
        return diffInSeconds;
    }

    public static void pushForensicData(ForensicOutputDetails outputDetails, InstanceDetails instanceDetails, ThresholdDetails thresholdDetails, KpiData kpiData,
                                        long forensicStartTime, long forensicEndTime, ForensicDetails forensicDetails, String eventDetectedTime) {


        CategoryDetails categoryDetails = ForensicCache.INSTANCE.getCategoryDetails(kpiData.getKpiName());
        Map<String, String> metaData = new HashMap<>(forensicDetails.getArguments());
        metaData.put("ComponentVersion", instanceDetails.getComponentVersion());
        metaData.put("ComponentType", instanceDetails.getComponentType());
        metaData.put("ComponentName", instanceDetails.getComponentName());
        metaData.put("InstanceIdentifier", instanceDetails.getIdentifier());
        metaData.put("AccountId", ComponentAgentCache.getInstance().getAgentBean().getAccountDetails().get(0).getIdentifier());
        metaData.put("ActionId", forensicDetails.getIdentifier());
        metaData.put("ActionTriggerTime", forensicStartTime+"");
        metaData.put("starttime", forensicStartTime+"");
        metaData.put("CategoryId", categoryDetails == null ? "": categoryDetails.getCategoryIdentifier());
        metaData.put("CategoryName", categoryDetails == null ? "": categoryDetails.getCategoryName());
        metaData.put("Event_Detected_Time", eventDetectedTime);
        metaData.put("Forensic_Name", forensicDetails.getName());
        metaData.put("InstanceId", instanceDetails.getIdentifier());
        metaData.put("InstanceName", instanceDetails.getName());
        metaData.put("KPIAttribute", kpiData.getKpiAttributeName());
        metaData.put("KPIGroupName", kpiData.getKpiGroupName());
        metaData.put("KPIIdentifier", kpiData.getKpiName());
        metaData.put("KPIId", kpiData.getId()+"");
        metaData.put("KPIValue", kpiData.getValue());
        metaData.put("Lower", thresholdDetails.getMinThreshold()+"");
        metaData.put("Upper", thresholdDetails.getMaxThreshold()+"");
        metaData.put("ProducerType", forensicDetails.getProducerType().name());
        metaData.put("Operation", thresholdDetails.getOperation());
        metaData.put("Severity", thresholdDetails.getSeverity());
        metaData.put("ip_address", instanceDetails.getHostAddress());
        metaData.put("kpiType", kpiData.getKpiType().name());
        metaData.put("persistence", thresholdDetails.getPersistence()+"");
        metaData.put("suppression", thresholdDetails.getSuppression()+"");
        metaData.put("ThresholdType", thresholdDetails.getThresholdType().name());
        metaData.put("violationLevel", thresholdDetails.getThresholdLevel());
        ForensicCache.INSTANCE.addForensicResponse(ForensicResponse.builder()
                        .cmdOut(compressNEncodeString(outputDetails.getOutput()))
                        .exitCode(outputDetails.getExitCode())
                        .stdErr(compressNEncodeString(outputDetails.getErrOutput()))
                        .scriptName(forensicDetails.getScriptName())
                        .commandType("ForensicCmds")
                        .agentType("ComponentAgent")
                        .triggerSource("ComponentAgent")
                        .agentIdentifier(ComponentAgentCache.getInstance().getComponentAgentDetails().getAgent().getIdentifier())
                        .metaData(metaData)
                        .commandEndTime(forensicEndTime)
                        .commandStartTime(forensicStartTime)
                .build());
    }

    private static String compressNEncodeString(String input) {
        if(input == null || input.trim().isEmpty()) {
            return input;
        }

        try {
            ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
            try (GZIPOutputStream gzipStream = new GZIPOutputStream(byteStream)) {
                gzipStream.write(input.getBytes(StandardCharsets.UTF_8));
            }
            return java.util.Base64.getEncoder().encodeToString(byteStream.toByteArray());
        } catch (Exception e) {
            log.error("Error occurred while doing the compression and encode on forensic output. Forensic output:{}", input, e);
            return input;
        }
    }

    /**
     *
     * @param compInstId, component instance id.
     * @param kpiDataList, list of kpi data.
     */
    public static void pushKpiData(String compInstId, List<KpiData> kpiDataList) {
        if(kpiDataList == null) {
            return ;
        }
        for(KpiData kpiData: kpiDataList) {
            KPIAgentMessageProtos.KPIAgentMessage.KpiData kpi;
            if(kpiData.isKpiGroup()){
                if(kpiData.getKpiType().toString().equals(KpiType.CONFIG_WATCH.toString()) ||
                        kpiData.getKpiType().toString().equals(KpiType.FILE_WATCH.toString())) {
                    kpi = KPIAgentMessageProtos.KPIAgentMessage.KpiData.newBuilder()
                            .setKpiUid(kpiData.getId())
                            .setTimeInGMT(kpiData.getTimeInGMT())
                            .setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.valueOf(kpiData.getKpiType().getDisplayName()))
                            .setKpiName(kpiData.getKpiName())
                            .setKpiGroupName(kpiData.getKpiGroupName())
                            .setWatcherKpiValue(Commons.buildConfigKpi(kpiData.getConfigKpiKeyVal()))
                            .setErrorCode(kpiData.getErrorCode())
                            .setIsKpiGroup(kpiData.isKpiGroup())
                            .setCollectionInterval(kpiData.getCollectionInterval())
                            .build();
                } else {
                    // In case of Availability, Forensics and Core group kpis
                    kpi = Commons.getGroupKpiData(compInstId, kpiData);
                }
            } else {
                // In case of Availability or Core for non group kpis
                kpi = Commons.getNonGroupKpiData(compInstId, kpiData);
            }

            if(kpi != null) {
                log.trace("Pushing component instance id: {}, Kpi data: {} into queue.", compInstId, kpiData);
                ForensicCache.INSTANCE.processToForensics(compInstId, kpi);
                DataQueue.getInstance().enqueue(compInstId, kpi);
            }
        }
    }

    public static int getThresholdForExecTime(int thresholdInSecs) {
        if(thresholdInSecs > 0) {
            return 1000 * thresholdInSecs;
        }
        return execThreshold;
    }

    public static String getPipeCreationTime(Path file) {
        try {
            BasicFileAttributes attr = Files.readAttributes(file, BasicFileAttributes.class);
            Instant roundedCreationTime = attr.creationTime().toInstant().truncatedTo(ChronoUnit.SECONDS);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return roundedCreationTime.atZone(ZoneId.systemDefault()).format(formatter);
        } catch (IOException e) {
            log.error("Error occurred while retrieving pipe creation time for the named pipe {}", file.getFileName(), e);
            return null;
        }
    }

    public static void writeIntoAckPipe(String namedPipe, NetAgentAckData ackData) {
        try {
            boolean isPipeExists = CommonUtil.isNamedPipeExists(namedPipe);
            if (!isPipeExists) {
                log.info("Named pipe does not exists for writing, pipe:{}.", namedPipe);
                return;
            }

            EndianParser parser = EndianParserFactory.getParser();

            try (FileChannel fileChannel = FileChannel.open(Paths.get(namedPipe), StandardOpenOption.WRITE)) {
                if (ackData == null) {
                    return;
                }

                byte[] ackBuffer = new byte[NetAgentMessageHeader.HEADER_SIZE + NetAgentMessageHeader.START_BYTES_COUNT];
                ackBuffer[0] = NetAgentProcessor.START_BYTE1;
                ackBuffer[1] = NetAgentProcessor.START_BYTE2;
                ackBuffer[2] = 1;
                ackBuffer[3] = 0;
                ackBuffer[4] = ackData.getMsgFlag();
                ackBuffer[5] = ackData.getType();

                byte[] ackBufferWithSeqNo = parser.addIntToBuffer(ackBuffer, ackData.getSeqNo(), 6);

                // No data, set message len to 0
                ackBufferWithSeqNo[10] = 0;
                ackBufferWithSeqNo[11] = 0;
                ackBufferWithSeqNo[12] = 0;
                ackBufferWithSeqNo[13] = 0;

                ByteBuffer ack = ByteBuffer.wrap(ackBufferWithSeqNo);

                int writtenBytes = fileChannel.write(ack);
                log.debug("Data written into pipe:{}, writtenBytes:{}, seqNum:{}, flag:{}, type:{}.", namedPipe, writtenBytes, ackData.getSeqNo(), ackData.getMsgFlag(), ackData.getType());
            } catch (Exception e) {
                log.error("Error occurred while writing data into named pipe:{}", namedPipe, e);
            }

            log.debug("Completed the named pipe writer.");
        } catch (Throwable e) {
            log.error("Error occurred in runOneIteration method.", e);
        }
    }
}
