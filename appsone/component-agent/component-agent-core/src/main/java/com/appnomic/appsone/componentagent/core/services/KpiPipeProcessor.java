package com.appnomic.appsone.componentagent.core.services;

import com.appnomic.appsone.common.util.ConfProperties;
import com.appnomic.appsone.componentagent.common.beans.NetAgentAckData;
import com.appnomic.appsone.componentagent.common.beans.NetAgentData;
import com.appnomic.appsone.componentagent.common.beans.NetAgentMessageHeader;
import com.appnomic.appsone.componentagent.common.util.CommonUtil;
import com.appnomic.appsone.componentagent.common.util.NetAgentProcessor;
import com.appnomic.appsone.componentagent.core.Constants;
import com.appnomic.appsone.componentagent.core.cache.ComponentAgentCache;
import com.appnomic.appsone.componentagent.core.util.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.util.concurrent.AbstractIdleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.Buffer;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static com.appnomic.appsone.componentagent.core.util.Commons.getPipeCreationTime;
import static com.appnomic.appsone.componentagent.core.util.Commons.writeIntoAckPipe;

public class KpiPipeProcessor extends AbstractIdleService {
    private static final Logger log = LoggerFactory.getLogger(KpiPipeProcessor.class);
    private NetAgentProcessor kpiNetAgentProcessor;
    private FileChannel fileChannel;
    private DateTimeFormatter inputFormatter, outputFormatter;
    private String readNamedPipe;
    private String writeNamedPipe;
    private boolean infiniteValue = true;
    private String kpiPipeCreationTime;
    private int logSuppressDurationInMinutes;

    @Override
    protected void startUp() {
        try {
            log.debug("Service {} has started", KpiPipeProcessor.class.getSimpleName());
            kpiNetAgentProcessor = new NetAgentProcessor();
            inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:00");
            readNamedPipe = ConfProperties.getString(Constants.NAMED_PIPE_PATH_READ, Constants.NAMED_PIPE_PATH_READ_DEFAULT);
            writeNamedPipe = ConfProperties.getString(Constants.NAMED_PIPE_PATH_WRITE, Constants.NAMED_PIPE_PATH_WRITE_DEFAULT);
            long readSleepInterval = ConfProperties.getLong(Constants.NAMED_PIPE_PATH_READ_SLEEP_INTERVAL, Constants.NAMED_PIPE_PATH_READ_SLEEP_INTERVAL_DEFAULT);
            logSuppressDurationInMinutes = ConfProperties.getInt(Constants.NAMED_PIPE_PATH_READ_ERROR_LOG_INTERVAL, Constants.NAMED_PIPE_PATH_READ_ERROR_LOG_INTERVAL_DEFAULT);

            long lastLogTime = 0;
            boolean checkPipeExistence = true;
            while (checkPipeExistence) {
                if (!CommonUtil.isNamedPipeExists(readNamedPipe)) {
                    long currentTime = System.currentTimeMillis();
                    if ((currentTime - lastLogTime) >= (logSuppressDurationInMinutes * 60000L)) {
                        log.error("Named pipe does not exist for reading, pipe: {}", readNamedPipe);
                        lastLogTime = currentTime;
                    }
                    Thread.sleep(readSleepInterval * 1000);
                } else {
                    log.info("Named pipe exist for reading data. Pipe:{}", readNamedPipe);
                    Path file = Paths.get(readNamedPipe);
                    String pipeCreationTime = getPipeCreationTime(file); // Retrieving the creation time of named pipe to monitor the creation of new pipe in the future
                    log.debug("Named pipe {} created on {}", file.getFileName(), pipeCreationTime);
                    if (pipeCreationTime == null) {
                        log.warn("Pipe creation time obtained is NULL of the pipe {}", file.getFileName());
                        continue;
                    }
                    kpiPipeCreationTime = pipeCreationTime;
                    checkPipeExistence = false;
                    lastLogTime = 0;
                }
            }

            log.debug("Attempting to open FileChannel for named pipe '{}' with READ operation in startUp function of class {}. Ensuring the pipe exists and is accessible for data reading.", KpiPipeProcessor.class.getSimpleName(), readNamedPipe);
            fileChannel = FileChannel.open(Paths.get(readNamedPipe), StandardOpenOption.READ);

            log.trace("Calling processKpiPipe()");
            processKpiPipe(fileChannel, readSleepInterval);
        } catch (Exception e) {
            log.error("Error occurred in startUp method of KPIPipeProcessor.", e);
        }
    }

    @Override
    protected void shutDown() throws IOException {
        fileChannel.close();
        infiniteValue = false;
        log.info("Service {} has shutdown", KpiPipeProcessor.class.getSimpleName());
    }

    protected void processKpiPipe(FileChannel fileChannel, long sleepInterval) {
        log.trace("Inside run() of class {}", KpiPipeProcessor.class.getSimpleName());
        long lastLogTime = 0;
        while (infiniteValue) {
            try {
                if (ComponentAgentCache.getInstance().getAgentBean() == null) {
                    log.debug("Agent details are not loaded. Reading from pipe:{}, expectedMessageType:{} will be skipped.", readNamedPipe, NetAgentProcessor.MSG_TYPE_KPI);
                    Thread.sleep(sleepInterval * 1000);
                    continue;
                }

                if (!CommonUtil.isNamedPipeExists(readNamedPipe)) {
                    long currentTime = System.currentTimeMillis();
                    if ((currentTime - lastLogTime) >= (logSuppressDurationInMinutes * 60000L)) {
                        log.error("Named pipe does not exist for reading, pipe: {}", readNamedPipe);
                        lastLogTime = currentTime;
                    }
                    Thread.sleep(sleepInterval * 1000);
                    continue;
                }

                // This check monitors the creation time of the named pipe,
                // To detect if a pipe with the same name has been deleted and recreated.
                Path pipe = Paths.get(readNamedPipe);
                String fileName = String.valueOf(pipe.getFileName());

                long currentTime = System.currentTimeMillis();
                if ((currentTime - lastLogTime) >= (logSuppressDurationInMinutes * 60000L)) {
                    log.trace("Getting the pipe creation time for pipe {}", fileName);
                    lastLogTime = currentTime;
                }

                String newPipeCreationTime = getPipeCreationTime(pipe);
                if(!kpiPipeCreationTime.equals(newPipeCreationTime)) {
                    log.info("New named pipe creation detected, Closing the existing file channel. Details:- Pipe {}, Old creation time {}, New creation time {}", fileName, kpiPipeCreationTime, newPipeCreationTime);
                    fileChannel.close();
                    fileChannel = FileChannel.open(pipe, StandardOpenOption.READ); //Reopening new file channel
                    kpiPipeCreationTime = newPipeCreationTime; //Replacing the old creation time with new creation time.
                }

                boolean isDataAvailable = true;
                while (isDataAvailable) {
                    ByteBuffer byteBuffer = ByteBuffer.allocate(1024);
                    int read = fileChannel.read(byteBuffer);
                    if (read > 0) {
                        log.trace("Inside the try-catch block, more than zero bytes read from pipe:{}.", readNamedPipe);
                        ((Buffer) byteBuffer).flip();
                        byte[] bytes = new byte[byteBuffer.remaining()];
                        byteBuffer.get(bytes);
                        kpiNetAgentProcessor.parse(bytes, bytes.length);

                        if (kpiNetAgentProcessor.found()) {
                            NetAgentMessageHeader header = kpiNetAgentProcessor.getHeader();
                            log.debug("Header details obtained from the pipe {}. Details: Flags: {}, Type: {}, version: {}, Sequence number: {}", readNamedPipe, header.flags, header.type, header.version, header.seqNo);
                            String message = kpiNetAgentProcessor.getMessage();
                            kpiNetAgentProcessor.reset();

                            if (header.flags != NetAgentProcessor.MSG_FLAGS_RES) {
                                log.warn("Invalid flag type received from pipe:{}, header:{}, message:{}", readNamedPipe, header, message);
                                return;
                            }

                            NetAgentAckData ackData = NetAgentAckData.builder()
                                    .seqNo(header.seqNo)
                                    .msgFlag(NetAgentProcessor.MSG_FLAGS_ACK)
                                    .type(NetAgentProcessor.MSG_TYPE_KPI)
                                    .build();

                            if (header.type == NetAgentProcessor.MSG_TYPE_MIN_MODE_ON) {
                                ackData.setType(NetAgentProcessor.MSG_TYPE_MIN_MODE_ON);
                                String minimalModePriorities = ConfProperties.getString(Constants.MINIMAL_MODE_PRIORITIES, Constants.MINIMAL_MODE_PRIORITIES_DEFAULT);
                                ComponentAgentCache.getInstance().setCollectionMode(minimalModePriorities);
                                log.info("Minimal mode is enabled. Configuring Component-Agent (CA) to run in minimal mode. Writing Ack message to the named pipe {}", writeNamedPipe);
                                writeIntoAckPipe(writeNamedPipe, ackData);
                                continue;
                            } else if (header.type == NetAgentProcessor.MSG_TYPE_MIN_MODE_OFF) {
                                ackData.setType(NetAgentProcessor.MSG_TYPE_MIN_MODE_OFF);
                                ComponentAgentCache.getInstance().setCollectionMode("ALL");
                                log.info("Minimal mode is disabled. Configuring Component-Agent (CA) to resume normal operation. Writing Ack message to the named pipe {}", writeNamedPipe);
                                writeIntoAckPipe(writeNamedPipe, ackData);
                                continue;
                            }

                            log.debug("Writing Ack message to the named pipe {}", writeNamedPipe);
                            writeIntoAckPipe(writeNamedPipe, ackData);

                            log.trace("Processing the data received from the pipe {}", readNamedPipe);
                            //TODO: based on type we should have a implementation logic, here by default we are implementing the net agent data processing.
                            processKpiData(message, readNamedPipe);
                        } else {
                            log.debug("Incomplete data read from named pipe '{}'. Awaiting further data until the complete message is received. Current pipe state: [PipeName: {}, Timestamp: {}, Retrying...]", readNamedPipe, readNamedPipe, System.currentTimeMillis());
                        }
                        ((Buffer) byteBuffer).clear();
                    } else {
                        if ((currentTime - lastLogTime) >= (logSuppressDurationInMinutes * 60000L)) {
                            log.debug("No data to read from pipe:{}", readNamedPipe);
                            lastLogTime = currentTime;
                        }
                        isDataAvailable = false;
                    }
                }
            } catch (InterruptedException e) {
                log.error("Error occurred while reading kpi pipe data.Pipe:{}, expectedMessageType:{}", readNamedPipe, NetAgentProcessor.MSG_TYPE_KPI, e);
            } catch (IOException e) {
                log.error("Error occurred while performing IO operations on the pipe {}", readNamedPipe, e);
            }
        }
    }

    public void processKpiData(String message, String namedPipe) {
        try {
            NetAgentData netAgentData = JSONUtil.fromJSON(message, new TypeReference<NetAgentData>() {
            });

            boolean isValid = validateNetAgent(ComponentAgentCache.getInstance().getAgentBean().getIdentifier(), netAgentData);
            if (!isValid) {
                return;
            }

            if (netAgentData.getServers() == null || netAgentData.getServers().isEmpty()) {
                log.info("Received empty server data pipe:{}, data:{}", namedPipe, netAgentData);
            } else {
                log.trace("Net agent data read from named pipe:{}, data:{}", namedPipe, netAgentData);
            }

            netAgentData.getServers().forEach(map -> {
                try {
                    if (!map.containsKey("serverIp")) {
                        return;
                    }
                    if (!map.containsKey("serverPort")) {
                        return;
                    }

                    String serverIp = (String) map.get("serverIp");
                    int serverPort = Integer.parseInt(map.get("serverPort") + "");
                    String timeInGMT = convertToZeroSeconds(netAgentData.getTime(), inputFormatter, outputFormatter);

                    ComponentAgentCache.getInstance().addNetAgentKpiData(serverIp, serverPort, timeInGMT, map);
                    log.info("Net agent data identified for serverIp:{}, port:{}, time:{}", serverIp, serverPort, timeInGMT);
                } catch (Exception e) {
                    log.error("Error occurred while processing the net agent data:{}, pipe:{}", map, namedPipe, e);
                }
            });
        } catch (Exception e) {
            log.error("Error occurred while kpi data processing from pipe:{}, data:{}", message, namedPipe, e);
        }
    }

    public boolean validateNetAgent(String agentIdentifier, NetAgentData netAgentData) {
        try {
            if (netAgentData == null) {
                log.error("Net agent data object is null.");
                return false;
            }
            if (netAgentData.getAgentId() == null || netAgentData.getAgentId().trim().isEmpty()) {
                log.error("Net agent data has null/empty agent id.");
                return false;
            }

            if (agentIdentifier == null || agentIdentifier.trim().isEmpty()) {
                log.error("Component agent identifier has null/empty agent id.");
                return false;
            }

            if (netAgentData.getTime() == null || netAgentData.getTime().trim().isEmpty()) {
                log.error("Net agent data has null/empty time.");
                return false;
            }

            if (!agentIdentifier.equalsIgnoreCase(netAgentData.getAgentId())) {
                log.error("Component agent identifier:{}, net agent identifier:{}.", agentIdentifier, netAgentData.getAgentId());
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("Error occurred while validating the agentIdentifier:{},net agent data:{}", agentIdentifier, netAgentData, e);
            return false;
        }
    }

    public String convertToZeroSeconds(String timestamp, DateTimeFormatter inputFormatter, DateTimeFormatter outputFormatter) {

        // Parse the original timestamp
        LocalDateTime dateTime = LocalDateTime.parse(timestamp, inputFormatter);

        // Format the timestamp to ensure seconds are zero
        return dateTime.format(outputFormatter);
    }
}
