package com.appnomic.appsone.componentagent.core.beans;

import com.appnomic.appsone.common.util.ConfProperties;
import com.appnomic.appsone.common.util.ResourceBundleUtils;
import com.appnomic.appsone.componentagent.common.ComponentAgentException;
import com.appnomic.appsone.componentagent.common.beans.*;
import com.appnomic.appsone.componentagent.common.beans.Constants;
import com.appnomic.appsone.componentagent.common.enums.ErrorCodes;
import com.appnomic.appsone.componentagent.common.intf.ICollectionBase;
import com.appnomic.appsone.componentagent.common.util.CommonUtil;
import com.appnomic.appsone.componentagent.common.util.DateUtil;
import com.appnomic.appsone.componentagent.core.cache.ComponentAgentCache;
import com.appnomic.appsone.componentagent.core.cache.DataCollectionTask;
import com.appnomic.appsone.componentagent.core.util.Commons;
import com.appnomic.appsone.componentagent.core.util.ThreadPool;
import com.appnomic.appsone.componentagent.httpd.HttpStatusProducer;
import com.appnomic.appsone.componentagent.httpjson.HttpJsonProducer;
import com.appnomic.appsone.componentagent.jdbc.JDBCProducer;
import com.appnomic.appsone.componentagent.jmx.JmxProducer;
import com.appnomic.appsone.componentagent.jppf.JPPFProducer;
import com.appnomic.appsone.componentagent.coreproducer.CoreProducer;
import com.appnomic.appsone.componentagent.netagent.NetAgentProducer;
import com.appnomic.appsone.componentagent.shell.ShellProducer;
import com.appnomic.appsone.componentagent.ssh.JschInstance;
import com.appnomic.appsone.componentagent.ssh.SSHProducer;
import com.appnomic.appsone.componentagent.was.WebSphereProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.*;

/**
 * Created by prasad on 11/5/16.
 */
public class ComponentAgentScheduler {
    private static final Logger log = LoggerFactory.getLogger(ComponentAgentScheduler.class);
    private final int execThreshold = 1000 * ConfProperties.getInt(com.appnomic.appsone.componentagent.core.Constants.CA_PRODUCERS_LONG_RUNNING_EXEC_TIME_THRESHOLD, com.appnomic.appsone.componentagent.core.Constants.CA_PRODUCERS_LONG_RUNNING_EXEC_TIME_THRESHOLD_DEFAULT);
    private final int channelsPerSession = ConfProperties.getInt(com.appnomic.appsone.componentagent.core.Constants.CA_PRODUCERS_SSH_MAX_CHANNELS_PER_SESSION,
            com.appnomic.appsone.componentagent.core.Constants.CA_PRODUCERS_SSH_MAX_CHANNELS_PER_SESSION_DEFAULT);
    private final int timeWaitAcquireChannel = ConfProperties.getInt(com.appnomic.appsone.componentagent.core.Constants.CA_PRODUCERS_SSH_CHANNEL_WAIT_TIME,
            com.appnomic.appsone.componentagent.core.Constants.CA_PRODUCERS_SSH_CHANNEL_WAIT_TIME_DEFAULT);
    private final int signingCheck = ConfProperties.getInt(com.appnomic.appsone.componentagent.core.Constants.CA_PRODUCERS_SIGNING_CHECK,
            com.appnomic.appsone.componentagent.core.Constants.CA_PRODUCERS_SIGNING_CHECK_DEFAULT);
    private final String sslVerions =  ConfProperties.getString(Constants.CA_CONFIG_SSL_VERSIONS,Constants.CA_CONFIG_SSL_VERSIONS_DEFAULT);
    private final String sqlTestQuery =  ConfProperties.getString(com.appnomic.appsone.componentagent.core.Constants.CA_JDBC_TEST_QUERY,
            com.appnomic.appsone.componentagent.core.Constants.CA_JDBC_TEST_QUERY_DEFAULT);
    private final int sqlTestQueryValidation =  ConfProperties.getInt(com.appnomic.appsone.componentagent.core.Constants.CA_JDBC_TEST_QUERY_VALIDATION,
            com.appnomic.appsone.componentagent.core.Constants.CA_JDBC_TEST_QUERY_VALIDATION_DEFAULT);
    private final int sqlTestQueryTimeoutInSecs =  ConfProperties.getInt(com.appnomic.appsone.componentagent.core.Constants.CA_JDBC_TEST_QUERY_TIMEOUT_SECS,
            com.appnomic.appsone.componentagent.core.Constants.CA_JDBC_TEST_QUERY_TIMEOUT_SECS_DEFAULT);

    private final String shellExecuteType = ConfProperties.getString(Constants.CA_SHELL_PRODUCER_EXECUTE_TYPE);
    private final int enableScheduleTimeDC = ConfProperties.getInt(com.appnomic.appsone.componentagent.core.Constants.CA_SCHEDULE_TIME_DC, com.appnomic.appsone.componentagent.core.Constants.CA_SCHEDULE_TIME_DC_DEFAULT);
    private final int netAgentDelayInMins = ConfProperties.getInt(com.appnomic.appsone.componentagent.core.Constants.NET_AGENT_DATA_DELAY_IN_MINS, com.appnomic.appsone.componentagent.core.Constants.NET_AGENT_DATA_DELAY_IN_MINS_DEFAULT);
    long bufferIntervalTimeInMill =  ConfProperties.getInt(Constants.CA_BUFFER_COLLECTION_INTERVAL,Constants.CA_BUFFER_COLLECTION_INTERVAL_DEFAULT) * 1000L;

    public void scheduleProducerInstances(Set<ComponentInstance> componentInstances, CollectionInterval interval) {

        ComponentAgentCache cache = ComponentAgentCache.getInstance();
        long currentTime = System.currentTimeMillis();
        String scheduleTime = enableScheduleTimeDC == 1 ? DateUtil.getCurrentGMTTime() : null;
        log.debug("Signature validation flag is {}.", signingCheck);
        //loop all the component instances for minute schedule
        for (ComponentInstance instance : componentInstances) {
            log.trace("Component instance '{}'", instance.getCompInstName());

            //Only active component instances have kpi data collection, otherwise continue to next instance
            if (!Commons.isActive(instance.getStatus())) {
                log.warn("Component instance '{}' is in-active, hence data will not be collected.", instance.getCompInstName());
                continue;
            }

            Map<Integer, List<Producer>> producerMap;
            if (interval.compareTo(CollectionInterval.MINUTE) == 0)
                producerMap = instance.getMinuteIntervalCollections();
            else
                producerMap = instance.getCollections();

            if (producerMap == null || producerMap.isEmpty()) {
                log.warn("Producers does not exist for component instance {} with collection interval {}.", instance.getCompInstName(), interval);
                continue;
            }

            for (Map.Entry<Integer, List<Producer>> map : producerMap.entrySet()) {
                log.debug("Interval:{}, No. of producer instances:{} for component instance:{}", map.getKey(), map.getValue().size(), instance.getCompInstName());

                ThreadPool.INSTANCE.secondExecutor.execute(() ->{

                    for (Producer producer : map.getValue()) {
                        log.debug("Producer:{}, component instance:{}, interval:{}", producer, instance.getCompInstName(), map.getKey());

                        String hashCode = Commons.getHashCode(producer.getProducerId(), instance.getCompInstId(), map.getKey());

                        //Only active produces have scheduled for kpi data collection, otherwise continue to next producer
                        if (!Commons.isActive(producer.getStatus())) {
                            log.warn("Producer {} for component instance {} is in-active, hence data will not be collected.",
                                    producer.getName(), instance.getCompInstName());
                            continue;
                        }

                        //is eligible for schedule change the name of method. continue for next producer
                        log.debug("Producer {} for component instance {} - Collection interval: {}, Last run time: {}, current time: {}", producer.getName(), instance.getCompInstName(), map.getKey(),
                                cache.prodLastRunTime.get(hashCode), currentTime);

                        // Based on collection interval checking the execution eligability
                        if (!Commons.isEligibleForScheduling(map.getKey(), cache.prodLastRunTime.get(hashCode), currentTime+bufferIntervalTimeInMill)) {
                            log.debug("Producer {} for component instance {} is not eligible for current iteration({} seconds interval).",
                                    producer.getName(), instance.getCompInstName(), map.getKey());
                            continue;
                        }

                        try {
                            String cronExpStr = Commons.getCronExpression(producer.getKpiDetails());
                            // Based on cron expression checking the execution eligibility
                            if(!Commons.isEligibleForScheduling(cronExpStr, Calendar.getInstance(TimeZone.getTimeZone("IST")).getTime(), producer.getName(), instance.getCompInstName())) {
                                log.debug("Producer {} for component instance {} is not eligible for current iteration({} seconds interval). Cron expression:{}",
                                        producer.getName(), instance.getCompInstName(), map.getKey(), cronExpStr);
                                continue;
                            }

                            if(!Commons.isProducerEligibleToExecute(producer, ComponentAgentCache.getInstance().getCollectionMode(), instance.getCompInstName())) {
                                log.info("Producer {} for component instance {} is not eligible. execution mode:{}",
                                        producer.getName(), instance.getCompInstName(), ComponentAgentCache.getInstance().getCollectionMode());
                                String errorMsg = ResourceBundleUtils.getString(ErrorCodes.SELF_HEAL_MODE.getCode(), producer.getName(), instance.getCompInstName());
                                List<KpiData> kpiData = CommonUtil.getKpiDataByErrorCodes(producer.getKpiDetails(), errorMsg, scheduleTime);
                                Commons.pushKpiData(instance.getCompInstId(), kpiData);
                                continue;
                            }

                            ICollectionBase prodInst = ComponentAgentCache.getInstance().getProducerInstance(hashCode);
                            log.trace("Key: {}, Get producer instance from cache {}",hashCode , prodInst);
                            if (prodInst == null) {

                                //Order all the parameters based on order number.
                                List<Parameter> parameters = null;
                                if(producer.getTypeDetails().getParameters() != null) {
                                    parameters = producer.getTypeDetails().getParameters();
                                    Collections.sort(parameters);
                                }

                                prodInst = Commons.instantiate(producer.getClassName(), instance.getAttributes(), parameters,
                                        producer.getKpiDetails(), producer.getName(), instance.getCompInstName());

                                if (prodInst instanceof SSHProducer) {
                                    SSHProducer sshProducer = (SSHProducer) prodInst;
                                    SSHDetails sshDetails = (SSHDetails) producer.getTypeDetails();
                                    if(signingCheck == 0){
                                        log.info("Skipping signature verification for '{}' file.", sshDetails.getScriptName());
                                    }else if(signingCheck == 1 && !Commons.isValidSign(producer.getAccountId(),
                                            sshDetails.getScriptName(), sshDetails.getSignature(), false)) {
                                        String errorMsg = ResourceBundleUtils.getString(ErrorCodes.SIGNATURE_VERIFICATION_FAILED.getCode(),
                                                sshDetails.getScriptName(), producer.getName(), instance.getCompInstName(), ErrorCodes.SIGNATURE_VERIFICATION_FAILED.getCode());
                                        log.error(errorMsg);
                                        List<KpiData> kpiData = CommonUtil.getKpiDataByErrorCodes(producer.getKpiDetails(), errorMsg, scheduleTime);
                                        Commons.pushKpiData(instance.getCompInstId(), kpiData);
                                        continue;
                                    } else {
                                        log.debug("Signature verification success for '{}' file.", sshDetails.getScriptName());
                                    }
                                    sshProducer.setScriptName(sshDetails.getScriptName());
                                    sshProducer.setScriptContent(Commons.getFileContentAndPutIntoCache(sshDetails.getScriptName()));
                                    sshProducer.setEnvironmentValue(producer.getEnvData());
                                    sshProducer.setCollectionInterval(map.getKey());
                                    sshProducer.setLastTimeCollected(cache.prodLastRunTime.get(hashCode) != null);

                                    String collectorSessionKey = instance.getCollectorSessionKey(producer.getProducerType().name());
                                    JschInstance jschInstance = (JschInstance) ComponentAgentCache.getInstance().
                                            getCollectorSession(collectorSessionKey);
                                    log.trace("Key:{}, Get jsch instance from cache {}", collectorSessionKey, hashCode);
                                    log.info("Get host details from cache");
                                    Map<String, String> hostDetails = cache.getHostDetails().get(instance.getStringValue(Constants.HOST_ADDRESS));
                                    String hostAddress, hostPort, hostUserName, hostPassword;
                                    if(hostDetails != null && !hostDetails.isEmpty()){
                                        hostAddress = hostDetails.get(Constants.HOST_ADDRESS);
                                        hostPort = hostDetails.get(Constants.SSH_PORT);
                                        hostUserName = hostDetails.get(Constants.HOST_USER_NAME);
                                        hostPassword = Commons.getAttributeValue(Constants.PASSWORD_TYPE, hostDetails.get(Constants.HOST_PASSWORD));
                                    }else {
                                        hostAddress = instance.getStringValue(Constants.HOST_ADDRESS);
                                        hostPort = instance.getStringValue(Constants.SSH_PORT);
                                        hostUserName = instance.getStringValue(Constants.HOST_USER_NAME);
                                        hostPassword = instance.getStringValue(Constants.HOST_PASSWORD);
                                    }

                                    if(hostPort == null || hostPort.trim().isEmpty()) {
                                        String errorMsg = ResourceBundleUtils.getString(ErrorCodes.SSH_PORT_IS_MISSING.getCode(), producer.getName(), instance.getCompInstName());
                                        List<KpiData> kpiData = CommonUtil.getKpiDataByErrorCodes(producer.getKpiDetails(), errorMsg, scheduleTime);
                                        Commons.pushKpiData(instance.getCompInstId(), kpiData);
                                        log.error(errorMsg);
                                        continue;
                                    }
                                    if (jschInstance == null) {
                                        int max_channels = ((instance.isAttributeExists(Constants.MAX_CHANNELS)) ? instance.getIntValue(Constants.MAX_CHANNELS) : channelsPerSession);
                                        jschInstance = new JschInstance(hostAddress,
                                                Integer.parseInt(hostPort),
                                                hostUserName,
                                                hostPassword, max_channels,
                                                timeWaitAcquireChannel);
                                        jschInstance.connect();
                                        ComponentAgentCache.getInstance().addCollectorSession(collectorSessionKey, jschInstance);
                                        log.trace("Key:{}, Add jsch instance into cache {}",collectorSessionKey, hashCode);
                                    }

                                    if (!jschInstance.isActive()) {
                                        log.error("Producer {} for component instance {} is unable to connect the server for collecting data.({} seconds interval).",
                                                producer.getName(), instance.getCompInstName(), map.getKey());
                                        String errorMsg = ResourceBundleUtils.getString(ErrorCodes.SSH_CONNECT_ISSUE.getCode(), hostAddress, hostPort, hostUserName);
                                        List<KpiData> kpiData = CommonUtil.getKpiDataByErrorCodes(producer.getKpiDetails(), errorMsg, scheduleTime);
                                        Commons.pushKpiData(instance.getCompInstId(), kpiData);
                                        continue;
                                    }
                                    sshProducer.setJschInstance(jschInstance);
                                } else if(prodInst instanceof ShellProducer) {
                                    ShellProducer shellProducer = (ShellProducer) prodInst;
                                    ShellDetails shellDetails = (ShellDetails) producer.getTypeDetails();
                                    if(signingCheck == 0){
                                        log.info("Skipping signature verification for '{}' file.", shellDetails.getScriptName());
                                    }else if(!Commons.isValidSign(producer.getAccountId(),
                                            shellDetails.getScriptName(), shellDetails.getSignature(), false)) {
                                        String errorMsg = ResourceBundleUtils.getString(ErrorCodes.SIGNATURE_VERIFICATION_FAILED.getCode(),
                                                shellDetails.getScriptName(), producer.getName(), instance.getCompInstName(), ErrorCodes.SIGNATURE_VERIFICATION_FAILED.getCode());
                                        log.error(errorMsg);
                                        List<KpiData> kpiData = CommonUtil.getKpiDataByErrorCodes(producer.getKpiDetails(), errorMsg, scheduleTime);
                                        Commons.pushKpiData(instance.getCompInstId(), kpiData);
                                        continue;
                                    }
                                    shellProducer.setScriptName(shellDetails.getScriptName());
                                    shellProducer.setEnvironmentValue(producer.getEnvData());
                                    shellProducer.setCollectionInterval(map.getKey());
                                    shellProducer.setLastTimeCollected(cache.prodLastRunTime.get(hashCode) != null);
                                    shellProducer.setExecutionType(shellExecuteType);

                                } else if(prodInst instanceof JmxProducer) {
                                    JmxProducer jmxCollector = (JmxProducer) prodInst;
                                    JMXDetails jmxDetails = (JMXDetails) producer.getTypeDetails();
                                    jmxCollector.setJmxUrl(jmxDetails.getJmxUrl());
                                    jmxCollector.setQueryName(jmxDetails.getQueryObject());
                                    jmxCollector.setAttributeDataType(jmxDetails.getAttributeDataType());
                                } else if(prodInst instanceof NetAgentProducer) {
                                    NetAgentProducer netAgentCollector = (NetAgentProducer) prodInst;
                                    List<String> attributeValues = netAgentCollector.getGroupAttributeValue();
                                    String serverIp = instance.getAttributes().get(Constants.HOST_ADDRESS);
                                    Map<String, Map<String, Object>> dataMap = new HashMap<>();
                                    String serverPort = instance.getAttributes().getOrDefault(Constants.MONITOR_PORT, "0");
                                    String timeInGMT = DateUtil.getCurrentGMTTime(netAgentDelayInMins);
                                    if(attributeValues.size() == 1 && attributeValues.contains(serverPort)) {
                                        dataMap.put(serverPort, ComponentAgentCache.getInstance().getNetAgentKpiData(serverIp, Integer.parseInt(serverPort), timeInGMT));
                                    } else {
                                        attributeValues
                                                .forEach(port -> {
                                                    Map<String, Object> kpiMap = ComponentAgentCache.getInstance().getNetAgentKpiData(serverIp, Integer.parseInt(port), timeInGMT);
                                                    dataMap.put(port , kpiMap);
                                                });
                                    }
                                    netAgentCollector.setKpiData(dataMap);
                                    netAgentCollector.setDataFetchTime(timeInGMT);
                                } else if(prodInst instanceof HttpStatusProducer) {
                                    HttpStatusProducer httpStatusProducer = (HttpStatusProducer) prodInst;
                                    HttpDetails httpDetails = (HttpDetails) producer.getTypeDetails();
                                    httpStatusProducer.setStatusUrl(httpDetails.getStatusUrl());
                                    httpStatusProducer.setSSLVersions(sslVerions);
                                    httpStatusProducer.setComponentName(instance.getComponentName());
                                } else if(prodInst instanceof WebSphereProducer) {
                                    WebSphereProducer webSphereProducer = (WebSphereProducer) prodInst;
                                    WebSphereDetails webSphereDetails = (WebSphereDetails) producer.getTypeDetails();
                                    webSphereProducer.setModule(webSphereDetails.getModule());
                                    webSphereProducer.setObjectName(webSphereDetails.getQueryObject());
                                } else if(prodInst instanceof JDBCProducer) {
                                    JDBCProducer jdbcProducer = (JDBCProducer) prodInst;
                                    JDBCDetails jdbcDetails = (JDBCDetails) producer.getTypeDetails();
                                    jdbcProducer.setJdbcDetails(jdbcDetails);
                                    JDBCConnectionKey key = jdbcDetails.getConnectionKey(jdbcProducer.getAttributes());
                                    log.trace("Initializing DB connection.{}, producer:{}, instance:{}", key, producer.getName(), instance.getCompInstName());
                                    Connection connection = ComponentAgentCache.getInstance().getJDBCConnection(key, jdbcProducer.getAttributes(),
                                            sqlTestQueryTimeoutInSecs, producer.getName(), instance.getCompInstName(), sqlTestQuery, sqlTestQueryValidation);
                                    jdbcProducer.setConn(connection);
                                    log.trace("Completed DB connection.{}, producer:{}, instance:{}", key, producer.getName(), instance.getCompInstName());
                                } else if(prodInst instanceof HttpJsonProducer) {
                                    HttpJsonProducer httpJsonProducer = (HttpJsonProducer) prodInst;
                                    HttpDetails httpDetails = (HttpDetails) producer.getTypeDetails();
                                    httpJsonProducer.setUrl(httpDetails.getStatusUrl());
                                    httpJsonProducer.setParameters(httpDetails.getParameters());
                                } else if(prodInst instanceof JPPFProducer) {
                                    JPPFDetails jppfDetails = (JPPFDetails) producer.getTypeDetails();
                                    JPPFProducer jppfProducer = (JPPFProducer) prodInst;
                                    jppfProducer.setJppfServerType(jppfDetails.getJppfServerType());
                                    jppfProducer.setParameters(jppfDetails.getParameters());
                                } else if (prodInst instanceof CoreProducer) {
                                    CoreProducer coreProducer = (CoreProducer) prodInst;
                                    CoreDetails coreDetails = (CoreDetails) producer.getTypeDetails();
                                    coreProducer.setParameters(coreDetails.getParameters());
                                    coreProducer.setInstanceName(instance.getCompInstName());
                                }
                                ICollectionBase prodInstTemp =
                                        ComponentAgentCache.getInstance().getProducerInstance(hashCode);
                                if(prodInstTemp == null){
                                    synchronized (ComponentAgentScheduler.class){
                                        prodInstTemp =
                                                ComponentAgentCache.getInstance().getProducerInstance(hashCode);
                                        if(prodInstTemp == null){
                                            prodInst.init();
                                            ComponentAgentCache.getInstance().addInstance(hashCode, prodInst);
                                        }
                                    }
                                }

                                if(prodInstTemp != null){
                                    reCheckProdInst(prodInstTemp, instance, cache, producer,
                                                    timeWaitAcquireChannel, hashCode, map,
                                                    channelsPerSession);
                                }


                                log.trace("Producer {} for component instance {} is initializing to collect data.({} seconds interval).",
                                        producer.getName(), instance.getCompInstName(), map.getKey());

                            } else {

                                reCheckProdInst(prodInst, instance, cache, producer,
                                                timeWaitAcquireChannel, hashCode, map,
                                                channelsPerSession);

                            }
                            DataCollectionTask task = new DataCollectionTask(prodInst, instance, producer, map.getKey(), scheduleTime);
                            cache.prodLastRunTime.put(hashCode, currentTime);
                            if(execThreshold > 0 && cache.getLastExecutionTime(hashCode) != null && cache.getLastExecutionTime(hashCode) > execThreshold) {
                                log.info("Last execution time {} ms, Producer {} for component instance {} is consumed more than execution threshold time {} ms.",
                                        cache.getLastExecutionTime(hashCode), producer.getName(), instance.getCompInstName(), execThreshold);

                                ThreadPool.INSTANCE.longRunningExecutor.execute(task);
                            } else if(map.getKey() < 60) {
                                ThreadPool.INSTANCE.secondExecutor.execute(task);
                            } else {
                                Commons.checkAndAutoSizeThreadPool();
                                ThreadPool.INSTANCE.minuteExecutor.execute(task);
                            }
                            log.info("Producer {} for component instance {} is scheduled in current iteration for data collection({} seconds interval).",
                                    producer.getName(), instance.getCompInstName(), map.getKey());
                        } catch (ComponentAgentException e) {
                            log.error(e.getSimpleMessage(), e);
                            List<KpiData> kpiData = CommonUtil.getKpiDataByErrorCodes(producer.getKpiDetails(), e.getSimpleMessage(), scheduleTime);
                            Commons.pushKpiData(instance.getCompInstId(), kpiData);
                        } catch (Exception e) {
                            log.error("Error occurred while scheduling ", e);
                            List<KpiData> kpiData = CommonUtil.getKpiDataByErrorCodes(producer.getKpiDetails(), "Error occurred while scheduling in", scheduleTime);
                            Commons.pushKpiData(instance.getCompInstId(), kpiData);
                        }
                    }
                });
            }
        }
    }

    private void reCheckProdInst(ICollectionBase prodInst, ComponentInstance instance,
            ComponentAgentCache cache,Producer producer, int timeWaitAcquireChannel,
            String hashCode, Map.Entry<Integer, List<Producer>> map, int channelsPerSession) throws ComponentAgentException {
        if (prodInst instanceof SSHProducer) {
            Map<String, String> hostDetails = cache.getHostDetails().get(instance.getStringValue(Constants.HOST_ADDRESS));
            String hostAddress, hostPort, hostUserName, hostPassword;
            if(hostDetails != null && !hostDetails.isEmpty()){
                hostAddress = hostDetails.get(Constants.HOST_ADDRESS);
                hostPort = hostDetails.get(Constants.SSH_PORT);
                hostUserName = hostDetails.get(Constants.HOST_USER_NAME);
                hostPassword = Commons.getAttributeValue(Constants.PASSWORD_TYPE, hostDetails.get(Constants.HOST_PASSWORD));
            }else {
                hostAddress = instance.getStringValue(Constants.HOST_ADDRESS);
                hostPort = instance.getStringValue(Constants.SSH_PORT);
                hostUserName = instance.getStringValue(Constants.HOST_USER_NAME);
                hostPassword = instance.getStringValue(Constants.HOST_PASSWORD);
            }

            SSHProducer sshProducer = (SSHProducer) prodInst;
            String collectorSessionKey = instance.getCollectorSessionKey(producer.getProducerType().name());
            JschInstance jschInstance = (JschInstance) ComponentAgentCache.getInstance().
                    getCollectorSession(collectorSessionKey);
            if (jschInstance == null || !jschInstance.isActive()) {
                ComponentAgentCache.getInstance().removeCollectorSession(collectorSessionKey);
                int max_channels = ((instance.isAttributeExists(Constants.MAX_CHANNELS)) ? instance.getIntValue(Constants.MAX_CHANNELS) : channelsPerSession);
                jschInstance = new JschInstance(hostAddress,
                                                Integer.parseInt(hostPort),
                                                hostUserName,
                                                hostPassword, max_channels,
                                                timeWaitAcquireChannel);
                jschInstance.connect();
                ComponentAgentCache.getInstance().addCollectorSession(collectorSessionKey, jschInstance);
                log.trace("Key:{}, Add jsch instance from cache {}",collectorSessionKey, hashCode);
            }
            sshProducer.setJschInstance(jschInstance);
            sshProducer.setEnvironmentValue(producer.getEnvData());
            sshProducer.setCollectionInterval(map.getKey());
            sshProducer.setLastTimeCollected(cache.prodLastRunTime.get(hashCode) != null);
        }  else if(prodInst instanceof ShellProducer) {
            ShellProducer shellProducer = (ShellProducer) prodInst;
            shellProducer.setLastTimeCollected(cache.prodLastRunTime.get(hashCode) != null);
            shellProducer.setEnvironmentValue(producer.getEnvData());
            shellProducer.setCollectionInterval(map.getKey());
        } else if(prodInst instanceof NetAgentProducer) {
            NetAgentProducer netAgentCollector = (NetAgentProducer) prodInst;
            List<String> attributeValues = netAgentCollector.getGroupAttributeValue();
            String serverIp = instance.getAttributes().get(Constants.HOST_ADDRESS);
            Map<String, Map<String, Object>> dataMap = new HashMap<>();
            String serverPort = instance.getAttributes().getOrDefault(Constants.MONITOR_PORT, "0");
            String timeInGMT = DateUtil.getCurrentGMTTime(netAgentDelayInMins);
            if(attributeValues.size() == 1 && attributeValues.contains(serverPort)) {
                dataMap.put(serverPort, ComponentAgentCache.getInstance().getNetAgentKpiData(serverIp, Integer.parseInt(serverPort), timeInGMT));
            } else {
                attributeValues
                        .forEach(port -> {
                            Map<String, Object> kpiMap = ComponentAgentCache.getInstance().getNetAgentKpiData(serverIp, Integer.parseInt(port), timeInGMT);
                            dataMap.put(port , kpiMap);
                        });
            }
            netAgentCollector.setKpiData(dataMap);
            netAgentCollector.setDataFetchTime(timeInGMT);
        }
        if (prodInst.shouldReInit()) {
            prodInst.init();
            ComponentAgentCache.getInstance().addInstance(hashCode, prodInst);
        }
        prodInst.setKPIs(producer.getKpiDetails());
    }
}
