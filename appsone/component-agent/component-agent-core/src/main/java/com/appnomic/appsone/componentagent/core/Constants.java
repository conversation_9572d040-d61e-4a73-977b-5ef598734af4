package com.appnomic.appsone.componentagent.core;

import com.appnomic.appsone.componentagent.common.enums.ConfOperationMode;
import com.appnomic.appsone.componentagent.common.enums.DataOperationMode;

public class Constants {

    // List of properties in basic file
    public static final String CA_UNIQUE_ID = "component.agent.uniqueid";
    public static final String CA_CONFIG_SERVER_URL = "component.agent.configserver.url";
    public static final String CA_CONFIG_NON_DISCOVERY_KPI = "component.agent.non.discovery.kpi";
    public static final String CA_CONFIG_OPERATION_MODE = "component.agent.config.operation.mode";
    public static final String CA_CONFIG_OFFLINE_MODE_DEACTIVATOR_FREQUENCY_DEF = "component.agent.offline.mode.deactivation";
    public static final String CA_CONFIG_DATA_PUSH_FREQUENCY_DEF = "component.agent.data.push.frequency";
    public static final String CA_CONFIG_REQUEST_POLLING_INTERVAL = "component.agent.conf.request.polling.interval";
    public static final String CA_CONFIG_OFFLINE_MODE_DEACTIVATOR_INIT_DELAY = "component.agent.offline.mode.deactivation.init.delay";
    public static final String CA_REQUEST_RETRY_COUNT = "component.agent.conf.request.retry.count";
    public static final String CA_HTTP_CONNECTION_TIMEOUT = "component.agent.conf.request.time.out";
    public static final String CA_HTTP_SOCKET_READ_TIMEOUT = "component.agent.conf.socket.read.time.out";
    public static final ConfOperationMode CA_CONFIG_OPERATION_MODE_DEFAULT_VALUE = ConfOperationMode.Remote;

    public static final String CA_DATA_OPERATION_MODE = "component.agent.data.operation.mode";
    public static final DataOperationMode CA_DATA_OPERATION_MODE_DEFAULT_VALUE = DataOperationMode.Remote;

    public static final String CA_PRODUCERS_MINUTE_THREAD_POOL_SIZE = "component.agent.producers.minute.threadpool.size";
    public static final String CA_PRODUCERS_MINUTE_THREAD_POOL_QUEUE_SIZE = "component.agent.producers.minute.threadpool.queuesize";
    public static final String CA_PRODUCERS_MINUTE_THREAD_POOL_FACTOR = "component.agent.producers.minute.threadpool.factor";
    public static final String CA_PRODUCERS_MINUTE_THREAD_POOL_MAX_FACTOR = "component.agent.producers.minute.threadpool.max.factor.multiplier";
    public static final String CA_PRODUCERS_MINUTE_THREAD_POOL_WARN_FACTOR = "component.agent.producers.minute.threadpool.warning.factor";

    public static final int CA_PRODUCERS_MINUTE_THREAD_POOL_DEFAULT = 16;
    // Set to 0 to disable auto calculation and fall back to maximum.size
    public static final int CA_PRODUCERS_MINUTE_THREAD_POOL_FACTOR_DEFAULT = 1;
    // Set to 0 to disable maximum size of the pool and fall back to maximum.size
    public static final int CA_PRODUCERS_MINUTE_THREAD_POOL_MAX_FACTOR_DEFAULT = 2;
    // Set to 0 to disable the check
    public static final int CA_PRODUCERS_MINUTE_THREAD_POOL_WARN_FACTOR_DEFAULT = 50;
    public static final int CA_PRODUCERS_MINUTE_THREAD_QUEUE_SIZE = 200;
    public static final int CA_PRODUCERS_MINUTE_THREAD_QUEUE_MIN_SIZE = 120;
    public static final int CA_PRODUCERS_MINUTE_THREAD_QUEUE_MAX_SIZE = 500;
    public static final String CA_PRODUCERS_EXEC_TIME_THRESHOLD = "component.agent.producers.execution.threshold";
    // Value is in seconds
    public static final int CA_PRODUCERS_EXEC_TIME_THRESHOLD_DEFAULT = 5;

    public static final String CA_PRODUCERS_LONG_RUNNING_EXEC_TIME_THRESHOLD = "component.agent.producers.long.running.execution.threshold";
    // Value is in seconds
    public static final int CA_PRODUCERS_LONG_RUNNING_EXEC_TIME_THRESHOLD_DEFAULT = 5;


    // Long running producers will be using below thread pool configurations
    public static final String CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_SIZE = "component.agent.producers.long.running.threadpool.size";
    public static final String CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_QUEUE_SIZE = "component.agent.producers.long.running.threadpool.queuesize";
    public static final String CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_FACTOR = "component.agent.producers.long.running.threadpool.factor";
    public static final String CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_WARN_FACTOR = "component.agent.producers.long.running.threshold.warning.factor";
    public static final int CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_DEFAULT = 4;
    // 0 value means disable auto calculation and fall back to maximum.size
    public static final String CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_FACTOR_DEFAULT = "0.5";
    // Set to 0 to disable the check
    public static final int CA_PRODUCERS_LONG_RUNNING_THREAD_POOL_WARN_FACTOR_DEFAULT = 10;
    public static final int CA_PRODUCERS_LONG_RUNNING_THREAD_QUEUE_SIZE = 50;
    public static final int CA_PRODUCERS_LONG_RUNNING_THREAD_QUEUE_MIN_SIZE = 2;
    public static final int CA_PRODUCERS_LONG_RUNNING_THREAD_QUEUE_MAX_SIZE = 16;

    public static final String CA_PRODUCERS_SECOND_THREAD_POOL_SIZE = "component.agent.producers.second.threadpool.size";
    public static final String CA_PRODUCERS_SECOND_THREAD_POOL_QUEUE_SIZE = "component.agent.producers.second.threadpool.queuesize";
    public static final String CA_PRODUCERS_SECOND_THREAD_POOL_FACTOR = "component.agent.producers.second.threadpool.factor";
    public static final String CA_PRODUCERS_SECOND_THREAD_POOL_WARN_FACTOR = "component.agent.producers.second.threadpool.warning.factor";
    public static final int CA_PRODUCERS_SECOND_THREAD_POOL_DEFAULT = 16;
    // 0 value means disable auto calculation and fall back to maximum.size
    public static final int CA_PRODUCERS_SECOND_THREAD_POOL_FACTOR_DEFAULT = 1;
    // Set to 0 to disable the check
    public static final int CA_PRODUCERS_SECOND_THREAD_POOL_WARN_FACTOR_DEFAULT = 50;
    public static final int CA_PRODUCERS_SECOND_THREAD_QUEUE_SIZE = 40000;
    public static final int CA_PRODUCERS_SECOND_THREAD_QUEUE_MIN_SIZE = 120;
    public static final int CA_PRODUCERS_SECOND_THREAD_QUEUE_MAX_SIZE = 120000;


    public static final String CA_FORENSICS_THREAD_POOL_SIZE = "component.agent.forensics.threadpool.size";
    public static final String CA_FORENSICS_THREAD_POOL_QUEUE_SIZE = "component.agent.forensics.threadpool.queuesize";
    public static final String CA_FORENSICS_THREAD_POOL_FACTOR = "component.agent.forensics.threadpool.factor";
    public static final String CA_FORENSICS_THREAD_POOL_WARN_FACTOR = "component.agent.forensics.threadpool.warning.factor";
    public static final int CA_FORENSICS_THREAD_POOL_DEFAULT = 16;
    // 0 value means disable auto calculation and fall back to maximum.size
    public static final int CA_FORENSICS_THREAD_POOL_FACTOR_DEFAULT = 1;

    // Set to 0 to disable the check
    public static final int CA_FORENSICS_THREAD_POOL_WARN_FACTOR_DEFAULT = 5;
    public static final int CA_FORENSICS_THREAD_QUEUE_SIZE = 200;
    public static final int CA_FORENSICS_THREAD_QUEUE_MIN_SIZE = 1;
    public static final int CA_FORENSICS_THREAD_QUEUE_MAX_SIZE = 1000;

    public static final String CA_PRODUCERS_INSTANCES_POOL_CACHE_SPEC = "component.agent.producers.instancespool.cache.spec";
    public static final String CA_PRODUCERS_INSTANCES_POOL_CACHE_SPEC_DEFAULT = "maximumSize=1000";

    public static final String CA_PRODUCERS_POOL_CACHE_SPEC = "component.agent.producers.pool.cache.spec";
    public static final String CA_PRODUCERS_POOL_CACHE_SPEC_DEFAULT = "maximumSize=250";

    // Default cache update schedule is every 1 minute
    public static final int CA_CACHE_UPDATE_INTERVAL_DEF = 60;
    public static final int CA_CACHE_UPDATE_INIT_DELAY = 1;
    // Default data push frequency in sec
    public static final int CA_DATA_PUSH_FREQUENCY_DEF_DEFAULT_VAL = 60;
    public static final int CA_DATA_PUSH_FREQUENCY_DEF_MIN_VAL = 15;
    public static final int CA_DATA_PUSH_FREQUENCY_DEF_MAX_VAL = 300;

    public static final int CA_SEC_SCHEDULER_FREQUENCY_DEF = 5;
    public static final int CA_DELAY_SCHEDULER_FREQUENCY_DEF = 5;
    public static final int CA_SERVICES_INIT_DELAY = 30;
    public static final int CA_MIN_SCHEDULER_FREQUENCY_DEF = 60;

    //Default polling time (in minutes).
    public static final int CA_MIN_SCHEDULER_FREQUENCY_DEF_MIN_VALUE = 1;
    public static final int CA_MIN_SCHEDULER_FREQUENCY_DEF_MAX_VALUE = 60;
    public static final int CA_MIN_SCHEDULER_FREQUENCY_DEF_DEFAULT_VALUE = 5;

    // Default offline mode frequencies diff (in hours).
    public static final int CA_OFFLINE_MODE_DEACTIVATOR_INIT_DELAY = 0;
    public static final int CA_OFFLINE_MODE_DEACTIVATOR_SCHEDULER_FREQUENCY_DEF_MIN = 1;
    public static final int CA_OFFLINE_MODE_DEACTIVATOR_SCHEDULER_FREQUENCY_DEF_MAX = 24;
    public static final int CA_OFFLINE_MODE_DEACTIVATOR_SCHEDULER_FREQUENCY_DEF_DEFAULT = 6;

    //Default online mode retry count
    public static final int CA_REQUEST_RETRY_COUNT_MIN_VALUE = 1;
    public static final int CA_REQUEST_RETRY_COUNT_MAX_VALUE = 9;
    public static final int CA_REQUEST_RETRY_COUNT_DEFAULT_VALUE = 3;

    //Default values for connection timeout and socket read timeout (in seconds)
    public static final int CA_HTTP_CONNECTION_TIMEOUT_MIN_VALUE =1;
    public static final int CA_HTTP_CONNECTION_TIMEOUT_MAX_VALUE =60;
    public static final int CA_HTTP_CONNECTION_TIMEOUT_DEFAULT_VALUE =5;
    public static final int CA_HTTP_SOCKET_READ_TIMEOUT_MIN_VALUE =1;
    public static final int CA_HTTP_SOCKET_READ_TIMEOUT_MAX_VALUE =300;
    public static final int CA_HTTP_SOCKET_READ_TIMEOUT_DEFAULT_VALUE =10;

    // Configuration Check Sum
    public static final String CA_CONFIG_CHECK_SUM_DEF = "";
    public static final String CA_CONFIG_OFFLINE_DEACTIVATOR_CHECK_SUM_DEF = "";
    public static final String CA_DATA_CONTENT_TYPE = "application/octet-stream";

    public static final int CA_15_SECOND_INTERVAL = 15;
    public static final int CA_30_SECOND_INTERVAL = 30;
    public static final int CA_NO_OF_BUCKETS = 3;

    public static final String CA_SCRIPTS_POOL_CACHE = "maximumSize=50";
    public static final String CA_PRODUCERS_SSH_MAX_CHANNELS_PER_SESSION = "component.agent.producers.ssh.max.channels.per.session";
    public static final int CA_PRODUCERS_SSH_MAX_CHANNELS_PER_SESSION_DEFAULT = 10;

    public static final String CA_PRODUCERS_SSH_CHANNEL_WAIT_TIME = "component.agent.producers.ssh.channel.wait.time";
    public static final int CA_PRODUCERS_SSH_CHANNEL_WAIT_TIME_DEFAULT = 10;

    public static final String CA_PRODUCERS_SIGNING_CHECK = "component.agent.producers.signing.check";
    public static final int CA_PRODUCERS_SIGNING_CHECK_DEFAULT = 1;

    public static final String CA_FORENSICS_SIGNING_CHECK = "component.agent.forensic.signing.check";
    public static final int CA_FORENSICS_SIGNING_CHECK_DEFAULT = 1;

    public static final String CA_FORENSICS_CATEGORY_WISE = "component.agent.forensic.category.wise.execute";
    public static final int CA_FORENSICS_CATEGORY_WISE_DEFAULT = 1;

    public static final String CA_FORENSICS_TRIGGER_QUEUE_SIZE = "component.agent.forensic.trigger.queue.size";
    public static final int CA_FORENSICS_TRIGGER_QUEUE_SIZE_DEFAULT = 1000;

    public static final String CA_HTTP_PROTOCOL = "HTTP";

    public static final String CA_HTTPS_PROTOCOL = "HTTPS";
    public static final String CA_CONFIG_FILE_NAME = "agent-details.json";
    public static final String CA_KEY_FILES_PATH = "./keyfiles/";
    public static final String CA_PUBLIC_KEY_FILE_EXTENSION = ".public";
    public static final String CA_PUBLIC_KEY_FILE = "public.key";
    public static final String CA_PRIVATE_KEY_FILE = "private.key";


    public static final String CA_HA_LOCK_RENEW_TIME = "component.agent.ha.lock.renew.time";
    public static final int CA_HA_LOCK_RENEW_TIME_DEFAULT = 2;
    public static final String CA_HA_TTL_TIME = "component.agent.ha.ttl.time";
    public static final int CA_HA_TTL_TIME_DEFAULT = 3;
    public static final String CA_HA_LOCK_RENEW_SCHEDULER = "component.agent.ha.lock.renew.scheduler";
    public static final int CA_HA_LOCK_RENEW_SCHEDULER_DEFAULT = 1;
    public static final String CA_HA_LOCK_CHECK_TIME = "component.agent.ha.lock.check.time";
    public static final int CA_HA_LOCK_CHECK_TIME_DEFAULT = 1;

    public static final String CA_HA_ENABLED = "component.agent.ha.enabled";
    public static final int CA_HA_ENABLED_DEFAULT = 1;

    public static final String GRPC_CLIENT_PEM_FILE = "grpc.client.pem.file";
    public static final String GRPC_CLIENT_PEM_FILE_DEFAULT = "grpc-cert.pem";

    public static final String GRPC_WORKER_THREADS = "grpc.worker.threads";
    public static final String GRPC_WORKER_THREADS_DEFAULT = "4";

    public static final String HTTP_CLIENT_CONNECTIONS = "http.client.connections";
    public static final String HTTP_CLIENT_CONNECTIONS_DEFAULT = "1";

    public static final String IS_GRPC_SSL_ENABLED = "grpc.client.ssl.connect";
    public static final String IS_GRPC_SSL_ENABLED_DEFAULT = "1";

    public static final String CA_MAX_INBOUND_MSG_SIZE = "grpc.max.message.size";
    public static final String CA_DATA_WAIT_CLIENT_TIMEOUT_SECS = "data.wait.client.timeout.secs";
    public static final int CA_DATA_WAIT_CLIENT_TIMEOUT_SECS_DEFAULT = 5;

    public static final String CA_LOCAL_QUEUE_SIZE = "data.queue.size";
    public static final String CA_LOCAL_QUEUE_SIZE_DEFAULT = "1000";
    public static final String CA_CONNECTION_RETRY_LIMIT = "data.connection.retry";
    public static final String CA_CONNECTION_RETRY_LIMIT_DEFAULT = "3";
    public static final int CA_CONNECTION_LOWER_BOUND = 1;
    public static final int CA_CONNECTION_UPPER_BOUND = 5;

    public static final String CA_CONNECTION_RETRY_SLEEP_LIMIT = "data.connection.retry.sleep.mills";
    public static final String CA_CONNECTION_RETRY_SLEEP_LIMIT_DEFAULT = "1000";
    public static final String CA_CONNECTION_SEND_EMPTY_DATA = "send.empty.data";
    public static final String CA_CONNECTION_SEND_EMPTY_DATA_DEFAULT = "1";

    public static final int CA_CONNECTION_RETRY_LOWER_SLEEP_BOUND = 1000;
    public static final int CA_CONNECTION_RETRY_UPPER_SLEEP_BOUND = 10000;


    public static final String CA_JDBC_PRODUCER_MIN_POOL_SIZE = "component.agent.jdbc.producer.min.pool.size";
    public static final String CA_JDBC_PRODUCER_MIN_POOL_DEFAULT = "1";
    public static final String CA_JDBC_PRODUCER_MAX_POOL_SIZE = "component.agent.jdbc.producer.max.pool.size";
    public static final String CA_JDBC_PRODUCER_MAX_POOL_DEFAULT = "5";
    public static final String JDBC_MIN_POOL_PROPERTY = "MinPoolSize";
    public static final String JDBC_MAX_POOL_PROPERTY = "MaxPoolSize";
    public static final String CA_JDBC_TEST_QUERY_VALIDATION = "component.agent.jdbc.test.query.validation";
    public static final int CA_JDBC_TEST_QUERY_VALIDATION_DEFAULT = 1;
    public static final String CA_JDBC_TEST_QUERY = "component.agent.jdbc.test.query";
    public static final String CA_JDBC_TEST_QUERY_DEFAULT = "select 1 from dual";
    public static final String CA_JDBC_TEST_QUERY_TIMEOUT_SECS = "component.agent.jdbc.test.query.timeout.secs";
    public static final String CA_JDBC_TEST_QUERY_TIMEOUT_SECS_DEFAULT = "3";

    public static final String CRON_TIME_ZONE = "cron.timezone";

    public static final String CA_SCHEDULE_TIME_DC = "enable.schedule.data.collection.time";
    public static final String CA_SCHEDULE_TIME_DC_DEFAULT = "1";

    public static final String CA_LOCAL_IP_ADDRESSES = "local.ip.addresses";
    public static final String CA_LOCAL_IP_ADDRESSES_DEFAULT = "";

    public static final String CA_AGENT_VERSION = "component.agent.version";
    public static final String CA_AGENT_VERSION_DEFAULT = "2.0.0";

    public static final String CA_FORENSIC_MAX_LIMIT = "component.agent.forensics.limit";
    public static final String CA_FORENSIC_MAX_LIMIT_DEFAULT = "10";

    public static final String NAMED_PIPE_PATH_WRITE = "named.pipe.path.write";
    public static final String NAMED_PIPE_PATH_WRITE_DEFAULT = "/tmp/na_kpi_pipe_ack";

    public static final String NAMED_PIPE_PATH_READ = "named.pipe.path.read";
    public static final String NAMED_PIPE_PATH_READ_DEFAULT = "/tmp/na_kpi_pipe_data";

    public static final String NAMED_PIPE_PATH_READ_SLEEP_INTERVAL = "named.pipe.path.read.sleep.interval.secs";
    public static final int NAMED_PIPE_PATH_READ_SLEEP_INTERVAL_DEFAULT = 1;

    public static final String NAMED_PIPE_PATH_READ_ERROR_LOG_INTERVAL = "named.pipe.path.read.error.log.interval.minutes";
    public static final int NAMED_PIPE_PATH_READ_ERROR_LOG_INTERVAL_DEFAULT = 1;

    public static final String NET_AGENT_DATA_DELAY_IN_MINS = "net.agent.data.delay.mins";
    public static final int NET_AGENT_DATA_DELAY_IN_MINS_DEFAULT = -1;

    public static final String FORENSIC_ACK_NAMED_PIPE_PATH_WRITE = "forensic.named.pipe.path.write";
    public static final String FORENSIC_ACK_NAMED_PIPE_PATH_WRITE_DEFAULT = "/tmp/na_forensic_pipe_ack";

    public static final String FORENSIC_DATA_NAMED_PIPE_PATH_READ = "forensic.named.pipe.path.read";
    public static final String FORENSIC_DATA_NAMED_PIPE_PATH_READ_DEFAULT = "/tmp/na_forensic_pipe_data";

    public static final String NETAGENT_FORENSIC_MESSAGE_DESCRIPTION = "Forensic data collected from NetAgent";

    public static final String MINIMAL_MODE_PRIORITIES = "minimal.mode.priorities";
    public static final String MINIMAL_MODE_PRIORITIES_DEFAULT = "HIGH";
}
