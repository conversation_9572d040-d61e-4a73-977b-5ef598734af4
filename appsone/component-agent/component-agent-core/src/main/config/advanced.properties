#
# Below property value sets the producers thread pool size and this value is based on number of cpu cores
# Uncomment and set each properties if different from default.
# Default Values; These threads are used to submit the individual producers for data collection
# default value of the pool size is 16
#
#component.agent.producers.minute.threadpool.size=16

# Minutes based task executer thread pool internal queue size
#
#component.agent.producers.minute.threadpool.queuesize=200

# Thread pool size = No. of cores on the system where the CA is running * Thread pool factor
# This auto calculation is disabled when the threadpool factor is 0
# Default value is 1. Factor of no. of cores. This is a decimal number
#
#component.agent.producers.minute.threadpool.factor=1

# Define warning threshold. A warning message is written to logs when the
# (maximum thread pool size)/(number of cores) ratio crosses a warning threshold
#
#component.agent.producers.minute.threadpool.warning.factor=50

# Max thread pool size = No. of cores on the system where the CA is running * Thread pool factor
# This auto calculation is disabled when the this threadpool factor is 0.
# If value specified then factor number should be greater han minute thread pool factor.
# Default value is 2. Factor of no. of cores. This is a decimal number
#
#component.agent.producers.minute.threadpool.max.factor.multiplier=2

#
# Below property value sets the long running producers thread pool size and this value is based on half(0.5) of number of cpu cores
# Uncomment and set each properties if different from default.
# Default Values; These threads are used to submit the individual long running producers for data collection
# default value of the pool size is 4
#
#component.agent.producers.long.running.threadpool.size=4

# Default value is 5 seconds. If any of the scripts take more time than the defined value for this property,
# this producer scripts will be executed next time from long running producers pool.
#
#component.agent.producers.long.running.execution.threshold=5

# Thread pool size = No. of cores on the system where the CA is running * Thread pool factor
# This auto calculation is disabled when the long running threadpool factor is 0
# Default value is 1/2(0.5). Factor of no. of cores. This is a decimal number
#
#component.agent.producers.long.running.threadpool.factor=0.5

# Below properties are similar to minute thread pool configurations (including default values). Only difference is that
# these are applicable to seconds thread pool
#
#component.agent.producers.second.threadpool.size=16
#component.agent.producers.second.threadpool.factor=1
#component.agent.producers.second.threadpool.warning.factor=50

# Seconds basaed task executer thread pool internal queue size
#
#component.agent.producers.second.threadpool.queuesize=40000

# Below properties are used for forensics thread pool
#
#component.agent.forensics.threadpool.size=16
#component.agent.forensics.threadpool.factor=1
#component.agent.forensics.threadpool.warning.factor=5
#component.agent.forensics.threadpool.queuesize=200

# Number of forensics executed per host at any time, Once queue limit is reached then forensics are dropped.
#component.agent.forensics.limit=10

# Default value is 5 seconds. If any of the scripts take more time than the defined value for this property,
# messages are logged to component-agent-diagnostics.log file. Load average of the system is logged post
# execution of the script which breaches the threshold
#
#component.agent.producers.execution.threshold=5

#
# ProducerPoolCache holds producer instances to avoid instantiation of individual producer for every collection
# Uncomment and set properties if different from default. default value of the spec is maximumSize=250

#component.agent.producers.instancespool.cache.spec=maximumSize=250

#
# Uncomment and set the one of value ksh/bash/sh/cmd, then producer scripts will be executed using same.
#
#component.agent.shell.producer.execute.type=

#
# Uncomment and set the one of value ksh/bash/sh/cmd, then forensic scripts will be executed using same.
#
#component.agent.forensic.shell.producer.execute.type=


#Set fallback operation mode retry count, default values are:
# Min - 1
# Default - 3
# Max - 9
#component.agent.conf.request.retry.count=

#Set frequency difference for offline deactivator, default values are:
# Min - 1 hour
# Max - 24 hours
# Default - 6 hours
# If value = -1, never wakeup (This would be required only for Passive monitoring)
#component.agent.offline.mode.deactivation=

#Set conf request polling interval, default values are:
# Min - 1 minute
# Default - 5 minutes
# Max - 60 minutes
#component.agent.conf.request.polling.interval=

#Set request timeout, default values are:
# Min - 15 seconds
# Default - 60 seconds
# Max - 300 second
#component.agent.conf.request.time.out=

#Set request socket timeout, default values are:
# Min - 1 seconds
# Default - 5 seconds
# Max - 300 second
#component.agent.conf.socket.read.time.out=


#Set data push frequency, default values are:
# Min - 15 seconds
# Default - 60 seconds
# Max - 300 second
#component.agent.data.push.frequency=

#Below properties will be used for jdbc producer for connection details, default values are shown below
#component.agent.jdbc.producer.min.pool.size = 1
#component.agent.jdbc.producer.max.pool.size = 5

#Below property has value is 0 then won't send empty data request to HEAL(no data collection happened), otherwise it sends data to HEAL.
#send.empty.data=1

#Below property has the number of retries in-case of data request failure case. Default we do 3 attempts.
#data.connection.retry=3

#Below property has the wait time between each retry attempt.
#data.connection.retry.sleep.mills=1000

# ===================
# ETCD Configurations
# ===================
#
# Below property "etcd.nodes" is a mandatory field for HighAvailability, Not setting any value will
# not consider the HA for component agent.
# For multiple nodes (cluster) use ',' as separator.
# eg ; etcd.nodes=http://************:2379,http://************:2379,http://************:2379
# Un-comment and edit the below properties to override.
#
#etcd.nodes=

# Uncomment and Set below property value 0 (means HA is not required) in case of component agent is running on remote(push model) only.
# By default HA will be enabled, also HA should be available in case of local(pull model).
component.agent.ha.enabled=0

data.endpoint.url=/raw-kpi-data

#component.agent.jdbc.test.query.validation=1
#component.agent.jdbc.test.query=select 1 from dual
#component.agent.jdbc.test.query.timeout.secs=3
#component.agent.version=2.0.0
#component.agent.forensic.category.wise.execute=1


http.client.connection.socket.timeout = 10000
http.client.connection.timeout = 5000

# Uncomment and set below values to collect kpi data from pipes which are written by NetAgent.
# These pipe names should exactly match with the pipe names as per NetAgent
named.pipe.path.write=/tmp/na_kpi_pipe_ack
named.pipe.path.read=/tmp/na_kpi_pipe_data
named.pipe.path.read.sleep.interval.secs=2
forensic.named.pipe.path.write=/tmp/na_forensic_pipe_ack
forensic.named.pipe.path.read=/tmp/na_forensic_pipe_data
minimal.mode.priorities=High