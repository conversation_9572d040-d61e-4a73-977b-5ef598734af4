package com.appnomic.appsone.componentagent.common.util;

import com.appnomic.appsone.componentagent.common.ComponentAgentException;
import com.appnomic.appsone.componentagent.common.beans.Constants;
import com.appnomic.appsone.componentagent.common.beans.KpiData;
import com.appnomic.appsone.componentagent.common.beans.KpiDetails;
import com.appnomic.appsone.componentagent.common.beans.Parameter;
import com.appnomic.appsone.componentagent.common.enums.ErrorCodes;
import com.appnomic.appsone.componentagent.common.enums.KpiType;
import com.appnomic.appsone.componentagent.common.enums.ParameterType;
import com.appnomic.appsone.componentagent.common.enums.ProducerType;
import com.google.common.base.Splitter;
import com.google.common.io.ByteStreams;
import java8.util.Optional;
import java8.util.function.Predicate;
import java8.util.function.Predicates;
import java8.util.stream.Collectors;
import java8.util.stream.StreamSupport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.management.*;
import javax.management.openmbean.CompositeData;
import java.io.*;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by prasad on 24/5/16.
 */
public class CommonUtil {

    private static final Logger log = LoggerFactory.getLogger(CommonUtil.class);
    private static final String regex = "\\{[^\\}]*\\}";
    private static final Pattern pattern = Pattern.compile(regex);

    public static byte[] getScriptFunction(String content, String arguments) {
        String stringBuffer = "function SSHCollector() {\n" +
                content +
                "}\n" +
                "SSHCollector" + arguments;
        return stringBuffer.getBytes(StandardCharsets.UTF_8);
    }

    public static byte[] setEnvData(String content, String envData, String environmentVariable) {
        String stringBuffer = environmentVariable + "='" + envData + "';export " + environmentVariable + ";" +
                content;
        return stringBuffer.getBytes(StandardCharsets.UTF_8);
    }

    public static String getArguments(List<Parameter> parameters, Map<String, String> attributes, ParameterType parameterType) throws ComponentAgentException {
        if(parameters == null || parameters.isEmpty()) {
            return null;
        }
        StringBuilder stringBuffer = new StringBuilder();
        for(Parameter parameter : parameters) {
            if (parameterType.compareTo(parameter.getParameterType()) != 0) {
                continue;
            }

            if(attributes == null || attributes.isEmpty()) {
                stringBuffer.append(" ").append(parameter.getValue());
            } else {
                Matcher matcher = pattern.matcher(parameter.getValue());
                String replaceValue = parameter.getValue();
                while (matcher.find()) {
                    String grp = matcher.group();
                    String key = grp.replace("{", "").replace("}", "").trim();
                    String value = attributes.get(key);
//                    log.info("value :{}", value);
                    if (value != null) {
                        replaceValue = replaceValue.replace(grp, value);
                    } else {
                        throw new ComponentAgentException(ErrorCodes.ATTRIBUTE_NOT_FOUND.getCode(),
                                ":Attribute:" + key + " is not configured for parameter type:" + parameterType.name()+".");
                    }
                }
                stringBuffer.append(" ").append(replaceValue);
            }
        }
        return stringBuffer.toString();
    }


    public static List<String> getParameterValues(List<Parameter> parameters, Map<String, String> attributes, ParameterType parameterType
            , boolean includeKey, boolean throwForMissingKey, String instanceName, String producerName) throws ComponentAgentException {
        if(parameters == null || parameters.isEmpty()) {
            return null;
        }
        List<String> argList = new ArrayList<>();
        for(Parameter parameter : parameters) {
            if (parameterType.compareTo(parameter.getParameterType()) != 0) {
                continue;
            }

            if(attributes == null) {
                argList.add(parameter.getValue());
            } else {
                Matcher matcher = pattern.matcher(parameter.getValue());
                String replaceValue = parameter.getValue();
                while (matcher.find()) {
                    String grp = matcher.group();
                    String key = grp.replace("{", "").replace("}", "").trim();
                    String value = attributes.get(key);
//                    log.info("value :{}", value);
                    if (value != null) {
                        replaceValue = replaceValue.replace(grp, value);
                    } else {
                        if(throwForMissingKey) {
                            throw new ComponentAgentException(ErrorCodes.ATTRIBUTE_NOT_FOUND.getCode(),
                                    ":Attribute:" + key + " is not configured for parameter type:" + parameterType.name() + ".");
                        } else {
                            replaceValue = "";
                            log.warn("Producer parameter:{} value is not exists from available attributes:{} for instance:{}, producer:{}, so continue with empty value.", parameter, attributes, instanceName, producerName);
                        }
                    }
                }
                argList.add((includeKey?parameter.getName()+"=":"")+replaceValue);
            }
        }
        return argList;
    }

    public static Map<String, String> getParametersMap(List<Parameter> parameters, Map<String, String> attributes, ParameterType parameterType) throws ComponentAgentException {
        Map<String, String> parametersMap = new HashMap<>();
        if(parameters == null || parameters.isEmpty()) {
            return parametersMap;
        }
        for(Parameter parameter : parameters) {
            if (parameterType.compareTo(parameter.getParameterType()) != 0) {
                continue;
            }

            if(attributes == null) {
                parametersMap.put(parameter.getName(), parameter.getValue());
            } else {
                Matcher matcher = pattern.matcher(parameter.getValue());
                String replaceValue = parameter.getValue();
                while (matcher.find()) {
                    String grp = matcher.group();
                    String key = grp.replace("{", "").replace("}", "").trim();
                    String value = attributes.get(key);
//                    log.info("value :{}", value);
                    if (value != null) {
                        replaceValue = replaceValue.replace(grp, value);
                    } else {
                        throw new ComponentAgentException(ErrorCodes.ATTRIBUTE_NOT_FOUND.getCode(),
                                ":Attribute:" + key + " is not configured for parameter type:" + parameterType.name()+".");
                    }
                }
                parametersMap.put(parameter.getName(), replaceValue);

            }
        }
        return parametersMap;
    }

    public static String formatContent(String content, Map<String, String> placeHolders) {
        String formatContent = content;
        if (placeHolders == null) {
            return formatContent;
        }
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(formatContent);
        while (matcher.find()) {
            String grp = matcher.group();
            String key = grp.replace("{", "").replace("}", "").trim();
            String value = placeHolders.get(key);
            if (value != null) {
                formatContent = formatContent.replace(grp, value);
            }
        }
        return formatContent;
    }

    public static List<String> getDataInList(String separator, String data) {
        List<String> kpiDatas;
        Splitter splitter = Splitter.on(separator);
        kpiDatas = splitter.splitToList(data);
        return kpiDatas;
    }

    public static List<KpiData> getKpiData(String content, String groupName, String producerName, String instanceName) {

        List<KpiData> kpiDataList = getDataInList(Constants.NEW_LINE_SEPARATOR, content.trim())
                .parallelStream()
                .distinct()
                .map(line -> {
                    try {
                        List<String> data = getDataInList(Constants.ENV_SEPARATOR_VARIABLE_VALUE, line);
                        if (data.size() == 6) {
                            String errorCode = getErrorCode(data.get(4), data.get(0), groupName, producerName, instanceName);
                            KpiData kpiData = new KpiData(data.get(0), data.get(1), data.get(2), Boolean.parseBoolean(data.get(3)), errorCode, KpiType.get(data.get(5)), null, null);
                            log.trace("Collected kpi data {}:", kpiData);
                            return kpiData;
                        } else if(data.size() == 8){
                            String errorCode = getErrorCode(data.get(4), data.get(0), groupName, producerName, instanceName);
                            KpiData kpiData = new KpiData(data.get(0), data.get(1), data.get(2), Boolean.parseBoolean(data.get(3)), errorCode, KpiType.get(data.get(5)), data.get(6), data.get(7));
                            log.trace("Collected kpi data {}:", kpiData);
                            return kpiData;
                        } else {
                            log.error(ResourceBundleUtils.getString(ErrorCodes.DATA_CONVERSION_ERROR.getCode(), "KPI data expected size is 6, actual is "+data.size()+"."));
                            return null;
                        }
                    } catch (Exception e) {
                        log.error(ResourceBundleUtils.getString(ErrorCodes.DATA_CONVERSION_ERROR.getCode(), e.getMessage()), e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(java.util.stream.Collectors.toList());

        log.trace("Number of KPI's collected is {}. {} ", kpiDataList.size(), kpiDataList);
        return kpiDataList;
    }

    public static String getErrorCode(String errorCode, String kpiName, String groupName, String producerName, String instanceName) {
        if(errorCode == null || errorCode.trim().isEmpty()) {
            return errorCode;
        }
        String kpi = (kpiName == null || kpiName.trim().isEmpty()) ? "" : kpiName;
        String grp = (groupName == null || groupName.trim().isEmpty()) ? "" : groupName;
        String prd = (producerName == null || producerName.trim().isEmpty()) ? "" : producerName;
        String inst = (instanceName == null || instanceName.trim().isEmpty()) ? "" : instanceName;
        return ResourceBundleUtils.getString(errorCode, kpi, grp, prd, inst, errorCode);
    }

    public static int getTimeOutValue(int timeoutInSecs, int collectionInterval, int timeoutMultiplier) {
        if(timeoutInSecs > 0 || timeoutMultiplier == 0) {
            return timeoutInSecs;
        }
        int timeOut = collectionInterval * timeoutMultiplier;
        return Math.min(timeOut, Constants.MAX_TIMEOUT_SECONDS);
    }

    public static String getFileContent(String filename) throws ComponentAgentException {
        URL url = CommonUtil.class.getClassLoader().getResource(filename);
        if(url == null) {
            throw new ComponentAgentException(ErrorCodes.FILE_NOT_FOUND.getCode(),
                    ResourceBundleUtils.getString(ErrorCodes.FILE_NOT_FOUND.getCode(), filename));
        }
        return new String(getFileContentInBytes(filename, false), StandardCharsets.UTF_8);
    }

    public static byte[] getFileContentInBytes(String filename, boolean isFullPath) throws ComponentAgentException {
        byte[] fileContent;
        try {
            if(isFullPath) {
                log.trace("isFullPath:{}, filename:{}", isFullPath, filename);
                fileContent = Files.readAllBytes(Paths.get(filename));
            } else {
                URL url = CommonUtil.class.getClassLoader().getResource(filename);
                File file = new File(url.getFile());
                log.trace("File name:{}-{}-{}", file.isFile(), filename, url.getFile());
                Path filePath = Paths.get(url.getPath());
                fileContent = Files.readAllBytes(filePath);
            }
        } catch (Exception e) {
            try {
                fileContent = ByteStreams.toByteArray(CommonUtil.class.getClassLoader().getResourceAsStream(filename));
            } catch (Exception e1) {
                throw new ComponentAgentException(e1, ErrorCodes.ERROR_READING_FILE_CONTENT.getCode(),
                        ResourceBundleUtils.getString(ErrorCodes.ERROR_READING_FILE_CONTENT.getCode(), filename));
            }
        }
        return fileContent;
    }

    public static Set<String> getHostAddresses() {
        Set<String> ipAddress = new HashSet<>();
        try {
            Enumeration<NetworkInterface> e = NetworkInterface.getNetworkInterfaces();
            while(e.hasMoreElements())
            {
                NetworkInterface n = e.nextElement();
                Enumeration<InetAddress> ee = n.getInetAddresses();
                while (ee.hasMoreElements())
                {
                    InetAddress i = ee.nextElement();
                    ipAddress.add(i.getHostName());
                    ipAddress.add(i.getHostAddress());
                }
            }
        } catch (SocketException e1) {
            log.error("Error occurred while fetching host address.");
        }
        return ipAddress;
    }

    public static boolean isLocalHostAddress(String hostAddress, Set<String> hostAddresses) {
        if(hostAddresses == null || hostAddresses.isEmpty()) {
            return false;
        }
        return hostAddresses.contains(hostAddress);
    }

    public static ProducerType getProducerType(ProducerType producerType, boolean isLocalHostAddress) {

        //Check the producer type is script then processed for further validation otherwise return the existing producer type.
        if(!ProducerType.SCRIPT.equals(producerType)) {
            return producerType;
        }

        //If producer type is script and host address is not matching with localhost addresses then return the existing producer type.
        if(!isLocalHostAddress) {
            return producerType;
        }

        //If producer type is script and host address is match with one of the localhost addresses then change the SSH to SHELL producer type.
        return ProducerType.WMI;
    }

    static Predicate<KpiData> isError() {
        return (KpiData k) -> (k.getErrorCode() != null && k.getErrorCode().trim().length() > 0);
    }

    static Predicate<KpiDetails> isGroupKpi() {
        return KpiDetails::isKpiGroup;
    }

    static Predicate<KpiDetails> isDiscoveryKpi(KpiType kpiType) {
        Predicate<KpiDetails> isDiscoveryKpi = Predicates.and(isGroupKpi(), KpiDetails::isDiscovery);
        return Predicates.and(isDiscoveryKpi, k -> kpiType.compareTo(kpiType) == 0);
    }

    static Predicate<KpiDetails> isNotDiscoveryKpi(KpiType kpiType) {
        Predicate<KpiDetails> isNotDiscoveryKpi = Predicates.and(isGroupKpi(), k -> !k.isDiscovery());
        return Predicates.and(isNotDiscoveryKpi, k -> kpiType.compareTo(kpiType) == 0);
    }


    static List<KpiData> getCoreKpiData(Set<KpiDetails> kpiDetailsSet, List<KpiData> cKpiDataList,
                                                    String timeInGMT, String producerName, String instanceName, KpiType kpiType) {

        List<KpiData> kpiDataList = null;

        if(KpiType.CONFIG_WATCH.compareTo(kpiType) == 0 &&
                kpiDetailsSet.size() == StreamSupport.stream(kpiDetailsSet).filter(isDiscoveryKpi(kpiType)).count()) {

            List<KpiData> kpiData = cKpiDataList;

            // Group the kpi data with kpi name and attribute values
            Map<String, Map<String, Map<String, String>>> kpiDataMap = StreamSupport.stream(kpiData)
                    .filter(Predicates.negate(isError()))
                    .filter(k -> k.getFileName() != null && k.getFileName().trim().length() > 0)
                    .collect(Collectors.groupingBy(KpiData::getKpiName,
                            Collectors.groupingBy(KpiData::getFileName,
                                    Collectors.groupingBy(KpiData::getKpiAttributeName,
                                            Collectors.mapping(KpiData::getValue,
                                                    Collectors.joining())))));


            Map<String, Map<String, Map<String,String>>> kpiDataErrorCodesByKeyMap = StreamSupport.stream(kpiData)
                    .filter(isError())
                    .filter(k -> k.getFileName() != null && k.getFileName().trim().length() > 0)
                    .collect(Collectors.groupingBy(KpiData::getKpiName,
                            Collectors.groupingBy(KpiData::getFileName,
                                    Collectors.groupingBy(KpiData::getKpiAttributeName,
                                    Collectors.mapping(KpiData::getErrorCode,
                                            Collectors.joining())))));

            kpiDataList = getDiscoveryConfigWatchKpiData(kpiDetailsSet, kpiDataMap, kpiDataErrorCodesByKeyMap, timeInGMT, producerName, instanceName);
            log.debug("Processed kpi data for grpc:{}", kpiDataList.toString());
            logGroupKpiErrors(cKpiDataList, producerName, instanceName);
        }
        else if((KpiType.FILE_WATCH.compareTo(kpiType) == 0 || KpiType.CONFIG_WATCH.compareTo(kpiType) == 0)
                && kpiDetailsSet.size() == (int) StreamSupport.stream(kpiDetailsSet).filter(isGroupKpi()).count()) {
            List<KpiData> kpiData = cKpiDataList;

            if(KpiType.FILE_WATCH.compareTo(kpiType) == 0) {
                kpiData = convertFileWatchData(cKpiDataList);
            }

            // Group the kpi data with kpi name and attribute values
            Map<String, Map<String, Map<String, String>>> kpiDataMap = StreamSupport.stream(kpiData)
                    .filter(Predicates.negate(isError()))
                    .filter(k -> k.getFileName() != null && k.getFileName().trim().length() > 0)
                    .collect(Collectors.groupingBy(KpiData::getKpiName,
                            Collectors.groupingBy(KpiData::getFileName,
                                    Collectors.groupingBy(KpiData::getKpiAttributeName,
                                            Collectors.mapping(KpiData::getValue,
                                                    Collectors.joining())))));


            Map<String, Map<String,String>> kpiDataErrorCodesByKeyMap = StreamSupport.stream(kpiData)
                    .filter(isError())
                    .filter(k -> k.getFileName() != null && k.getFileName().trim().length() > 0)
                    .collect(Collectors.groupingBy(KpiData::getKpiName,
                            Collectors.groupingBy(KpiData::getFileName,
                                    Collectors.mapping((KpiData k) -> k.getFileName()+k.getKpiAttributeName()+Constants.ERROR_CODE_SPLITTER+k.getErrorCode(),
                                            Collectors.joining(",")))));

            kpiDataList = getWatcherKpiData(kpiDetailsSet, kpiDataMap, kpiDataErrorCodesByKeyMap, timeInGMT, producerName, instanceName);
            log.debug("Processed kpi data for grpc:{}", kpiDataList.toString());
            logGroupKpiErrors(cKpiDataList, producerName, instanceName);

        } else if(kpiType.name().equals(KpiType.FORENSIC.name())){

            // Group the kpi data with kpi name and attribute values
            Map<String, Map<String, String>> kpiDataMap = StreamSupport
                    .stream(cKpiDataList)
                    .filter(Predicates.negate(isError()))
                    .collect(Collectors.groupingBy(KpiData::getKpiName,
                             Collectors.groupingBy(KpiData::getKpiAttributeName,
                             Collectors.mapping(KpiData::getValue,
                                       Collectors.joining()))));

            Map<String, Map<String,String>> kpiDataErrorCodesByKeyMap = StreamSupport
                    .stream(cKpiDataList)
                     .filter(isError())
                     .collect(Collectors.groupingBy(KpiData::getKpiName,
                     Collectors.groupingBy(KpiData::getKpiAttributeName,
                     Collectors.mapping((KpiData k) -> k.getKpiAttributeName()+Constants.ERROR_CODE_SPLITTER+k.getErrorCode(),
                         Collectors.joining(",")))));

            kpiDataList = getForensicKpiData(kpiDetailsSet, kpiDataMap, kpiDataErrorCodesByKeyMap, timeInGMT, producerName, instanceName);
            logGroupKpiErrors(cKpiDataList, producerName, instanceName);

        } else if(kpiDetailsSet.size() == (int)StreamSupport.stream(kpiDetailsSet).filter(Predicates.negate(isGroupKpi())).count()) {

            kpiDataList = getNonGroupKpiData(kpiDetailsSet, cKpiDataList, timeInGMT, producerName, instanceName);
            logErrors(kpiDataList, producerName, instanceName);

        } else if(kpiDetailsSet.size() == (int)StreamSupport.stream(kpiDetailsSet).filter(isNotDiscoveryKpi(kpiType)).count()) { // Check for group, non discovery and core type

            // Group the kpi data with kpi name and attribute values
            Map<String, Map<String, String>> kpiDataMap = StreamSupport.stream(cKpiDataList)
                    .filter(Predicates.negate(isError()))
                    .collect(Collectors.groupingBy(KpiData::getKpiName,
                            Collectors.groupingBy(KpiData::getKpiAttributeName,
                                    Collectors.mapping(KpiData::getValue,
                                            Collectors.joining()))));

            Map<String, Map<String,String>> kpiDataErrorCodesByKeyMap = StreamSupport.stream(cKpiDataList)
                    .filter(isError())
                    .collect(Collectors.groupingBy(KpiData::getKpiName,
                            Collectors.groupingBy(KpiData::getKpiAttributeName,
                                    Collectors.mapping(KpiData::getErrorCode,
                                            Collectors.joining(",")))));

            kpiDataList = getGroupKpiData(kpiDetailsSet, kpiDataMap, kpiDataErrorCodesByKeyMap, timeInGMT, producerName, instanceName);
            logGroupKpiErrors(cKpiDataList, producerName, instanceName);

        } else if(kpiDetailsSet.size() == StreamSupport.stream(kpiDetailsSet).filter(isDiscoveryKpi(kpiType)).count()) {

            // Group the kpi data with kpi name and attribute values
            Map<String, Map<String, String>> kpiDataMap = StreamSupport.stream(cKpiDataList)
                    .filter(Predicates.negate(isError()))
                    .collect(Collectors.groupingBy(KpiData::getKpiName,
                            Collectors.groupingBy(KpiData::getKpiAttributeName,
                                    Collectors.mapping(KpiData::getValue,
                                            Collectors.joining()))));

            Map<String, Map<String, String>> kpiDataErrorCodesMap = StreamSupport.stream(cKpiDataList)
                    .filter(isError())
                    .collect(Collectors.groupingBy(KpiData::getKpiName,
                            Collectors.groupingBy(KpiData::getKpiAttributeName,
                            Collectors.mapping(KpiData::getErrorCode,
                                    Collectors.joining(",")))));

            kpiDataList = getDiscoveryGroupKpiData(kpiDetailsSet, kpiDataMap, kpiDataErrorCodesMap, timeInGMT, producerName, instanceName);

            logGroupKpiErrors(cKpiDataList, producerName, instanceName);
        }

        return kpiDataList;
    }



    static void logGroupKpiErrors(List<KpiData> cKpiDataList, String producerName, String instanceName) {

        if(cKpiDataList == null || cKpiDataList.isEmpty()) {
            return;
        }

        Map<String, Map<String,String>> kpiDataErrorCodesByKeyMap = StreamSupport.stream(cKpiDataList)
                .filter(isError())
                .collect(Collectors.groupingBy(KpiData::getKpiName,
                        Collectors.groupingBy(KpiData::getKpiAttributeName,
                                Collectors.mapping(KpiData::getErrorCode, Collectors.joining(",")))));

        for(Map.Entry<String, Map<String, String>> entry: kpiDataErrorCodesByKeyMap.entrySet()) {
            for(Map.Entry<String, String> eEntry: entry.getValue().entrySet()) {

            }
        }

        StreamSupport.stream(kpiDataErrorCodesByKeyMap.entrySet()).forEach(entry -> {
            for (Map.Entry<String, String> ee : entry.getValue().entrySet()) {
                ErrorCodes errorCodes = ErrorCodes.get(ee.getValue().trim());
                if (errorCodes != null) {
                    log.error(ResourceBundleUtils.getString(errorCodes.getCode(), entry.getKey(), ee.getKey(), producerName, instanceName, errorCodes.getCode()));
                } else {
                    log.error(ResourceBundleUtils.getString(ErrorCodes.UNKNOWN_ERROR_CODE.getCode(), ee.getValue(), ee.getKey(), producerName, instanceName, entry.getKey()));
                }
            }

        });
    }

    static void logErrors(List<KpiData> cKpiData, String producerName, String instanceName) {

        if(cKpiData == null || cKpiData.isEmpty()) {
            return;
        }


        StreamSupport.stream(cKpiData)
                .filter(isError())
                .forEach(k -> {
            ErrorCodes errorCodes = ErrorCodes.get(k.getErrorCode().trim());
            if (errorCodes != null) {
                log.error(ResourceBundleUtils.getString(errorCodes.getCode(), k.getKpiName(), "", producerName, instanceName, errorCodes.getCode()));
            } else {
                log.error(ResourceBundleUtils.getString(ErrorCodes.UNKNOWN_ERROR_CODE.getCode(), k.getErrorCode(), "", producerName, instanceName, k.getKpiName()));
            }
        });
    }

    public static List<KpiData> getKpiDataByErrorCodes(Set<KpiDetails> kpiDetailsSet,
                                            String errorCode,
                                            String timeInGMT) {

        return StreamSupport.stream(kpiDetailsSet).map(g -> {
            KpiData k = g.getKpiData();
            k.setTimeInGMT(timeInGMT);
            k.setErrorCode(errorCode);
            return k;
        }).collect(Collectors.toList());
    }


    static List<KpiData> getNonGroupKpiData(Set<KpiDetails> kpiDetailsSet, List<KpiData> cKpiData, String timeInGMT,
                                            String producerName, String instanceName) {
        List<KpiData> details = StreamSupport.stream(kpiDetailsSet).map(g -> {
            KpiData k = g.getKpiData();
            k.setTimeInGMT(timeInGMT);

            Optional<KpiData> optional = StreamSupport.stream(cKpiData)
                    .filter(c -> c.getKpiName().equals(g.getKpiName()))
                    .findFirst();

            if(optional.isPresent()) {
                k.setValue(optional.get().getValue());
                if(optional.get().getErrorCode() != null && !optional.get().getErrorCode().trim().isEmpty()) {
                    k.setErrorCode(ResourceBundleUtils.getString(optional.get().getErrorCode(), optional.get().getErrorCode(), k.getKpiName(), producerName, instanceName));
                }
            } else {
                k.setErrorCode(ResourceBundleUtils.getString(ErrorCodes.KPI_NOT_COLLECTED.getCode(), k.getKpiName(), "", producerName, instanceName, ErrorCodes.KPI_NOT_COLLECTED.getCode()));
            }

            return k;
        }).collect(Collectors.toList());

        return details;
    }

    static List<KpiData> convertFileWatchData(List<KpiData> kpiData) {
        List<KpiData> kpiDataList = new ArrayList<>();

        kpiDataList.addAll(StreamSupport.stream(kpiData)
                .filter(isError())
                .collect(Collectors.toList()));

        StreamSupport.stream(kpiData)
                .filter(Predicates.negate(isError()))
                .forEach(k -> {
                    kpiDataList.add(new KpiData(k.getKpiName(), k.getValue(), Constants.FILE_CONTENT, k.isKpiGroup(), k.getErrorCode(), k.getKpiType(), k.getFileName(), null));
                    kpiDataList.add(new KpiData(k.getKpiName(), k.getLastUpdated(), Constants.LAST_UPDATED_TIME, k.isKpiGroup(), k.getErrorCode(), k.getKpiType(), k.getFileName(), null));
                });
        return kpiDataList;
    }

    static List<KpiData> getDiscoveryGroupKpiData(Set<KpiDetails> kpiDetailsSet,
                                                Map<String, Map<String, String>> kpiDataMap,
                                                Map<String, Map<String, String>> kpiDataErrorCodesMap, String timeInGMT,
                                                String producerName, String instanceName) {
        return StreamSupport.stream(kpiDetailsSet).map(g -> {
            KpiData k = g.getKpiData();
            k.setTimeInGMT(timeInGMT);
            Map<String, String> grpKpiData = new HashMap<>();
            String errorCode = null;
            if(kpiDataMap.containsKey(g.getKpiName())) {
                grpKpiData.putAll(kpiDataMap.get(g.getKpiName()));
            }
            if(kpiDataErrorCodesMap.containsKey(g.getKpiName())) {
                StreamSupport.stream(kpiDataErrorCodesMap.get(g.getKpiName()).entrySet()).forEach(errEntry -> {
                    grpKpiData.put(errEntry.getKey(), ResourceBundleUtils.getString(errEntry.getValue(), k.getKpiName(), k.getKpiGroupName(), producerName, instanceName, errEntry.getValue()));
                });
                errorCode = kpiDataErrorCodesMap.get(g.getKpiName()).values().stream().distinct().collect(java.util.stream.Collectors.joining(","));
            }
            if(grpKpiData.isEmpty()) {
                errorCode = ResourceBundleUtils.getString(ErrorCodes.KPI_NOT_COLLECTED.getCode(), k.getKpiName(), k.getKpiGroupName(), producerName, instanceName, ErrorCodes.KPI_NOT_COLLECTED.getCode());
            }
            k.setGroupKpiKeyVal(grpKpiData);
            k.setErrorCode(errorCode);
            return k;
        }).collect(Collectors.toList());
    }

    static List<KpiData> getDiscoveryConfigWatchKpiData(Set<KpiDetails> kpiDetailsSet,
                                           Map<String, Map<String, Map<String, String>>> kpiDataMap,
                                           Map<String, Map<String, Map<String, String>>> kpiDataErrorCodesMap,
                                           String timeInGMT, String producerName, String instanceName) {

        log.debug("kpiDataMap:"+kpiDataMap);
        log.debug("kpiDataErrorCodesMap:"+kpiDataErrorCodesMap);
        List<KpiData> details = StreamSupport.stream(kpiDetailsSet).map(g -> {

            KpiData k = g.getKpiData();
            k.setTimeInGMT(timeInGMT);

            log.debug("G string:"+g.toString());
            Map<String, Map<String,String>> watcherKpiData = new HashMap<>();

            // collected kpi data population
            if(kpiDataMap.containsKey(g.getKpiName())) {
                watcherKpiData.putAll(kpiDataMap.get(g.getKpiName()));
            }

            if(kpiDataErrorCodesMap.containsKey(g.getKpiName())) { // error code data population

//                String errorCodes = StreamSupport.stream(kpiDataErrorCodesMap.get(g.getKpiName()).entrySet()).collect();
                StreamSupport.stream(kpiDataErrorCodesMap.get(g.getKpiName()).entrySet()).forEach(discoveryEntry -> {
                    if(watcherKpiData.containsKey(discoveryEntry.getKey())) {
                        watcherKpiData.get(discoveryEntry.getKey()).putAll(discoveryEntry.getValue());
                    }
                });

//                k.setErrorCode(StreamSupport.stream(kpiDataErrorCodesMap.get(g.getKpiName()).values()).distinct().collect(Collectors.joining(",")));
                k.setErrorCode("Error");
            }

            log.debug("Watcher kpi data:"+watcherKpiData);
            k.setConfigKpiKeyVal(watcherKpiData);

            if(watcherKpiData.isEmpty()) {
                k.setErrorCode(ResourceBundleUtils.getString(ErrorCodes.KPI_NOT_COLLECTED.getCode(), k.getKpiName(), k.getKpiGroupName(), producerName, instanceName, ErrorCodes.KPI_NOT_COLLECTED.getCode()));
            } else {
                // error code data population
                if(kpiDataErrorCodesMap.get(g.getKpiName()) != null) {
//                    k.setErrorCode(StreamSupport.stream(kpiDataErrorCodesMap.get(g.getKpiName()).values()).distinct().collect(Collectors.joining(",")));
                    k.setErrorCode("Error");
                }
            }

            log.debug("KPI Data :{}", k);
            return k;
        }).collect(Collectors.toList());

        return details;
    }


    static List<KpiData> getWatcherKpiData(Set<KpiDetails> kpiDetailsSet,
                                                Map<String, Map<String, Map<String, String>>> kpiDataMap,
                                                Map<String, Map<String, String>> kpiDataErrorCodesMap,
                                           String timeInGMT, String producerName, String instanceName) {

        log.debug("kpiDataMap:"+kpiDataMap);
        log.debug("kpiDataErrorCodesMap:"+kpiDataErrorCodesMap);
        List<KpiData> details = StreamSupport.stream(kpiDetailsSet).map(g -> {

            KpiData k = g.getKpiData();
            k.setTimeInGMT(timeInGMT);

            log.debug("G string:"+g.toString());
            List<String> fileNames = Arrays.asList(g.getKpiAttributeName().split("\\|"));
            Map<String, Map<String,String>> watcherKpiData = new HashMap<>();

            // collected kpi data population
            if(kpiDataMap.containsKey(g.getKpiName())) {
                Map<String, Map<String,String>> filesContents = kpiDataMap.get(g.getKpiName());
                StreamSupport.stream(fileNames)
                        .distinct()
                        .forEach(file -> {
                            Map<String, String> fileContent = new HashMap<>();
                            if(filesContents.containsKey(file)) {
                                fileContent = filesContents.get(file);
                            }
                            watcherKpiData.put(file, fileContent);
                        });
            }


            if(kpiDataErrorCodesMap.containsKey(g.getKpiName())) { // error code data population
                watcherKpiData.putAll(StreamSupport.stream(kpiDataErrorCodesMap.get(g.getKpiName()).keySet()).collect(Collectors.toMap(x -> x.trim(), x -> new HashMap<>())));
                k.setErrorCode(StreamSupport.stream(kpiDataErrorCodesMap.get(g.getKpiName()).values()).distinct().collect(Collectors.joining(",")));
            }

            if(watcherKpiData.isEmpty()) {
                k.setConfigKpiKeyVal(watcherKpiData);
                k.setErrorCode(ResourceBundleUtils.getString(ErrorCodes.KPI_NOT_COLLECTED.getCode(), k.getKpiName(), k.getKpiGroupName(), producerName, instanceName, ErrorCodes.KPI_NOT_COLLECTED.getCode()));

            } else {
                // error code data population
                k.setConfigKpiKeyVal(watcherKpiData);
                if(kpiDataErrorCodesMap.get(g.getKpiName()) != null) {
                    k.setErrorCode(StreamSupport.stream(kpiDataErrorCodesMap.get(g.getKpiName()).values()).distinct().collect(Collectors.joining(",")));
                }
            }
            return k;
        }).collect(Collectors.toList());

        return details;
    }

    static List<KpiData> getForensicKpiData(Set<KpiDetails> kpiDetailsSet,
            Map<String, Map<String, String>> kpiDataMap,
            Map<String, Map<String, String>> kpiDataErrorCodesMap, String timeInGMT, String producerName, String instanceName) {

        return StreamSupport.stream(kpiDetailsSet).map(g -> {

            KpiData k = g.getKpiData();
            k.setTimeInGMT(timeInGMT);

            if(kpiDataMap.containsKey(g.getKpiName())) {
                Map<String, String> kpiAttributes = kpiDataMap.get(g.getKpiName());
                Map<String, String> validKpiAttributes = StreamSupport
                        .stream(kpiAttributes.entrySet())
                        .filter(e -> e.getValue() != null && !e.getValue().isEmpty())
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

                Map<String, String> errorCodes = kpiDataErrorCodesMap.get(g.getKpiName());
                Map<String, String> errorKpiAttributes = null;
                if (errorCodes != null) {
                    errorKpiAttributes = StreamSupport
                            .stream(errorCodes.entrySet())
                            .filter(e -> e.getValue() != null && !e.getValue().isEmpty())
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                    validKpiAttributes.putAll(errorKpiAttributes);
                }
                k.setGroupKpiKeyVal(validKpiAttributes);

                if (errorKpiAttributes != null) {
                    k.setErrorCode(StreamSupport.stream(errorKpiAttributes.values()).distinct().collect(Collectors.joining(",")));
                }
            } else {
                Map<String, String> validKpiAttributes = new HashMap<>();
                k.setGroupKpiKeyVal(validKpiAttributes);
                k.setErrorCode(ResourceBundleUtils.getString(ErrorCodes.KPI_NOT_COLLECTED.getCode(), k.getKpiName(), k.getKpiGroupName(), producerName, instanceName, ErrorCodes.KPI_NOT_COLLECTED.getCode()));
            }
            return k;
        }).collect(Collectors.toList());
    }

    static List<KpiData> getGroupKpiData(Set<KpiDetails> kpiDetailsSet,
                                                Map<String, Map<String, String>> kpiDataMap,
                                                Map<String, Map<String, String>> kpiDataErrorCodesMap, String timeInGMT,
                                         String producerName, String instanceName) {

        return StreamSupport.stream(kpiDetailsSet).map(g -> {

            KpiData k = g.getKpiData();
            k.setTimeInGMT(timeInGMT);

            List<String> attributes = Arrays.asList(g.getKpiAttributeName().split("\\|"));
            Map<String, String> kpiAttributesData = kpiDataMap.getOrDefault(g.getKpiName(), new HashMap<>());
            Map<String, String> kpiAttributesErrorData = kpiDataErrorCodesMap.getOrDefault(g.getKpiName(), new HashMap<>());

            Map<String, String> kpiDataEntries = new HashMap<>();
            Set<String> errorCodes = new HashSet<>();
            for (String attribute: attributes) {
                if(kpiAttributesData.containsKey(attribute)) {
                    kpiDataEntries.put(attribute, kpiAttributesData.get(attribute));
                } else if(kpiAttributesErrorData.containsKey(attribute)) {
                    kpiDataEntries.put(attribute, ResourceBundleUtils.getString(kpiAttributesErrorData.get(attribute), k.getKpiName(), k.getKpiGroupName(), producerName, instanceName, kpiAttributesErrorData.get(attribute)));
                    errorCodes.add(kpiAttributesErrorData.get(attribute));
                } else {
                    kpiDataEntries.put(attribute, ResourceBundleUtils.getString(ErrorCodes.KPI_NOT_COLLECTED.getCode(), k.getKpiName(), k.getKpiGroupName(), producerName, instanceName, ErrorCodes.KPI_NOT_COLLECTED.getCode()));
                    errorCodes.add(ErrorCodes.KPI_NOT_COLLECTED.getCode());
                }
            }

            k.setGroupKpiKeyVal(kpiDataEntries);
            k.setErrorCode(StreamSupport.stream(errorCodes).collect(Collectors.joining(",")));
            return k;
        }).collect(Collectors.toList());
    }

    public static List<KpiData> filterCollectedData(Set<KpiDetails> kpiDetailsSet, List<KpiData> collectedKpiDataList,
                                                    String gmtTime, String producerName, String instanceName, String scriptName) {

        List<KpiData> filterData = new ArrayList<>();

        Optional<KpiDetails> coreTypeOptional = StreamSupport.stream(kpiDetailsSet).filter(k -> k.getKpiType().compareTo(KpiType.CORE) == 0).findAny();
        Optional<KpiDetails> availTypeOptional = StreamSupport.stream(kpiDetailsSet).filter(k -> k.getKpiType().compareTo(KpiType.AVAILABILITY) == 0).findAny();
        Optional<KpiDetails> configWatchOptional = StreamSupport.stream(kpiDetailsSet).filter(k -> k.getKpiType().compareTo(KpiType.CONFIG_WATCH) == 0).findAny();
        Optional<KpiDetails> fileWatchOptional = StreamSupport.stream(kpiDetailsSet).filter(k -> k.getKpiType().compareTo(KpiType.FILE_WATCH) == 0).findAny();
        Optional<KpiDetails> forensicOptional =
                StreamSupport.stream(kpiDetailsSet).filter(k -> k.getKpiType().name().equals(KpiType.FORENSIC.name())).findAny();
        if(coreTypeOptional.isPresent()) {
            filterData = getCoreKpiData(kpiDetailsSet, collectedKpiDataList, gmtTime, producerName, instanceName, KpiType.CORE);
        } else if(availTypeOptional.isPresent()) {
            filterData = getCoreKpiData(kpiDetailsSet, collectedKpiDataList, gmtTime, producerName, instanceName, KpiType.AVAILABILITY);
        } else if(configWatchOptional.isPresent()) {
            filterData = getCoreKpiData(kpiDetailsSet, collectedKpiDataList, gmtTime, producerName, instanceName, KpiType.CONFIG_WATCH);
        } else if(fileWatchOptional.isPresent()) {
            filterData = getCoreKpiData(kpiDetailsSet, collectedKpiDataList, gmtTime, producerName, instanceName, KpiType.FILE_WATCH);
        } else if(forensicOptional.isPresent()) {
            filterData = getCoreKpiData(kpiDetailsSet, collectedKpiDataList, gmtTime, producerName, instanceName, KpiType.FORENSIC);
        }
        return filterData;
    }

    private static boolean isCoreGroupDataHasSingleValue(KpiData kpiData) {
        return kpiData.getFileName() == null || (kpiData.getFileName().trim().length() == 0);
    }

    private static KpiData getKPIData(String kpiName, List<KpiData> collectedList) {
        KpiData kpiData = null;
        for(KpiData collectedKpiData: collectedList) {
            if(collectedKpiData.getKpiName().equals(kpiName)) {
                kpiData = collectedKpiData;
                break;
            }
        }
        return kpiData;
    }

    public static ObjectName getMBeanObjectName(MBeanServerConnection serverConnection, ObjectName mbeanObject, String targetObject, String producerName, String instanceName) throws Exception {
        log.trace("Inside getMBeanObjectName(serverConnection:{}, mbeanObject: {}, name: {}, producer:{}, instance:{}) method.", serverConnection, mbeanObject, targetObject, producerName, instanceName);
        ObjectName returnObjName = null;

        Set<ObjectName> objectNames = serverConnection.queryNames(mbeanObject, null);
        log.debug("Object name set {}", objectNames);
        if (objectNames == null) {
            throw new ComponentAgentException(ErrorCodes.TARGET_OBJECT_NOT_EXISTS.getCode(),
                    ResourceBundleUtils.getString(ErrorCodes.TARGET_OBJECT_NOT_EXISTS.getCode(), targetObject,
                            producerName, instanceName));
        }

        // Get the target object for kpi data collection
        for (ObjectName obName : objectNames) {
            if(targetObject == null || targetObject.trim().isEmpty()) {
                returnObjName = obName;
                break;
            }
            String[] targetObjectDetails = targetObject.split("#@#");
            String properName = targetObjectDetails.length == 2 ? targetObjectDetails[0] : "Name";
            String properValue = targetObjectDetails.length == 2 ? targetObjectDetails[1] : targetObject;
            String actualPropertyValue = obName.getKeyProperty(properName);
            if(properValue.equalsIgnoreCase(actualPropertyValue)) {
                returnObjName = obName;
                break;
            }
        }
        return returnObjName;
    }

    public static Object getKpiValue(MBeanServerConnection serverConnection, String kpiName, String groupAttributeName, String attributeDataType, String objectName, String producerName, String instanceName) throws ComponentAgentException {
        log.trace("Inside getKpiValue(kpiName:{}, groupAttributeName:{}, attributeDataType:{}, producerName:{}, instanceName:{}) method.",
                kpiName, groupAttributeName, attributeDataType, producerName, instanceName);
        Object kpiValue = null;
        ObjectName targetObjectName = null;

        try {

            targetObjectName = CommonUtil.getMBeanObjectName(serverConnection, new ObjectName(objectName), groupAttributeName, producerName, instanceName);
            log.trace("Resolved target object name:{}", targetObjectName);

            if(targetObjectName == null) {
                throw new ComponentAgentException(ErrorCodes.JMX_INVALID_TARGET_OBJECT.getCode(),
                        ResourceBundleUtils.getString(ErrorCodes.JMX_INVALID_TARGET_OBJECT.getCode(), objectName, producerName, instanceName));
            }

            //Getting kpi data which are Direct Data type
            if (Constants.JMX_DIRECT_DATA.equalsIgnoreCase(attributeDataType)) {
                String actualName = kpiName.contains("##@@##") ? kpiName.split("##@@##")[1]: kpiName;
                kpiValue = getKpiValue(serverConnection, new ObjectName(objectName), actualName, producerName, instanceName);
            }

            //Getting kpi data which are Composite Data Type (Object)
            if(Constants.JMX_COMPOSITE_DATA.equalsIgnoreCase(attributeDataType)) {
                String[] lookupName = kpiName.split("-");
                Object objectValue = serverConnection.getAttribute(targetObjectName, lookupName[0]);
                if(objectValue instanceof CompositeData) {
                    CompositeData cds = (CompositeData)objectValue;
                    kpiValue = cds.get(lookupName[1]);
                }
            }

            log.debug("Kpi Name:{}, value:{}, group attribute name:{}", kpiName, kpiValue, groupAttributeName);
        } catch (MalformedObjectNameException e) {
            throw new ComponentAgentException(e, ErrorCodes.JMX_INVALID_TARGET_OBJECT.getCode(),
                    ResourceBundleUtils.getString(ErrorCodes.JMX_INVALID_TARGET_OBJECT.getCode(), targetObjectName, producerName, instanceName));
        } catch (IOException e) {
            throw new ComponentAgentException(e, ErrorCodes.JMX_CONNECTION_ERROR.getCode(),
                    ResourceBundleUtils.getString(ErrorCodes.JMX_CONNECTION_ERROR.getCode(), serverConnection, producerName, instanceName));
        } catch (Exception e) {

            throw new ComponentAgentException(e, ErrorCodes.JMX_ATTRIBUTE_NOT_AVAILABLE.getCode(),
                    ResourceBundleUtils.getString(ErrorCodes.JMX_ATTRIBUTE_NOT_AVAILABLE.getCode(), targetObjectName, producerName, instanceName, e.getMessage()));
        }
        return kpiValue;
    }

    private static Object getKpiValue(MBeanServerConnection connection, ObjectName targetObjectName, String kpiName, String producerName, String instanceName)
            throws IOException {
        Set<ObjectName> objectNames = connection.queryNames(targetObjectName, null);
        log.trace("producerName:{}, instanceName:{}, objectNames:{}, kpi name:{} size: {}", producerName, instanceName,
                targetObjectName, kpiName, objectNames.size());
        List<Double> kpiValue = new ArrayList<>();
        for (ObjectName objName: objectNames) {
            try {
                Object objValue = connection.getAttribute(objName, kpiName);
                if (objValue != null) {
                    kpiValue.add(Double.parseDouble(objValue.toString()));
                }
            } catch (Exception e) {
                log.warn("Error occurred while getting MBean:{} value for kpi:{}, producerName:{}, instanceName:{}.",
                        targetObjectName, kpiName, producerName, instanceName, e);
            }
        }
        if(kpiValue.isEmpty()) {
            return null;
        }
        return kpiValue.stream().mapToDouble(a -> a).sum();
    }

    public static String getExecutionTypeByOS() {
        String os = System.getProperty("os.name").toLowerCase();

        if (os.contains("win")) {
            return ".bat";
        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
            return ".sh";
        } else {
            return ".sh";
        }
    }

    // Method to get the full file path
    public static String getFilePath(String folderPath, String fileName) {
        // Get the user's home directory
        String userHomeDirectory = System.getProperty("user.dir");
        File parentDirectory = new File(userHomeDirectory).getParentFile();
        String foundPath = null;
        for(int i = 1;i <= 4; i ++) {
            // Navigate one directory backward from the home directory
            parentDirectory = parentDirectory.getParentFile();

            // Append folders from the parent directory
            String newDirectoryPath = parentDirectory.getPath() + File.separator + folderPath;

            // Create the full file path
            String fullPath = newDirectoryPath + File.separator + fileName;

            log.trace("Folder path:{}, fileName:{}, fullPath:{}, loop:{}", folderPath, fileName, fullPath, i);

            // Validate the file with the new directory
            File file = new File(fullPath);

            // Return the full file path if it exists, otherwise return null
            if (file.exists()) {
                foundPath = fullPath;
                break;
            }
        }

        return foundPath;
    }

    public static List<String> readDataFromPipe(String pipePath) {
        if(pipePath == null || pipePath.trim().isEmpty()) {
            log.info("Pipe path:{} is empty or not configured in advance.properties file.", pipePath);
            return null;
        }
        Path path = Paths.get(pipePath);
        if (!Files.exists(path)) {
            log.error("Configured pipe path:{} does not exists.", pipePath);
            return null;
        }
        List<String> kpiDataInJsonStr = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(Files.newInputStream(path), StandardCharsets.UTF_8))) {
            String line;
            StringBuilder kpiDataJson = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                if(line.trim().isEmpty()) {
                    kpiDataInJsonStr.add(kpiDataJson.toString());
                    kpiDataJson = new StringBuilder();
                } else {
                    kpiDataJson.append(line);
                }
            }
        } catch (Exception e) {
            log.error("Error occurred while reading pipe data. Path:{}", pipePath, e);
        }
        return kpiDataInJsonStr.stream().filter(s -> !s.trim().isEmpty()).distinct().collect(java.util.stream.Collectors.toList());
    }

    private static long lastLogTime = 0;
    public static boolean isNamedPipeExists(String pipePath) {
        long now = System.currentTimeMillis();
        boolean canLog = now - lastLogTime > 10000;
        if(pipePath == null || pipePath.trim().isEmpty()) {
            log.warn("Invalid pipePath value :{}", pipePath);
            lastLogTime = now;
            return false;
        }
        try {
            Path path = Paths.get(pipePath);
            if (Files.exists(path)) {
                if (Files.isRegularFile(path)) {
                    if (canLog) {
                        log.warn("{} is a regular file; expected a named pipe.", pipePath);
                        lastLogTime = now;
                    }
                    return false;
                } else if (Files.isDirectory(path)) {
                    if (canLog) {
                        log.warn("{} is a directory; expected a named pipe.", pipePath);
                        lastLogTime = now;
                    }
                    return false;
                } else {
                    if (canLog) {
                        log.trace("{} is a named pipe (special file).", pipePath);
                        lastLogTime = now;
                    }
                    return true;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("Error occurred while validating the named pipe path:{}.", pipePath, e);
            return false;
        }
    }

    public static boolean createNamedPipe(String pipePath) {
        if(pipePath == null || pipePath.trim().isEmpty()) {
            log.warn("Invalid named pipe path for creation:{}", pipePath);
            return false;
        }
        try {
            // Run the mkfifo command to create a named pipe
            ProcessBuilder pb = new ProcessBuilder("mkfifo", pipePath);
            Process process = pb.start();
            process.waitFor(); // Wait for the process to complete

            if (process.exitValue() == 0) {
                log.info("Named pipe created path:{}", pipePath);
                return true;
            } else {
                log.error("Failed to create named pipe:{}", pipePath);
                return false;
            }
        } catch (IOException | InterruptedException e) {
            log.error("Error occurred while creating named pipe:{}.", pipePath, e);
            return false;
        }
    }
}
