# Java Files
*.class
*.jar

# IntelliJ Files
.idea/
*.iml
*.iws

# Maven Files
target/
log/
*.log
dependency-reduced-pom.xml

# Eclipse Files
.classpath
.project
.settings/

.DS_STORE
appsone-ui-frontend/node_modules/
appsone-ui-frontend/src/bower_modules/
*.idea


# Don't track build output
appsone-ui-frontend/dist/

# ignore node folder
appsone-ui-frontend/node/
appsone-ui/src/main/web/**
/appsone-ui-frontend/package-lock.json
GEMINI.md