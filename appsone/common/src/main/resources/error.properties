#################### appsone-ui error codes ####################
# Error codes related to different modules in the code base
# Choose a Band of 1000 for various modules.
# Error codes for component agent
# Error codes range 1001-1999
E1001=Property component.agent.uniqueid is missing, stopping component agent.
E1002=Property component.agent.configserver.url is missing, stopping component agent.
E1003=Agent details are not loaded.
E1004=Unable to connect to the broker to get configuration.
E1005=Unable to connect to the broker to send data.
E1006=Invalid response received from the broker for configuration request.
E1007=Invalid response received from the broker for data request.
E1008=Configuration cache load error.
E1009=Configuration URL \"{0}\" is not valid.
E1010=Data address \"{0}\" is not valid for \"{1}\".
E1011=\"{0}\":Operation mode \"{1}\" is invalid.
E1012=Agent details not received from broker.
E1013=Agent details received from broker is not component agent.
E1014=Checksum details not present in configuration response from the broker.
E1015=Agent is not active so data collection wont be happen.
E1016=Error occurred while scheduling minute collection interval producers.
E1017=Class \"{0}\" not found to instantiate.
E1018=Unable to instantiate class \"{0}\".
E1019=Illegal access to instantiate class \"{0}\".
E1020=Throwable exception occurred.
E1021=Unable to connect the target server for component instance {0} and producer {1}.
E1022=Error occurred while scheduling seconds collection interval producers.
E1023=Error occurred while doing scheduled job of data sender, {0}.
E1024=Error occurred while parsing the collected data into KPI data object, {0}
E1027=
E1028=
E1031=File not exists or Error reading file content, file name \"{0}\".
E1033=
E1034=Agent details received from broker is null for identifier {0}.
E1035=Agent parameter details received from broker is null for identifier: {0}, name: {1}.
E1036=Error occurred while pre-processing agent details received from broker for identifier: {0}.
E1037=Agent details received from broker \"{0}\" identifier is not same as requested \"{1}\" identifier, stopping component agent.
E1038=Error occurred while pre-processing \"{0}\" component instance details for identifier: {1}.
E1039=Component instance details received from broker is null for identifier {0}.
E1040=Kpi's not exists in \"{0}\" component instance for identifier {1}.
E1041={0}::Invalid collection interval for KPI:{1} for component instance:{2}.
E1042={0}::Default producer not specified for KPI:{1} for component instance:{2}.
E1043={0}::Default producer not exist for KPI:{1} for component instance:{2}.
E1044=File \"{0}\" not found.
E1049={0}:KPI alias name is null/empty for KPI:{1} for component instance:{1}. Skip the kpi for data collector.
E1066=Invalid producer type \"{0}\" for KPI:{1} for component instance:{2} for identifier:{3}.
E1067=Error occurred while signature verification for script \"{0}\".
E1068=Producer \"{0}\" for KPI:{1} for component instance:{2} is not active so data collection wont be happen.
E1070={3}::Signature verification failed for script \"{0}\", data collection wont happen for producer \"{1}\", component instance \"{2}\"
E1071={3}::KPI type \"{0}\" invalid for KPI \"{1}\", component instance \"{2}\"
E1072=Error occurred while decrypting the password. \"{0}\"
E1073=Error occurred while encrypting the password. \"{0}\"
E1074={0}::Error occurred while getting public key. {1}
E1075={0}::Error occurred while getting private key. {1}
E1081=Invalid conf operation mode, stopping component agent.
E1085=Invalid data operation mode, stopping component agent.
E1086=Connection timeout config value \"{0}\" sec is invalid, it starts with default maximum value \"{1}\" sec.
E1087=Connection timeout config value \"{0}\" sec is invalid, it starts with default minimum value \"{1}\" sec.
E1088=Socket read timeout config value \"{0}\" sec is invalid, it starts with default maximum value \"{1}\" sec.
E1089=Socket read timeout config value \"{0}\" sec is invalid, it starts with default minimum value \"{1}\" sec.
E1090=Offline Deactivation frequency config value \"{0}\" hours is invalid, it starts with default maximum value \"{1}\" hours.
E1091=Offline Deactivation frequency config value \"{0}\" hours is invalid, it starts with default minimum value \"{1}\" hours.
E1092=Retry count config value \"{0}\" is invalid, it starts with default maximum value \"{1}\".
E1093=Retry count config value \"{0}\" is invalid, it starts with default minimum value \"{1}\".
E1094=Polling interval config value \"{0}\" min is invalid, it starts with default maximum value \"{1}\" min.
E1095=Polling interval config value \"{0}\" min is invalid, it starts with default minimum value \"{1}\" min.
E1096=\"{0}\" file is missing in config folder, stopping component agent.
E1097=Data push frequency config value \"{0}\" sec is invalid, it starts with default maximum value \"{1}\" sec.
E1098=Data push frequency config value \"{0}\" sec is invalid, it starts with default minimum value \"{1}\" sec.
E1099=File name can not be null/empty, producerName \"{0}\", instanceName \"{1}\".
E1100=SSH port is missing for producer name \"{0}\", instance name \"{1}\", because of this data collection wont happen.
E1106=Skipping data collection for producer \"{0}\" of instance \"{1}\" as the Component-Agent is currently operating in Minimal Mode.


# Error codes for Data services
# Error codes range 2001-2999
E2001=Database not reachable.
E2002=Unable to fetch list for entity.
E2003=Unable to update entity.
E2004=AppsOne user not found for given loginId \"{0}\".
E2005=\"{0}\" entity unable to fetch from DB.
E2006=\"{0}\" data failed to insert entity into database.
E2007=Failed to update entity to database.
E2008=Requested entity not found.
E2009=Entity with same name already exists.
E2010=Failed to delete entity.
E2011=Unrecognized threshold response type \"{0}\".
E2012=Special characters are not allowed for name.
E2013=Name length should be between 2 - 45 characters.
E2014=Duplicate Group values are not allowed for same group.
E2015=Entity Add or Update is not allowed for Inactive Parent.
E2016=Unable to Invalidate Agent cache
E2017=Error occurred while getting public key.
E2018=Error occurred while getting private key.
E2019=Error while reading file content
E2020=Error while Encrypting sensitive data.
E2021=Error while Decrypting sensitive data.
E2022=Data service not responding, for uid \"{0}\" and url \"{1}\", stopping component agent.
E2025=Invalid old password.
E2023=Error while sending registration mail.
E2024=New password can not be same as last 5 old passwords.
E2026=Invalid host address.
E2027=Duplicate Txn patterns are not allowed.
E2028=Can not update standerd role.
E2029=Mendatory fields are missings.
E2030=Host \"{0}\" already exists.
E2031=Level name \"{0}\" already exists.
E2032=Time profile is overlapping for time profile name \"{0}\", day \"{1}\", start time \"{2}\" and end time \"{3}\".
E2033=You do not have privileges to proceed this request.
E2034=Transaction with same name and pattern already exists.
E2035=Description length should be between 25-256 characters.
E2036=Can not delete role mapped to user(s).
E2038=\"{0}\" can not be null.
E2039=\"{0}\" is not selected.
E2040=\"{0}\" entity is not found in DB for given name/id \"{1}\".
E2041=Entity \"{0}\" is already exists in DB.
E2042=User with provided EmailID \"{0}\" already exists!
E2043=User with provided UserName \"{0}\" already exists!
E2044=Duplicate transaction response type (for slow status) is found for coverage window \"{0}\".
E2045=Duplicate transaction status is found for coverage window \"{0}\".
E2046=SMTP Security details not found.".
E2047=Can not save password without username".
E2048=User with given user name is Inactive. Please contact admin.
E2049=Error while logging out from the system.
E2050=Your account is locked. Please contact admin.
E2051=Your password has expired. Please contact admin.
E2052=No agent is found for given uid \"{0}\".
E2053=Invalid data communication tyep \"{0}\".
E2054=Values for \"{0}"\ group must match pattern \"{1}"\.
E2055=Value \"{0}"\ must match pattern \"{1}"\.
E2056=Active directory ip can not be null.
E2057=Active directory domain can not be null.
E2058=User role can not be null.
E2059=Active directory user not found for given loginId \"{0}\".
E2060=Account can not be null.
E2061=This \"{0}\" is already mapped with \"{1}\". Please change the mapping or continue for update.
# Error codes for common project
# Error codes range 3001-3500
E3001=

# Error codes range 3501-4000 for Notification service.
E3501=Can't send an email without host.
E3502=Can't have a password without username.
E3503=Encoding not accepted: {0}
E3504=Generic error: {0}
E3505={0}::Error occurred while loading cache. {1}
E3506={0}::Invalid HTTP server port \"{1}\".
E3507={0}::Invalid HTTPS server port \"{1}\".
E3508=Error occurred while starting undertow server.
E3509=Invalid HTTP server address \"{0}\".
E3510=Error occurred while loading key store for file \"{0}\".
E3511=Invalid HTTP server address \"{0}\".
E3512=Error occurred while stopping undertow server.
E3513=Unable to send email notification for alert profile name \"{0}\" because of error occurred while unzipping and decompress attachments.
E3514={0}::Unable to send EMail notification for name \"{1}\" because of SMTP gateway details are not configured for account id \"{2}\".
E3515={0}::Unable to send EMail notification for name \"{1}\" because of unexpected error occurred.
E3516={0}::Unable to send SMS notification for name \"{1}\" because of SMS gateway details are not configured for account id \"{2}\".
E3517={0}::Unable to send SMS notification for name \"{1}\" because of Mobile numbers not configured or disabled notification.
E3518={0}::Unable to send SMS notification for name \"{1}\" because of unexpected error occurred.
E3519={0}::Error occurred while parsing the violation request.
E3520={0}::Unexpected error occurred while sleeping the thread.
E3521={0}::Unable to send Email notification for name \"{1}\" because of email address not configured or disabled notification.

#Error codes range 4001-4500 for commons
E4001=File \"{0}\" not found.
E4002=Error reading file content, file name \"{0}\". {1}
E4003={0} property not set in the configuration file.

#Error codes range 5000-5500 for supervisor scripts
E5001=Invalid number of arguments passed to the script.
E5002=Invalid data received at an argument.
E5003=Command not found.
E5004=Error at library dependency during ldd check(Linux/Unix only).
E5005=Permission denied for command execution.
E5006=Unable to create temporary file or directory during script execution.
E5007=Failed to delete a temporary file created during script execution.
E5008=Installation path of command is not available in env variable PATH.

#Error codes range 5501-6000 for supervisor controller

#Error codes range 6000-6500 for event handlers