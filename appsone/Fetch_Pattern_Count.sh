. ${producer_env_lib_folder}Producer_Library.sh #@@@Producer_Library.sh#@@@

version() {
    VERSION=1.0
    export VERSION
    ver=$?
    return $ver
}

set_file_string() {
    file_string="Fetch_Pattern_Count"
    sfs=$?
    return $sfs
}

pre_check_dep() {
    dep="grep awk"
    pcd=$?
    return $pcd
}

param_proc() {
    
    KPI_NAME="${kpi_prefix}PATTERN_COUNT"
    echo "${KPI_NAME}${FIELD_SEPARATOR}${FIELD_SEPARATOR}${FIELD_SEPARATOR}true${FIELD_SEPARATOR}E1201${FIELD_SEPARATOR}Core" 
    Cleanup_Script 1
}

Process() {
    # Default to empty if kpi_prefix is not defined
    : "${kpi_prefix:=}"

    if [ "x$ERRCODE" = "x" ]; then
        producer_env_param1=$(echo "$producer_env_param" | sed 's/ /;/g')
        for PROCS in $(echo "$producer_env_param1" | tr '|' ' ')
        do
            log_path=$(echo "$PROCS" | cut -d ',' -f1 | sed 's/;/ /g')
            error_pattern=$(echo "$PROCS" | cut -d ',' -f2 | sed 's/;/ /g')

            if [ ! -f "$log_path" ]; then
                echo "${kpi_prefix}PATTERN_COUNT${FIELD_SEPARATOR}${FIELD_SEPARATOR}${log_path},${error_pattern}${FIELD_SEPARATOR}true${FIELD_SEPARATOR}E1203${FIELD_SEPARATOR}Core"
                continue
            fi

            error_count=$(grep -ic "${error_pattern}" "$log_path")
            echo "${kpi_prefix}PATTERN_COUNT${FIELD_SEPARATOR}${error_count}${FIELD_SEPARATOR}${log_path},${error_pattern}${FIELD_SEPARATOR}true${FIELD_SEPARATOR}${ERRCODE}${FIELD_SEPARATOR}Core"
        done
    else
        for item in $(echo "$producer_env_param" | tr '|' ' ')
        do
            echo "${kpi_prefix}PATTERN_COUNT${FIELD_SEPARATOR}${FIELD_SEPARATOR}${item}${FIELD_SEPARATOR}true${FIELD_SEPARATOR}${ERRCODE}${FIELD_SEPARATOR}Core"
        done
    fi

    proc=0
    return $proc
}

post_process() {
    :
    p_proc=$?
    return $p_proc
}

. ${producer_env_lib_folder}Producer_Driver.sh #@@@Producer_Driver.sh#@@@
driver
