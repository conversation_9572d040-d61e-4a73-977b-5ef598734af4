. ${producer_env_lib_folder}Producer_Library.sh #@@@Producer_Library.sh#@@@

version()
{
    VERSION=1.0
    export VERSION
    ver=$?
    return $ver
}

set_file_string(){
    file_string="trickleFeedMonitoring"
    sfs=$?
    return $sfs
}

pre_check_dep(){
    dep="touch find perl"
    pcd=$?
    return $pcd
}
param_proc()
{
    :
}

param_checks()
{
    if [ "x${producer_env_param}" = "x" ]; then
          echo "UNPROCESSED_TRICKLE_FILES${FIELD_SEPARATOR}${FIELD_SEPARATOR}${FIELD_SEPARATOR}true${FIELD_SEPARATOR}E1201${FIELD_SEPARATOR}Core"
          Cleanup_Script 1
    fi
    if [ "x${FlagToIterate}" = "x" ]; then
          FlagToIterate=1
    fi
    if [ "x${UPDATE_DURATION}" = "x" ]; then
          UPDATE_DURATION=300
    fi
    if [ "x${File_Pattern}" = "x" ]; then
          File_Pattern="*.txt"
    fi
}

compareDates()
{
    retcode=`perl -w -e 'my $formatted_date1 = substr($ARGV[0], 4, 4) . substr($ARGV[0], 2, 2) . substr($ARGV[0], 0, 2); my $formatted_date2 = substr($ARGV[1], 4, 4) . substr($ARGV[1], 2, 2) . substr($ARGV[1], 0, 2); if ($formatted_date1 > $formatted_date2) { print 0; } elsif ($formatted_date1 < $formatted_date2) { print 1; } else { print 2}' "$1" "$2"`
    return $retcode
}

fetchFileStatus()
{
    _folderPath="$1"
    _pattern="$2"
    _currDate=$(date +"%d%m%Y")
    _count=0

    if [ -d "${_folderPath}" ]; then
        if [ $(uname) = "HP-UX" ]; then
            find_cmd="find ${_folderPath} -type f -name \"${_pattern}\" ! -newer $TMPDIR/file_for_find"
        else
            find_cmd="find ${_folderPath} -maxdepth ${FlagToIterate} -type f -name \"${_pattern}\" ! -newer $TMPDIR/file_for_find"
        fi

        eval "$find_cmd" | while read line; do
            _processingDate=$(echo `basename "${line}"` | cut -d '_' -f5 | sed 's/[.].*$//')
            echo "$_processingDate" | grep -qE '^[0-9]{8}$'
            if [ $? -ne 0 ]; then
                 continue 
            fi
            compareDates "${_processingDate}" "${_currDate}"
            retcode=$?
            [ $retcode -ne 0 ] && _count=$((_count + 1))
        done

        if [ ${_count:-0} -gt 0 ]; then
            echo "##########################Section Start######################" >> "${_forensciFile}"
            echo "For Folder ${_folderPath}" >> "${_forensciFile}"
            ls -lrt "${_folderPath}" >> "${_forensciFile}"
            echo "##########################Section End######################" >> "${_forensciFile}"
        fi
        echo "UNPROCESSED_TRICKLE_FILES${FIELD_SEPARATOR}${_count}${FIELD_SEPARATOR}${_folderPath}${FIELD_SEPARATOR}true${FIELD_SEPARATOR}${FIELD_SEPARATOR}Core"
    else
        echo "UNPROCESSED_TRICKLE_FILES${FIELD_SEPARATOR}${_folderPath}${FIELD_SEPARATOR}${FIELD_SEPARATOR}true${FIELD_SEPARATOR}E1203${FIELD_SEPARATOR}Core"
    fi
}

Process()
{
    _curtime=`date "+%Y-%m-%d_%H-%M"`
    _forensciFile="$TMPDIR/TrickleFile_${_curtime}"
    param_checks
    if [ "x$ERRCODE" = "x"  ]; then
          then_time=`perl -w -e '@mytime=localtime (time - $ARGV[0]); printf "%d%.2d%.2d%.2d%.2d", $mytime[5]+1900,$mytime[4]+1,$mytime[3],$mytime[2],$mytime[1];' $UPDATE_DURATION`
          touch -t ${then_time} $TMPDIR/file_for_find
          for _folderpath in `echo "${producer_env_param}" | tr '|' ' '`
          do
                for _filepattern in `echo "${File_Pattern}" | tr '|' ' '`
                do
                    fetchFileStatus "${_folderpath}" "${_filepattern}"
                done
          done
          proc=0
    else
          echo "UNPROCESSED_TRICKLE_FILES${FIELD_SEPARATOR}${FIELD_SEPARATOR}$ARGUMENT${FIELD_SEPARATOR}true${FIELD_SEPARATOR}$ERRCODE${FIELD_SEPARATOR}Core"
          proc=0
    fi
    return $proc
}


post_process()
{
    purge_files "$TMPDIR" "TrickleFile_*"
    rm -f $TMPDIR/file_for_find
    p_proc=$?
    return $p_proc
}

. ${producer_env_lib_folder}Producer_Driver.sh #@@@Producer_Driver.sh#@@@
driver
