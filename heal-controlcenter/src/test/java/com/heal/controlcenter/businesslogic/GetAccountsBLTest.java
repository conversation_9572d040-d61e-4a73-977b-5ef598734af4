package com.heal.controlcenter.businesslogic;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.heal.configuration.pojos.Tags;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.TagsDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GetAccountsBLTest {

    @InjectMocks
    GetAccountsBL getAccountsBL;

    @Mock
    AccountsDao accountsDao;
    @Mock
    UserDao userDao;
    @Mock
    TagsDao tagsDao;
    @Mock
    ObjectMapper objectMapper;
    @Mock
    KeyCloakAuthService keyCloakAuthService;
    @Mock
    ClientValidationUtils clientValidationUtils;
    @Mock
    ServerValidationUtils serverValidationUtils;

    private final String AUTH_TOKEN = "valid-token";
    private final String USER_ID = "user-001";
    private String[] requestParams;

    @BeforeEach
    void setup() {
        requestParams = new String[]{AUTH_TOKEN};
    }

    /**
     * Tests clientValidation with valid auth token and search term.
     * Expects UtilityBean with correct values.
     */
    @Test
    void testClientValidation_success() throws Exception {
        String[] params = new String[]{AUTH_TOKEN, "search"};
        UtilityBean<String> result = getAccountsBL.clientValidation(null, params);
        assertNotNull(result);
        assertEquals(AUTH_TOKEN, result.getRequestParams().get(Constants.AUTH_KEY));
        assertEquals("search", result.getRequestParams().get(Constants.SEARCH_TERM_KEY));
    }

    /**
     * Tests clientValidation with search term as null.
     * Expects search term to default to empty string.
     */
    @Test
    void testClientValidation_searchTermNull_defaultsToEmptyString() throws Exception {
        String[] params = new String[]{AUTH_TOKEN};
        UtilityBean<String> result = getAccountsBL.clientValidation(null, params);
        assertNotNull(result);
        assertEquals("", result.getRequestParams().get(Constants.SEARCH_TERM_KEY));
    }

    /**
     * Tests clientValidation with search term as empty string (whitespace).
     * Expects search term to default to empty string.
     */
    @Test
    void testClientValidation_searchTermEmptyString_defaultsToEmptyString() throws Exception {
        String[] params = new String[]{AUTH_TOKEN, "   "};
        UtilityBean<String> result = getAccountsBL.clientValidation(null, params);
        assertNotNull(result);
        assertEquals("", result.getRequestParams().get(Constants.SEARCH_TERM_KEY));
    }

    /**
     * Tests clientValidation with empty auth token.
     * Expects ClientException to be thrown.
     */
    @Test
    void testClientValidation_authTokenNull_throwsClientException() throws ClientException {
        String[] emptyParams = new String[]{""};
        doThrow(new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE))
                .when(clientValidationUtils).authKeyValidation("");
        ClientException exception = assertThrows(ClientException.class,
                () -> getAccountsBL.clientValidation(null, emptyParams));
        assertEquals("ClientException : " + UIMessages.AUTH_KEY_EXCEPTION_MESSAGE, exception.getMessage());
    }

    /**
     * Tests clientValidation with null auth token.
     * Expects ClientException to be thrown.
     */
    @Test
    void testClientValidation_authTokenIsNull_throwsClientException() throws ClientException {
        String[] params = new String[]{null};

        doThrow(new ClientException(UIMessages.AUTH_KEY_EXCEPTION_MESSAGE))
                .when(clientValidationUtils).authKeyValidation(null);

        ClientException exception = assertThrows(ClientException.class,
                () -> getAccountsBL.clientValidation(null, params));

        assertEquals("ClientException : " + UIMessages.AUTH_KEY_EXCEPTION_MESSAGE, exception.getMessage());
    }

    /**
     * Tests serverValidation with valid access details.
     * Expects UtilityBean with correct user ID and access details.
     */
    @Test
    void testServerValidation_success() throws Exception {
        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.AUTH_KEY, AUTH_TOKEN, Constants.SEARCH_TERM_KEY, "search"))
                .build();

        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), any());
        String jsonAccessDetails = new Gson().toJson(accessDetailsBean);

        UserAccessBean userAccessBean = new UserAccessBean();
        userAccessBean.setAccessDetails(jsonAccessDetails);

        when(serverValidationUtils.authKeyValidation(AUTH_TOKEN)).thenReturn(USER_ID);
        when(userDao.fetchUserAccessDetailsUsingIdentifier(USER_ID)).thenReturn(userAccessBean);
        when(objectMapper.readValue(jsonAccessDetails, AccessDetailsBean.class)).thenReturn(accessDetailsBean);

        UtilityBean<AccessDetailsBean> result = getAccountsBL.serverValidation(utilityBean);

        assertNotNull(result);
        assertEquals("*", result.getPojoObject().getAccounts().get(0));
        assertEquals(USER_ID, result.getMetadata().get(Constants.USER_ID_KEY));
        assertEquals("search", result.getRequestParams().get(Constants.SEARCH_TERM_KEY));
    }

    /**
     * Tests serverValidation with invalid access details JSON.
     * Expects ServerException to be thrown.
     */
    @Test
    void testServerValidation_invalidAccessDetails_throwsServerException() throws ServerException, JsonProcessingException {
        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.AUTH_KEY, AUTH_TOKEN))
                .build();

        UserAccessBean accessBean = new UserAccessBean();
        accessBean.setAccessDetails("null");

        when(serverValidationUtils.authKeyValidation(AUTH_TOKEN)).thenReturn(USER_ID);
        when(userDao.fetchUserAccessDetailsUsingIdentifier(USER_ID)).thenReturn(accessBean);
        when(objectMapper.readValue("null", AccessDetailsBean.class)).thenThrow(new RuntimeException());

        ServerException exception = assertThrows(ServerException.class,
                () -> getAccountsBL.serverValidation(utilityBean));

        assertEquals("ServerException : " + UIMessages.USER_ACCESS_DETAILS_NOT_FOUND, exception.getMessage());
    }

    /**
     * Tests serverValidation when userId extraction throws exception.
     * Expects ServerException to be thrown.
     */
    @Test
    void testServerValidation_userIdExtractionThrowsControlCenterException() throws ServerException {
        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.AUTH_KEY, AUTH_TOKEN))
                .build();

        when(serverValidationUtils.authKeyValidation(AUTH_TOKEN))
                .thenThrow(new ServerException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE));

        ServerException exception = assertThrows(ServerException.class,
                () -> getAccountsBL.serverValidation(utilityBean));

        assertEquals("ServerException : " + UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE, exception.getMessage());
    }

    /**
     * Tests serverValidation when parsed AccessDetailsBean is null.
     * Expects ServerException to be thrown.
     */
    @Test
    void testServerValidation_accessDetailsBeanIsNull_throwsServerException() throws ServerException, JsonProcessingException {
        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .requestParams(Map.of(Constants.AUTH_KEY, AUTH_TOKEN, Constants.SEARCH_TERM_KEY, "search"))
                .build();

        String accessDetailsJson = "{}";

        UserAccessBean userAccessBean = new UserAccessBean();
        userAccessBean.setAccessDetails(accessDetailsJson);

        when(serverValidationUtils.authKeyValidation(AUTH_TOKEN)).thenReturn(USER_ID);
        when(userDao.fetchUserAccessDetailsUsingIdentifier(USER_ID)).thenReturn(userAccessBean);
        when(objectMapper.readValue(accessDetailsJson, AccessDetailsBean.class)).thenReturn(null); // Simulate null return

        ServerException exception = assertThrows(ServerException.class,
                () -> getAccountsBL.serverValidation(utilityBean));

        assertEquals("ServerException : " + UIMessages.INVALID_USER_ACCESS_DETAILS, exception.getMessage());
    }

    /**
     * Tests process with null pageable and search term.
     * Expects defaults to be applied and account to be returned.
     */
    @Test
    void testProcess_pageableAndSearchTermNull_defaultsApplied() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, USER_ID))
                .requestParams(Map.of())
                .build();
        AccountBean accBean = new AccountBean();
        accBean.setId(1);
        accBean.setIdentifier("acc-001");
        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setOffset(19800000L);
        timezoneBean.setTimeZoneId("Asia/Kolkata");
        when(accountsDao.getAccounts(any(), any())).thenReturn(List.of(accBean));
        when(accountsDao.countAccounts(any())).thenReturn(1);
        when(accountsDao.getAccountTimezoneDetails(1)).thenReturn(timezoneBean);
        when(accountsDao.getAccountAnomalyConfiguration(1)).thenReturn(null);
        when(tagsDao.getTagsByObjectId(1, "account")).thenReturn(List.of());
        Page<Account> result = getAccountsBL.process(utilityBean);
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals("acc-001", result.getContent().get(0).getIdentifier());
        assertEquals(10, result.getSize());
        assertEquals(0, result.getNumber());
    }

    /**
     * Tests process with account filtering by identifier.
     * Expects only matching account to be returned.
     */
    @Test
    void testProcess_withAccountFiltering() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("acc-001"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, USER_ID))
                .requestParams(Map.of(Constants.SEARCH_TERM_KEY, "acc"))
                .build();
        AccountBean accBean1 = new AccountBean();
        accBean1.setId(1);
        accBean1.setIdentifier("acc-001");
        accBean1.setName("Account 1");
        accBean1.setUpdatedTime("2024-01-01 10:00:00");
        when(accountsDao.getAccounts(any(), any())).thenReturn(List.of(accBean1));
        when(accountsDao.countAccounts(any())).thenReturn(1);
        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setOffset(19800000L);
        timezoneBean.setTimeZoneId("Asia/Kolkata");
        when(accountsDao.getAccountTimezoneDetails(1)).thenReturn(timezoneBean);
        when(accountsDao.getAccountAnomalyConfiguration(1)).thenReturn(null);
        when(tagsDao.getTagsByObjectId(1, "account")).thenReturn(List.of(new Tags()));
        Page<Account> result = getAccountsBL.process(utilityBean);
        assertEquals(1, result.getTotalElements());
        assertEquals("acc-001", result.getContent().get(0).getIdentifier());
    }

    /**
     * Tests process with various threshold severity flags in config.
     * Expects correct ThresholdSeverity values in Account.
     */
    @Test
    void testProcess_thresholdSeverityFlags() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, USER_ID))
                .requestParams(Map.of())
                .build();
        AccountBean accBean = new AccountBean();
        accBean.setId(1);
        accBean.setIdentifier("acc-001");
        accBean.setName("Account 1");
        accBean.setUpdatedTime("2024-01-01 10:00:00");
        AccountAnomalyConfigurationBean config = org.mockito.Mockito.mock(AccountAnomalyConfigurationBean.class);
        org.mockito.Mockito.when(config.isLowEnable()).thenReturn(true);
        org.mockito.Mockito.when(config.isMediumEnable()).thenReturn(false);
        org.mockito.Mockito.when(config.isHighEnable()).thenReturn(true);
        org.mockito.Mockito.when(config.getClosingWindow()).thenReturn(5);
        org.mockito.Mockito.when(config.getMaxDataBreaks()).thenReturn(2);
        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setOffset(19800000L);
        timezoneBean.setTimeZoneId("Asia/Kolkata");
        when(accountsDao.getAccounts(any(), any())).thenReturn(List.of(accBean));
        when(accountsDao.countAccounts(any())).thenReturn(1);
        when(accountsDao.getAccountTimezoneDetails(1)).thenReturn(timezoneBean);
        when(accountsDao.getAccountAnomalyConfiguration(1)).thenReturn(config);
        when(tagsDao.getTagsByObjectId(1, "account")).thenReturn(List.of());
        Page<Account> result = getAccountsBL.process(utilityBean);
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        Account account = result.getContent().get(0);
        assertNotNull(account.getThresholdSeverity());
        assertTrue(account.getThresholdSeverity().isLow());
        assertEquals(5, account.getClosingWindow());
        assertEquals(2, account.getMaxDataBreaks());
    }

    /**
     * Tests process when anomaly config throws exception.
     * Expects defaults to be applied and account to be returned.
     */
    @Test
    void testProcess_anomalyConfigException_defaultsApplied() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, USER_ID))
                .requestParams(Map.of())
                .build();
        AccountBean accBean = new AccountBean();
        accBean.setId(1);
        accBean.setIdentifier("acc-001");
        when(accountsDao.getAccounts(any(), any())).thenReturn(List.of(accBean));
        when(accountsDao.countAccounts(any())).thenReturn(1);
        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setOffset(19800000L);
        timezoneBean.setTimeZoneId("Asia/Kolkata");
        when(accountsDao.getAccountTimezoneDetails(1)).thenReturn(timezoneBean);
        when(accountsDao.getAccountAnomalyConfiguration(1)).thenThrow(new ControlCenterException("error"));
        when(tagsDao.getTagsByObjectId(1, "account")).thenReturn(List.of());
        Page<Account> result = getAccountsBL.process(utilityBean);
        assertEquals(1, result.getTotalElements());
    }

    /**
     * Tests process when an unexpected exception occurs.
     * Expects DataProcessingException with internal error message.
     */
    @Test
    void testProcess_unexpectedException() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, USER_ID))
                .requestParams(Map.of())
                .build();
        AccountBean accBean = new AccountBean();
        accBean.setId(1);
        accBean.setIdentifier("acc-001");
        when(accountsDao.getAccounts(any(), any())).thenReturn(List.of(accBean));
        when(accountsDao.countAccounts(any())).thenReturn(1);
        when(accountsDao.getAccountTimezoneDetails(1)).thenAnswer(invocation -> {
            throw new RuntimeException("Unexpected error");
        });
        DataProcessingException exception = assertThrows(DataProcessingException.class, () -> getAccountsBL.process(utilityBean));
        assertEquals("DataProcessingException : Unexpected error", exception.getMessage());
    }

    /**
     * Tests process when an unexpected exception occurs (catch block coverage).
     * Expects DataProcessingException with internal error message and logs error.
     */
    @Test
    void testProcess_catchBlock_unexpectedException() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, USER_ID))
                .requestParams(Map.of())
                .build();
        // Simulate unexpected exception in accountDao.getAccounts
        when(accountsDao.getAccounts(any(), any())).thenThrow(new RuntimeException("Simulated error"));
        DataProcessingException exception = assertThrows(DataProcessingException.class, () -> getAccountsBL.process(utilityBean));
        assertEquals("DataProcessingException : simulated error", exception.getMessage());
    }

    /**
     * Tests process with sorting on lastModifiedBy and updatedTime.
     * Expects accounts to be sorted accordingly.
     */
    @Test
    void testProcess_withSorting() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, USER_ID))
                .requestParams(Map.of())
                .pageable(org.springframework.data.domain.PageRequest.of(0, 10,
                        org.springframework.data.domain.Sort.by(
                                org.springframework.data.domain.Sort.Order.asc("lastModifiedBy"),
                                org.springframework.data.domain.Sort.Order.desc("updatedTime")
                        )))
                .build();
        AccountBean accBean1 = new AccountBean();
        accBean1.setId(1);
        accBean1.setIdentifier("acc-001");
        accBean1.setLastModifiedBy("bob");
        accBean1.setUpdatedTime("2024-01-01 10:00:00");
        AccountBean accBean2 = new AccountBean();
        accBean2.setId(2);
        accBean2.setIdentifier("acc-002");
        accBean2.setLastModifiedBy("alice");
        accBean2.setUpdatedTime("2024-01-02 10:00:00");
        AccountBean accBean3 = new AccountBean();
        accBean3.setId(3);
        accBean3.setIdentifier("acc-003");
        accBean3.setLastModifiedBy("bob");
        accBean3.setUpdatedTime("2024-01-03 10:00:00");
        // Unsorted input
        List<AccountBean> beans = List.of(accBean1, accBean2, accBean3);
        when(accountsDao.getAccounts(any(), any())).thenReturn(beans);
        when(accountsDao.countAccounts(any())).thenReturn(3);
        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setOffset(19800000L);
        timezoneBean.setTimeZoneId("Asia/Kolkata");
        when(accountsDao.getAccountTimezoneDetails(anyInt())).thenReturn(timezoneBean);
        when(accountsDao.getAccountAnomalyConfiguration(anyInt())).thenReturn(null);
        when(tagsDao.getTagsByObjectId(anyInt(), any())).thenReturn(List.of());
        Page<Account> result = getAccountsBL.process(utilityBean);
        assertEquals(3, result.getTotalElements());
        // Should be sorted by lastModifiedBy ASC, then updatedTime DESC
        List<Account> sorted = result.getContent();
        assertEquals("acc-002", sorted.get(0).getIdentifier()); // alice
        assertEquals("acc-003", sorted.get(1).getIdentifier()); // bob, 2024-01-03
        assertEquals("acc-001", sorted.get(2).getIdentifier()); // bob, 2024-01-01
        // Additional: check the order is correct for both fields
        assertTrue(sorted.get(0).getLastModifiedBy().compareTo(sorted.get(1).getLastModifiedBy()) < 0
                || (sorted.get(0).getLastModifiedBy().equals(sorted.get(1).getLastModifiedBy()) &&
                sorted.get(0).getUpdatedTime() < sorted.get(1).getUpdatedTime()));
        assertTrue(sorted.get(1).getLastModifiedBy().compareTo(sorted.get(2).getLastModifiedBy()) == 0
                && sorted.get(1).getUpdatedTime() > sorted.get(2).getUpdatedTime());
    }

    @Test
    void testProcess_accessibleAccountsEmpty() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, USER_ID))
                .requestParams(Map.of())
                .build();
        when(accountsDao.getAccounts(any(), any())).thenReturn(List.of());
        when(accountsDao.countAccounts(any())).thenReturn(0);
        Page<Account> result = getAccountsBL.process(utilityBean);
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
    }

    @Test
    void testProcess_tagsDaoThrowsException() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, USER_ID))
                .requestParams(Map.of())
                .build();
        AccountBean accBean = new AccountBean();
        accBean.setId(1);
        accBean.setIdentifier("acc-001");
        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setOffset(19800000L);
        timezoneBean.setTimeZoneId("Asia/Kolkata");
        when(accountsDao.getAccounts(any(), any())).thenReturn(List.of(accBean));
        when(accountsDao.countAccounts(any())).thenReturn(1);
        when(accountsDao.getAccountTimezoneDetails(1)).thenReturn(timezoneBean);
        when(accountsDao.getAccountAnomalyConfiguration(1)).thenReturn(null);
        when(tagsDao.getTagsByObjectId(1, "account")).thenThrow(new ControlCenterException("Tag error"));
        Page<Account> result = getAccountsBL.process(utilityBean);
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertTrue(result.getContent().get(0).getTags().isEmpty());
    }

    @Test
    void testProcess_accountDaoCountAccountsThrowsException() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, USER_ID))
                .requestParams(Map.of())
                .build();
        when(accountsDao.getAccounts(any(), any())).thenReturn(List.of());
        when(accountsDao.countAccounts(any())).thenThrow(new ControlCenterException("Count error"));
        DataProcessingException exception = assertThrows(DataProcessingException.class, () -> getAccountsBL.process(utilityBean));
        assertTrue(exception.getMessage().contains("Error in fetching accounts details"));
    }

    @Test
    void testProcess_pageableSortNotSorted() throws Exception {
        AccessDetailsBean accessDetailsBean = new AccessDetailsBean(List.of("*"), null);
        UtilityBean<AccessDetailsBean> utilityBean = UtilityBean.<AccessDetailsBean>builder()
                .pojoObject(accessDetailsBean)
                .metadata(Map.of(Constants.USER_ID_KEY, USER_ID))
                .requestParams(Map.of())
                .pageable(org.springframework.data.domain.PageRequest.of(0, 10))
                .build();
        AccountBean accBean = new AccountBean();
        accBean.setId(1);
        accBean.setIdentifier("acc-001");
        TimezoneBean timezoneBean = new TimezoneBean();
        timezoneBean.setOffset(19800000L);
        timezoneBean.setTimeZoneId("Asia/Kolkata");
        when(accountsDao.getAccounts(any(), any())).thenReturn(List.of(accBean));
        when(accountsDao.countAccounts(any())).thenReturn(1);
        when(accountsDao.getAccountTimezoneDetails(1)).thenReturn(timezoneBean);
        when(accountsDao.getAccountAnomalyConfiguration(1)).thenReturn(null);
        when(tagsDao.getTagsByObjectId(1, "account")).thenReturn(List.of());
        Page<Account> result = getAccountsBL.process(utilityBean);
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
    }
}
