package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.redis.ServiceRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.util.ClientValidationUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class GetAccountServicesBLTest {

    @Mock
    private ServiceRepo serviceRepo;

    @Mock
    private ClientValidationUtils clientValidationUtils;

    @Mock
    private ServerValidationUtils serverValidationUtils;

    @InjectMocks
    private GetAccountServicesBL getAccountServicesBL;

    @Test
    void testClientValidation_Success() throws ClientException {
        // Arrange
        String authKey = "Bearer test-token";
        String accountIdentifier = "test-account";

        doNothing().when(clientValidationUtils).authKeyValidation(authKey);
        doNothing().when(clientValidationUtils).accountIdentifierValidation(accountIdentifier);

        // Act
        UtilityBean<Object> result = getAccountServicesBL.clientValidation(null, authKey, accountIdentifier);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getRequestParams());
        assertEquals(authKey, result.getRequestParams().get(Constants.AUTH_KEY));
        assertEquals(accountIdentifier, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
    }

    @Test
    void testClientValidation_InvalidAuthKey_ShouldThrowException() throws ClientException {
        // Arrange
        String authKey = "";
        String accountIdentifier = "test-account";

        doThrow(new ClientException("Invalid auth key")).when(clientValidationUtils).authKeyValidation(authKey);

        // Act & Assert
        assertThrows(ClientException.class, () -> {
            getAccountServicesBL.clientValidation(null, authKey, accountIdentifier);
        });
    }

    @Test
    void testServerValidation_Success() throws ServerException {
        // Arrange
        Map<String, String> requestParams = new HashMap<>();
        requestParams.put(Constants.AUTH_KEY, "Bearer test-token");
        requestParams.put(Constants.ACCOUNT_IDENTIFIER, "test-account");

        UtilityBean<Object> utilityBean = UtilityBean.<Object>builder()
                .requestParams(requestParams)
                .build();

        String userId = "test-user";
        Account account = new Account();
        account.setIdentifier("test-account");

        when(serverValidationUtils.authKeyValidation(anyString())).thenReturn(userId);
        when(serverValidationUtils.accountValidation(anyString())).thenReturn(account);

        /*// Act
        UtilityBean<Object> result = getAccountServicesBL.serverValidation(utilityBean);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getMetadata());
        assertEquals(userId, result.getMetadata().get(Constants.USER_ID_KEY));
        assertEquals(account, result.getMetadata().get(Constants.ACCOUNT));*/
    }

    @Test
    void testProcess_Success() throws DataProcessingException {
        // Arrange
        Account account = new Account();
        account.setIdentifier("test-account");

        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.ACCOUNT, account);

        UtilityBean<Object> utilityBean = UtilityBean.<Object>builder()
                .metadata(metadata)
                .build();

        List<BasicEntity> mockServices = List.of();
        when(serviceRepo.getAllServicesDetails("test-account")).thenReturn(mockServices);

        /*// Act
        List<Object> result = getAccountServicesBL.process(utilityBean);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty()); // Returns empty list when no services found
        verify(serviceRepo).getAllServicesDetails("test-account");*/
    }

    @Test
    void testProcess_WithServices_Success() throws DataProcessingException {
        // Arrange
        Account account = new Account();
        account.setIdentifier("test-account");

        Map<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.ACCOUNT, account);

        UtilityBean<Object> utilityBean = UtilityBean.<Object>builder()
                .metadata(metadata)
                .build();

        BasicEntity service1 = new BasicEntity();
        service1.setId(1);
        service1.setName("Service 1");
        service1.setIdentifier("service-1");

        BasicEntity service2 = new BasicEntity();
        service2.setId(2);
        service2.setName("Service 2");
        service2.setIdentifier("service-2");

        List<BasicEntity> mockServices = List.of(service1, service2);
        when(serviceRepo.getAllServicesDetails("test-account")).thenReturn(mockServices);

        /*// Act
        List<Object> result = getAccountServicesBL.process(utilityBean);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(serviceRepo).getAllServicesDetails("test-account");*/
    }
}
