package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AgentTypePojo;
import com.heal.controlcenter.util.CommonUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
@PrepareForTest({GetAgentTypesBL.class})
public class GetAgentTypesBLTest {

    @InjectMocks
    GetAgentTypesBL getAgentTypesBL;

    @Mock
    CommonUtils commonUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    ControllerDao controllerDao;
    @Mock
    MasterDataDao masterDataDao;
    @Mock
    CompInstanceDao compInstanceDao;
    @Mock
    AgentDao agentDao;

    String[] requestParams = new String[3];
    UtilityBean<Integer> mockUtilityBean = null;
    AccountBean account = new AccountBean();

    ControllerBean controller = new ControllerBean();
    ViewTypesBean viewType = new ViewTypesBean();

    @BeforeEach
    void setUp() throws ControlCenterException {
        account.setId(1);

        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";
        requestParams[2] = "66";

        mockUtilityBean = UtilityBean.<Integer>builder()
                .requestParams(Map.of(
                        "Authorization", "mockAuthToken",
                        "accountIdentifier", "mockAccountIdentifier"
                ))
                .metadata(Map.of(
                        "userId", "mockUserId"
                ))
                .pojoObject(Integer.parseInt(requestParams[2]))
                .build();

    }

    @AfterEach
    void tearDown() {
        requestParams = null;
        mockUtilityBean = null;
    }

    @Test
    void clientValidations_EmptyAuthorizationToken() {
        requestParams[0] = "";
        requestParams[1] = "mockAccountIdentifier";

        String expectedMessage = "ClientException : Invalid authorization token";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getAgentTypesBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_EmptyAccountIdentifier() {
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "";

        String expectedMessage = "ClientException : Invalid account identifier";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getAgentTypesBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ErrorWhileFetchingUserId() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(ControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                getAgentTypesBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_AuthKeyInvalid() throws Exception {
        String expectedMessage = "ClientException : Error occurred while fetching userId from authorization token";
        when(commonUtils.getUserId("mockAuthToken")).thenThrow(ControlCenterException.class);
        ClientException requestException = assertThrows(ClientException.class, () ->
                getAgentTypesBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_ServiceInvalid() {
        String[] requestParams = new String[3];
        requestParams[0] = "mockAuthToken";
        requestParams[1] = "mockAccountIdentifier";
        requestParams[2] = "";
        String expectedMessage = "ClientException : Invalid serviceId";
        ClientException requestException = assertThrows(ClientException.class, () ->
                getAgentTypesBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));

        requestParams[2] = "service";
        requestException = assertThrows(ClientException.class, () ->
                getAgentTypesBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));

        requestParams[2] = "-10";
        requestException = assertThrows(ClientException.class, () ->
                getAgentTypesBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));

        requestParams[2] = "0";
        requestException = assertThrows(ClientException.class, () ->
                getAgentTypesBL.clientValidation(null, requestParams));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void clientValidations_Success() throws Exception {
        String userId = "mockUserId";
        when(commonUtils.getUserId("mockAuthToken")).thenReturn(userId);
        UtilityBean<Integer> utilityBean = getAgentTypesBL.clientValidation(null, requestParams);
        assertEquals(utilityBean.getMetadata().get("userId"), userId);
    }

    @Test
    void serverValidation_Success() throws Exception {
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        when(controllerDao.getServiceById(anyInt(), anyInt())).thenReturn(controller);
        getAgentTypesBL.serverValidation(mockUtilityBean);
    }

    @Test
    void serverValidation_InvalidAccountIdentifier() {
        String expectedMessage = "ServerException : Account with identifier [mockAccountIdentifier] is unavailable";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(any());
        ServerException requestException = assertThrows(ServerException.class, () ->
                getAgentTypesBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void serverValidation_InvalidServiceId() {
        viewType.setSubTypeId(67);
        ControllerBean controllerBean = new ControllerBean();
        controllerBean.setControllerTypeId(66);

        String expectedMessage = "ServerException : ServiceId [66] is unavailable for account [mockAccountIdentifier]";
        when(accountsDao.getAccountByIdentifier("mockAccountIdentifier")).thenReturn(account);
        when(controllerDao.getServiceById(anyInt(), anyInt())).thenReturn(null);
        ServerException requestException = assertThrows(ServerException.class, () ->
                getAgentTypesBL.serverValidation(mockUtilityBean));
        assertTrue(expectedMessage.contains(requestException.getMessage()));
    }

    @Test
    void process_Success() throws Exception {
        Set<Integer> ids = new HashSet<>();
        ids.add(1);
        ViewTypesBean subType = new ViewTypesBean();

        Set<AgentBean> agentBeans = new HashSet<>();
        AgentBean ab = new AgentBean();
        ab.setAgentTypeId(1);
        agentBeans.add(ab);

        when(compInstanceDao.getAgentIdForService(anyInt(), anyInt())).thenReturn(ids);
        when(compInstanceDao.getAgentIdForServiceFromTagMapping(anyInt(), anyInt())).thenReturn(ids);
        when(agentDao.getAgentTypeListApartFromJimAndForensicAgents(anySet())).thenReturn(agentBeans);
        when(masterDataDao.getMstSubTypeBySubTypeId(anyInt())).thenReturn(subType);

        List<AgentTypePojo> data = getAgentTypesBL.process(mockUtilityBean);
        assertEquals(1, data.size());
    }
}
