package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.AccountServiceValidationBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.beans.ViewApplicationServiceMappingBean;
import com.heal.controlcenter.dao.mysql.ControllerDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.dao.mysql.AccountServiceDao;
import com.heal.controlcenter.dao.mysql.ConnectionDetailsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.pojo.ServiceListPage;
import com.heal.controlcenter.util.KeyCloakAuthService;
import com.heal.controlcenter.pojo.KeyCloakUserDetails;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GetAccountServicesBLCreatedByTest {

    @Mock
    private ControllerDao controllerDao;

    @Mock
    private UserDao userDao;

    @Mock
    private AccountServiceDao accountServiceDao;

    @Mock
    private ConnectionDetailsDao connectionDetailsDao;

    @Mock
    private MasterDataDao masterDataDao;

    @Mock
    private KeyCloakAuthService keyCloakAuthService;

    @InjectMocks
    private GetAccountServicesBL getAccountServicesBL;

    @Test
    void testProcess_SetsCreatedByField() throws Exception {
        // Arrange
        Account account = new Account();
        account.setId(1);
        account.setIdentifier("test-account");

        AccountServiceValidationBean validationBean = AccountServiceValidationBean.builder()
                .account(account)
                .build();

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("userId", "test-user-id");

        Map<String, String> requestParams = new HashMap<>();
        requestParams.put("searchTerm", "");

        Pageable pageable = PageRequest.of(0, 10);

        UtilityBean<AccountServiceValidationBean> utilityBean = UtilityBean.<AccountServiceValidationBean>builder()
                .pojoObject(validationBean)
                .metadata(metadata)
                .requestParams(requestParams)
                .pageable(pageable)
                .build();

        // Mock service data
        ControllerBean service = new ControllerBean();
        service.setId(1);
        service.setName("Test Service");
        service.setIdentifier("test-service");
        service.setLastModifiedBy("user123");
        service.setCreatedTime("2023-01-01 10:00:00");
        service.setUpdatedTime("2023-01-02 11:00:00");
        service.setStatus(1);

        List<ControllerBean> services = List.of(service);

        // Mock user access
        ViewApplicationServiceMappingBean mapping = new ViewApplicationServiceMappingBean();
        mapping.setServiceId(1);
        mapping.setApplicationId(1);
        mapping.setApplicationName("Test App");
        mapping.setApplicationIdentifier("test-app");

        // Mock KeyCloak user details
        KeyCloakUserDetails userDetails = new KeyCloakUserDetails();
        userDetails.setUsername("testuser");

        // Setup mocks
        when(userDao.getUserAccessibleApplicationsServices(anyString(), anyString()))
                .thenReturn(List.of(mapping));
        when(controllerDao.getServicesList(eq(1), anyString(), anyList(), any(Pageable.class)))
                .thenReturn(services);
        when(controllerDao.getServicesListCount(eq(1), anyString(), anyList()))
                .thenReturn(1);
        when(connectionDetailsDao.getConnectionsByAccountId(1))
                .thenReturn(Collections.emptyList());
        when(masterDataDao.getCompInstanceDetails(1))
                .thenReturn(Collections.emptyList());
        when(keyCloakAuthService.getKeycloakUserDataFromId("user123"))
                .thenReturn(userDetails);
        when(accountServiceDao.getTagValueForService(anyInt(), anyString()))
                .thenReturn(null);
        when(accountServiceDao.isEntryPointService(anyInt()))
                .thenReturn(false);
        when(accountServiceDao.getServiceGroupForService(anyInt()))
                .thenReturn(null);

        // Act
        Page<ServiceListPage> result = getAccountServicesBL.process(utilityBean);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        
        ServiceListPage serviceListPage = result.getContent().get(0);
        assertNotNull(serviceListPage.getCreatedBy());
        assertEquals("testuser", serviceListPage.getCreatedBy());
        assertEquals("testuser", serviceListPage.getLastModifiedBy());
        
        // Verify that both createdBy and lastModifiedBy are set to the same value
        assertEquals(serviceListPage.getCreatedBy(), serviceListPage.getLastModifiedBy());
    }
}
