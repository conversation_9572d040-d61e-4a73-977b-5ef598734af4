package com.heal.controlcenter.businesslogic;

import com.heal.configuration.entities.UserAccessDetails;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.Service;
import com.heal.controlcenter.beans.ServiceSuppPersistenceConfigurationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.ServiceConfigurationDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ServiceSuppPersistenceConfigPojo;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.ServerValidationUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

class GetServiceAnomalyConfigBLTest {
    @InjectMocks
    private GetServiceAnomalyConfigBL getServiceAnomalyConfigBL;

    @Mock
    private CommonUtils commonUtils;

    @Mock
    private ServiceConfigurationDao serviceConfigurationDao;

    @Mock
    private ServerValidationUtils serverValidationUtils;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void clientValidation_NullAuthKey_ThrowsClientException() {
        String[] params = {"", "accountIdentifier", "serviceIdentifier"};
        assertThrows(ClientException.class, () ->
                getServiceAnomalyConfigBL.clientValidation(null,params));
    }

    @Test
    void clientValidation_EmptyAccountIdentifier_ThrowsClientException() {
        String[] params = {"authKey", "", "serviceIdentifier"};
        assertThrows(ClientException.class, () ->
                getServiceAnomalyConfigBL.clientValidation(null, params));
    }

    @Test
    void clientValidation_EmptyServiceIdentifier_ThrowsClientException() {
        String[] params = {"authKey", "accountIdentifier", ""};
        assertThrows(ClientException.class, () ->
                getServiceAnomalyConfigBL.clientValidation(null,params));
    }

    @Test
    void clientValidation_ValidInput_ReturnsUtilityBean() throws Exception {
        String[] params = {"authKey", "accountIdentifier", "serviceIdentifier"};
        UtilityBean result = getServiceAnomalyConfigBL.clientValidation(null,params);
        assertEquals("accountIdentifier", result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
    }

    @Test
    void serverValidation_ValidInput_ReturnsAccountServiceKey() throws ServerException {
        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.AUTH_KEY, "authKey");
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");

        Account account = new Account();
        account.setId(1);
        account.setIdentifier("accountIdentifier");

        Service service = Service.builder().id(1).identifier("serviceIdentifier").build();

        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.ACCOUNT, account);
        metadata.put(Constants.SERVICE, service);

        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .pojoObject("serviceIdentifier")
                .requestParams(requestParamsMap)
                .metadata(metadata)
                .build();

        when(serverValidationUtils.authKeyValidation(Mockito.anyString())).thenReturn("testUser");
        when(serverValidationUtils.accountValidation(Mockito.anyString())).thenReturn(account);
        when(serverValidationUtils.userAccessDetailsValidation(Mockito.anyString(), any())).thenReturn(new UserAccessDetails());
        when(serverValidationUtils.serviceValidation(anyString(), anyString(), anyString(), any())).thenReturn(service);

        UtilityBean<String> result = getServiceAnomalyConfigBL.serverValidation(utilityBean);

        assertNotNull(result);
        assertInstanceOf(Service.class, result.getMetadata().get(Constants.SERVICE));
        assertInstanceOf(Account.class, result.getMetadata().get(Constants.ACCOUNT));
        assertEquals("testUser", result.getMetadata().get(Constants.USER_ID));
    }

    @Test
    void process_ValidInput_AddAndReturnsServiceConfigDetails() throws Exception {
        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.AUTH_KEY, "authKey");
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");
        String userId = "userId";
        String authKey = "authKey";
        Account accountBean = Account.builder().id(1).identifier("accountIdentifier").build();
        Service service = Service.builder().id(2).identifier("serviceIdentifier").build();

        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.ACCOUNT, accountBean);
        metadata.put(Constants.SERVICE, service);

        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .requestParams(requestParamsMap)
                .metadata(metadata)
                .build();

        List<ServiceSuppPersistenceConfigurationBean> serviceBeanList = new ArrayList<>();
        serviceBeanList.add(ServiceSuppPersistenceConfigurationBean.builder()
                .id(1)
                .startCollectionInterval(60)
                .lowPersistence(10)
                .lowSuppression(20)
                .mediumPersistence(30)
                .mediumSuppression(40)
                .highPersistence(50)
                .highSuppression(60)
                .highEnable(true)
                .mediumEnable(true)
                .lowEnable(true)
                .build());

        when(commonUtils.getUserId(authKey)).thenReturn(userId);
        when(serviceConfigurationDao.getServiceConfiguration(anyInt(), anyInt())).thenReturn(null).thenReturn(serviceBeanList);
        when(serviceConfigurationDao.addServiceConfiguration(anyList())).thenReturn(new int[]{1, 2});

        Map<String, ServiceSuppPersistenceConfigPojo> result = getServiceAnomalyConfigBL.process(utilityBean);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(Constants.OPERATOR_LESS_THAN));
    }

    @Test
    void process_ValidInput_ReturnsServiceConfigDetails() throws Exception {
        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.AUTH_KEY, "authKey");
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, "accountIdentifier");
        requestParamsMap.put(Constants.SERVICE_IDENTIFIER, "serviceIdentifier");
        String userId = "userId";
        String authKey = "authKey";
        Account accountBean = Account.builder().id(1).identifier("accountIdentifier").build();
        Service service = Service.builder().id(2).identifier("serviceIdentifier").build();

        HashMap<String, Object> metadata = new HashMap<>();
        metadata.put(Constants.ACCOUNT, accountBean);
        metadata.put(Constants.SERVICE, service);

        UtilityBean<String> utilityBean = UtilityBean.<String>builder()
                .requestParams(requestParamsMap)
                .metadata(metadata)
                .build();

        List<ServiceSuppPersistenceConfigurationBean> serviceBeanList = new ArrayList<>();
        serviceBeanList.add(ServiceSuppPersistenceConfigurationBean.builder()
                .id(1)
                .startCollectionInterval(60)
                .lowPersistence(10)
                .lowSuppression(20)
                .mediumPersistence(30)
                .mediumSuppression(40)
                .highPersistence(50)
                .highSuppression(60)
                .highEnable(true)
                .mediumEnable(true)
                .lowEnable(true)
                .build());

        when(commonUtils.getUserId(authKey)).thenReturn(userId);
        when(serviceConfigurationDao.getServiceConfiguration(anyInt(), anyInt())).thenReturn(serviceBeanList);

        Map<String, ServiceSuppPersistenceConfigPojo> result = getServiceAnomalyConfigBL.process(utilityBean);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(Constants.OPERATOR_GREATER_THAN));
        assertEquals(20, result.get(Constants.OPERATOR_GREATER_THAN).getLowSuppression());
    }

}
