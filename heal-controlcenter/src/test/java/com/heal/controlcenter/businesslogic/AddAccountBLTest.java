package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.MasterDataDao;
import com.heal.controlcenter.dao.mysql.NotificationsDao;
import com.heal.controlcenter.dao.mysql.TagsDao;
import com.heal.controlcenter.dao.redis.AccountRepo;
import com.heal.controlcenter.dao.redis.NotificationRepo;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.TagMappingDetails;
import com.heal.controlcenter.pojo.Tags;
import com.heal.controlcenter.pojo.ThresholdSeverity;
import com.heal.controlcenter.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.security.KeyPair;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.anyList;

/**
 * Unit tests for AddAccountBL class, focusing on client-side, server-side, and process logic.
 */
@ExtendWith(MockitoExtension.class)
class AddAccountBLTest {

    @InjectMocks
    AddAccountBL addAccountBL;

    @Mock
    ClientValidationUtils clientValidationUtils;
    @Mock
    AccountsDao accountsDao;
    @Mock
    ServerValidationUtils serverValidationUtils;
    @Mock
    MasterDataDao masterDataDao;
    @Mock
    DateTimeUtil dateTimeUtil;
    @Mock
    TagsDao tagsDao;
    @Mock
    AccountRepo accountRepo;
    @Mock
    NotificationRepo notificationRepo;
    @Mock
    GetNotificationSettingsBL getNotificationSettingsBL;
    @Mock
    NotificationsDao notificationsDao;

    private final String AUTH_TOKEN = "valid-auth-token";
    private final String ACCOUNT_IDENTIFIER = "test-account-id";
    private final String USER_ID = "user-001";
    private final String ACCOUNT_NAME = "Test Account Name";
    private final int ACCOUNT_ID = 123;
    // Use ISO-8601 format for Instant, then convert to Timestamp for mocking
    private final String MOCKED_CREATED_TIME_ISO = "2024-06-22T10:00:00Z";
    private final String MOCKED_PRIVATE_KEY = "mocked-private-key";
    private final String MOCKED_PUBLIC_KEY = "mocked-public-key";
    private final String TIMEZONE_IDENTIFIER = "Asia/Kolkata";
    private final int TIMEZONE_TAG_ID = 1; // This ID is for TagDetailsBean.id
    private final long TIMEZONE_OFFSET = 19800000L;

    private Account validAccount;
    private UtilityBean<Account> validUtilityBean;

    @BeforeEach
    void setUp() {
        validAccount = Account.builder()
                .accountName(ACCOUNT_NAME)
                .identifier(ACCOUNT_IDENTIFIER)
                .status(1)
                .tags(List.of(Tags.builder().name(Constants.TIME_ZONE_TAG).identifier(TIMEZONE_IDENTIFIER).build()))
                .thresholdSeverity(ThresholdSeverity.builder().low(true).warning(true).critical(true).build())
                .closingWindow(10)
                .maxDataBreaks(2)
                .build();

        HashMap<String, String> requestParamsMap = new HashMap<>();
        requestParamsMap.put(Constants.AUTH_KEY, AUTH_TOKEN);
        requestParamsMap.put(Constants.ACCOUNT_IDENTIFIER, ACCOUNT_IDENTIFIER);

        validUtilityBean = UtilityBean.<Account>builder()
                .pojoObject(validAccount)
                .requestParams(requestParamsMap)
                .metadata(Map.of(Constants.USER_ID, USER_ID))
                .build();
    }

    /**
     * Tests successful client validation when all inputs (auth token, account identifier, and request body) are valid.
     * Expects a UtilityBean to be returned without any exceptions.
     */
    @Test
    void testClientValidation_success() throws ClientException {
        doNothing().when(clientValidationUtils).authKeyValidation(AUTH_TOKEN);
        doNothing().when(clientValidationUtils).accountIdentifierValidation(ACCOUNT_IDENTIFIER);

        UtilityBean<Account> result = addAccountBL.clientValidation(validAccount, AUTH_TOKEN, ACCOUNT_IDENTIFIER);

        assertNotNull(result);
        assertEquals(validAccount, result.getPojoObject());
        assertEquals(AUTH_TOKEN, result.getRequestParams().get(Constants.AUTH_KEY));
        assertEquals(ACCOUNT_IDENTIFIER, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));

        verify(clientValidationUtils, times(1)).authKeyValidation(AUTH_TOKEN);
        verify(clientValidationUtils, times(1)).accountIdentifierValidation(ACCOUNT_IDENTIFIER);
    }

    /**
     * Tests client validation when the authorization token is invalid (e.g., null or empty).
     * Expects a ClientException to be thrown due to auth key validation failure.
     */
    @Test
    void testClientValidation_authKeyInvalid_throwsClientException() throws ClientException {
        String invalidAuthToken = "";

        doThrow(new ClientException(UIMessages.AUTH_KEY_INVALID))
                .when(clientValidationUtils).authKeyValidation(invalidAuthToken);

        ClientException exception = assertThrows(ClientException.class,
                () -> addAccountBL.clientValidation(validAccount, invalidAuthToken, ACCOUNT_IDENTIFIER));

        assertEquals("ClientException : " + UIMessages.AUTH_KEY_INVALID, exception.getMessage());

        verify(clientValidationUtils, times(1)).authKeyValidation(invalidAuthToken);
        verify(clientValidationUtils, never()).accountIdentifierValidation(anyString());
    }

    /**
     * Tests client validation when the account name in the request body is invalid (e.g., too short).
     * Expects a ClientException to be thrown because `account.validate()` returns errors.
     */
    @Test
    void testClientValidation_invalidAccountNameInRequestBody_throwsClientException() throws ClientException {
        Account accountWithInvalidName = Account.builder()
                .accountName("A") // Invalid account name (too short)
                .identifier(ACCOUNT_IDENTIFIER)
                .status(1)
                .tags(List.of(Tags.builder().name(Constants.TIME_ZONE_TAG).identifier("Asia/Kolkata").build()))
                .thresholdSeverity(ThresholdSeverity.builder().low(true).warning(true).critical(true).build())
                .closingWindow(10)
                .maxDataBreaks(2)
                .build();

        Account spyAccount = spy(accountWithInvalidName);

        Map<String, String> errors = new HashMap<>();
        errors.put("accountName", "Account name must be between 2 and 32 characters.");
        doReturn(errors).when(spyAccount).validate();

        doNothing().when(clientValidationUtils).authKeyValidation(AUTH_TOKEN);
        doNothing().when(clientValidationUtils).accountIdentifierValidation(ACCOUNT_IDENTIFIER);

        ClientException exception = assertThrows(ClientException.class,
                () -> addAccountBL.clientValidation(spyAccount, AUTH_TOKEN, ACCOUNT_IDENTIFIER));

        assertTrue(exception.getMessage().contains("accountName"));
        assertTrue(exception.getMessage().contains("Account name must be between 2 and 32 characters."));

        verify(clientValidationUtils, times(1)).authKeyValidation(AUTH_TOKEN);
        verify(clientValidationUtils, times(1)).accountIdentifierValidation(ACCOUNT_IDENTIFIER);
        verify(spyAccount, times(1)).validate();
    }

    /**
     * Tests successful server validation when the auth key is valid and the account identifier is unique.
     * Expects a UtilityBean with the user ID in metadata.
     */
    @Test
    void testServerValidation_success() throws ServerException {
        UtilityBean<Account> utilityBean = UtilityBean.<Account>builder()
                .pojoObject(validAccount)
                .requestParams(new HashMap<>(Map.of(Constants.AUTH_KEY, AUTH_TOKEN, Constants.ACCOUNT_IDENTIFIER, ACCOUNT_IDENTIFIER)))
                .build();

        when(serverValidationUtils.authKeyValidation(AUTH_TOKEN)).thenReturn(USER_ID);
        when(accountsDao.existsByIdentifier(ACCOUNT_IDENTIFIER.trim())).thenReturn(false);

        UtilityBean<Account> result = addAccountBL.serverValidation(utilityBean);

        assertNotNull(result);
        assertEquals(validAccount, result.getPojoObject());
        assertEquals(AUTH_TOKEN, result.getRequestParams().get(Constants.AUTH_KEY));
        assertEquals(ACCOUNT_IDENTIFIER, result.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
        assertEquals(USER_ID, result.getMetadata().get(Constants.USER_ID));

        verify(serverValidationUtils, times(1)).authKeyValidation(AUTH_TOKEN);
        verify(accountsDao, times(1)).existsByIdentifier(ACCOUNT_IDENTIFIER.trim());
    }

    /**
     * Tests server validation when the auth key validation fails.
     * Expects a ServerException to be thrown, propagated from serverValidationUtils.
     */
    @Test
    void testServerValidation_authKeyValidationFails_throwsServerException() throws ServerException {
        UtilityBean<Account> utilityBean = UtilityBean.<Account>builder()
                .pojoObject(validAccount)
                .requestParams(new HashMap<>(Map.of(Constants.AUTH_KEY, "invalid-token", Constants.ACCOUNT_IDENTIFIER, ACCOUNT_IDENTIFIER)))
                .build();

        doThrow(new ServerException(UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE))
                .when(serverValidationUtils).authKeyValidation("invalid-token");

        ServerException exception = assertThrows(ServerException.class,
                () -> addAccountBL.serverValidation(utilityBean));

        assertEquals("ServerException : " + UIMessages.USERID_EXTRACTION_EXCEPTION_MESSAGE, exception.getMessage());

        verify(serverValidationUtils, times(1)).authKeyValidation("invalid-token");
        verify(accountsDao, never()).existsByIdentifier(anyString());
    }

    /**
     * Tests server validation when the account identifier already exists in the database.
     * Expects a ServerException with a specific message.
     */
    @Test
    void testServerValidation_accountIdentifierAlreadyExists_throwsServerException() throws ServerException {
        UtilityBean<Account> utilityBean = UtilityBean.<Account>builder()
                .pojoObject(validAccount)
                .requestParams(new HashMap<>(Map.of(Constants.AUTH_KEY, AUTH_TOKEN, Constants.ACCOUNT_IDENTIFIER, ACCOUNT_IDENTIFIER)))
                .build();

        when(serverValidationUtils.authKeyValidation(AUTH_TOKEN)).thenReturn(USER_ID);
        when(accountsDao.existsByIdentifier(ACCOUNT_IDENTIFIER.trim())).thenReturn(true);

        ServerException exception = assertThrows(ServerException.class,
                () -> addAccountBL.serverValidation(utilityBean));

        assertEquals("ServerException : Account identifier already in use, kindly use another identifier.", exception.getMessage());

        verify(serverValidationUtils, times(1)).authKeyValidation(AUTH_TOKEN);
        verify(accountsDao, times(1)).existsByIdentifier(ACCOUNT_IDENTIFIER.trim());
    }

    /**
     * Tests the successful execution of the process method, covering all major steps.
     * Verifies that the account is added, configurations are inserted, and Redis/notification updates occur.
     */
    @Test
    void testProcess_success() throws DataProcessingException, ControlCenterException, NoSuchAlgorithmException {
        try (MockedStatic<KeyGenerator> mockedKeyGenerator = mockStatic(KeyGenerator.class)) {
            KeyPair mockKeyPair = mock(KeyPair.class);
            mockedKeyGenerator.when(KeyGenerator::generateKeys).thenReturn(mockKeyPair);
            mockedKeyGenerator.when(() -> KeyGenerator.getPrivateKey(null, mockKeyPair)).thenReturn(MOCKED_PRIVATE_KEY);
            mockedKeyGenerator.when(() -> KeyGenerator.getPublicKey(null, mockKeyPair)).thenReturn(MOCKED_PUBLIC_KEY);

            // Mock DateTimeUtil to return a java.sql.Timestamp
            when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.from(Instant.parse(MOCKED_CREATED_TIME_ISO)));

            // Mock AccountsDao
            when(accountsDao.getAccountByName(ACCOUNT_NAME)).thenReturn(0);
            when(accountsDao.addAccount(any(AccountBean.class))).thenReturn(ACCOUNT_ID);

            // Mock MasterDataDao
            TimezoneBean timezoneBean = new TimezoneBean();
            timezoneBean.setId(TIMEZONE_TAG_ID); // This ID is for TimezoneBean.id
            timezoneBean.setTimeZoneId(TIMEZONE_IDENTIFIER);
            timezoneBean.setOffset(TIMEZONE_OFFSET);
            when(masterDataDao.getTimeZoneWithId(TIMEZONE_IDENTIFIER)).thenReturn(timezoneBean);

            // tagsDao.getTagDetailsByName returns TagDetailsBean, which has an 'id'
            TagDetailsBean mockTagDetailsBean = mock(TagDetailsBean.class);
            when(mockTagDetailsBean.getId()).thenReturn(TIMEZONE_TAG_ID);
            when(tagsDao.getTagDetailsByName(Constants.TIME_ZONE_TAG)).thenReturn(mockTagDetailsBean);
            when(tagsDao.addTagMappingDetails(any(TagMappingDetails.class))).thenReturn(1);

            when(accountRepo.getAccounts()).thenReturn(new ArrayList<>());
            doNothing().when(accountRepo).updateAccount(anyString(), any(com.heal.configuration.pojos.Account.class));
            doNothing().when(accountRepo).updateAccounts(anyList());

            when(notificationsDao.getEscalationSettingsForAccount(anyInt())).thenReturn(Collections.emptyList());
            doNothing().when(notificationRepo).updateNotificationSettingsInRedis(anyString(), anyList());

            Account result = addAccountBL.process(validUtilityBean);

            assertNotNull(result);

            verify(accountsDao, times(1)).getAccountByName(ACCOUNT_NAME);
            verify(dateTimeUtil, times(1)).getCurrentTimestampInGMT();
            verify(accountsDao, times(1)).addAccount(any(AccountBean.class));
            verify(accountsDao, times(1)).insertAccountAnomalyConfigurations(eq(ACCOUNT_ID), any(AccountAnomalyConfigurationBean.class), eq(USER_ID));
            verify(masterDataDao, times(1)).getTimeZoneWithId(TIMEZONE_IDENTIFIER);
            verify(tagsDao, times(1)).getTagDetailsByName(Constants.TIME_ZONE_TAG);
            verify(tagsDao, times(1)).addTagMappingDetails(any(TagMappingDetails.class));
            verify(accountRepo, times(1)).updateAccount(eq(ACCOUNT_IDENTIFIER), any(com.heal.configuration.pojos.Account.class));
            verify(accountRepo, times(1)).getAccounts();
            verify(accountRepo, times(1)).updateAccounts(anyList());
            verify(getNotificationSettingsBL, times(1)).addDefaultNotificationSettings(eq(ACCOUNT_ID), eq(USER_ID));
            verify(notificationsDao, times(1)).getEscalationSettingsForAccount(eq(ACCOUNT_ID));
        }
    }

    /**
     * Tests the scenario where an account with the same name already exists.
     * Expects a DataProcessingException due to ControlCenterException being thrown.
     */
    @Test
    void testProcess_accountNameAlreadyExists_throwsDataProcessingException() throws ControlCenterException, NoSuchAlgorithmException {
        when(accountsDao.getAccountByName(ACCOUNT_NAME)).thenReturn(1);

        DataProcessingException exception = assertThrows(DataProcessingException.class,
                () -> addAccountBL.process(validUtilityBean));

        assertEquals(String.format("DataProcessingException : Account name [Test Account Name] already exists.", ACCOUNT_NAME), exception.getMessage());

        verify(accountsDao, times(1)).getAccountByName(ACCOUNT_NAME);
        verifyNoMoreInteractions(dateTimeUtil, accountsDao, masterDataDao, tagsDao, accountRepo, notificationRepo, getNotificationSettingsBL, notificationsDao);
    }

    /**
     * Tests the scenario where `dateTimeUtil.getCurrentTimestampInGMT()` returns null.
     * Expects a DataProcessingException.
     */
    @Test
    void testProcess_getCurrentTimestampInGMT_returnsNull_throwsDataProcessingException() throws ControlCenterException, NoSuchAlgorithmException {
        try (MockedStatic<KeyGenerator> mockedKeyGenerator = mockStatic(KeyGenerator.class)) {
            KeyPair mockKeyPair = mock(KeyPair.class);
            mockedKeyGenerator.when(KeyGenerator::generateKeys).thenReturn(mockKeyPair);
            mockedKeyGenerator.when(() -> KeyGenerator.getPrivateKey(null, mockKeyPair)).thenReturn(MOCKED_PRIVATE_KEY);
            mockedKeyGenerator.when(() -> KeyGenerator.getPublicKey(null, mockKeyPair)).thenReturn(MOCKED_PUBLIC_KEY);

            when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(null); // Simulate null timestamp

            when(accountsDao.getAccountByName(ACCOUNT_NAME)).thenReturn(0);

            DataProcessingException exception = assertThrows(DataProcessingException.class,
                    () -> addAccountBL.process(validUtilityBean));

            assertEquals("DataProcessingException : Cannot invoke \"java.sql.Timestamp.toString()\" because the return value of \"com.heal.controlcenter.util.DateTimeUtil.getCurrentTimestampInGMT()\" is null", exception.getMessage());

            verify(accountsDao, times(1)).getAccountByName(ACCOUNT_NAME);
            verify(dateTimeUtil, times(1)).getCurrentTimestampInGMT();
            verifyNoMoreInteractions(accountsDao, masterDataDao, tagsDao, accountRepo, notificationRepo, getNotificationSettingsBL, notificationsDao);
        }
    }

    /**
     * Tests the scenario where fetching timezone details throws a ControlCenterException.
     * This covers the catch block for masterDataDao.getTimeZoneWithId.
     * Expects a DataProcessingException.
     */
    @Test
    void testProcess_getTimeZoneWithId_throwsControlCenterException_throwsDataProcessingException() throws ControlCenterException, NoSuchAlgorithmException {
        try (MockedStatic<KeyGenerator> mockedKeyGenerator = mockStatic(KeyGenerator.class)) {
            KeyPair mockKeyPair = mock(KeyPair.class);
            mockedKeyGenerator.when(KeyGenerator::generateKeys).thenReturn(mockKeyPair);
            mockedKeyGenerator.when(() -> KeyGenerator.getPrivateKey(null, mockKeyPair)).thenReturn(MOCKED_PRIVATE_KEY);
            mockedKeyGenerator.when(() -> KeyGenerator.getPublicKey(null, mockKeyPair)).thenReturn(MOCKED_PUBLIC_KEY);

            when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.from(Instant.parse(MOCKED_CREATED_TIME_ISO)));

            when(accountsDao.getAccountByName(ACCOUNT_NAME)).thenReturn(0);
            when(accountsDao.addAccount(any(AccountBean.class))).thenReturn(ACCOUNT_ID);

            doThrow(new ControlCenterException("Timezone not found")).when(masterDataDao).getTimeZoneWithId(TIMEZONE_IDENTIFIER);

            DataProcessingException exception = assertThrows(DataProcessingException.class,
                    () -> addAccountBL.process(validUtilityBean));

            assertEquals("DataProcessingException : Timezone not found", exception.getMessage());

            verify(accountsDao, times(1)).getAccountByName(ACCOUNT_NAME);
            verify(dateTimeUtil, times(1)).getCurrentTimestampInGMT();
            verify(accountsDao, times(1)).addAccount(any(AccountBean.class));
            verify(accountsDao, times(1)).insertAccountAnomalyConfigurations(eq(ACCOUNT_ID), any(AccountAnomalyConfigurationBean.class), eq(USER_ID));
            verify(masterDataDao, times(1)).getTimeZoneWithId(TIMEZONE_IDENTIFIER);
            verifyNoMoreInteractions(tagsDao, accountRepo, notificationRepo, getNotificationSettingsBL, notificationsDao);
        }
    }

    /**
     * Tests the scenario where `tagsDao.addTagMappingDetails` throws a ControlCenterException.
     * This covers the inner catch block for tag mapping.
     * Expects a DataProcessingException.
     */
    @Test
    void testProcess_addTagMappingDetails_throwsControlCenterException_throwsDataProcessingException() throws DataProcessingException, ControlCenterException, NoSuchAlgorithmException {
        try (MockedStatic<KeyGenerator> mockedKeyGenerator = mockStatic(KeyGenerator.class)) {
            KeyPair mockKeyPair = mock(KeyPair.class);
            mockedKeyGenerator.when(KeyGenerator::generateKeys).thenReturn(mockKeyPair);
            mockedKeyGenerator.when(() -> KeyGenerator.getPrivateKey(null, mockKeyPair)).thenReturn(MOCKED_PRIVATE_KEY);
            mockedKeyGenerator.when(() -> KeyGenerator.getPublicKey(null, mockKeyPair)).thenReturn(MOCKED_PUBLIC_KEY);

            when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.from(Instant.parse(MOCKED_CREATED_TIME_ISO)));

            when(accountsDao.getAccountByName(ACCOUNT_NAME)).thenReturn(0);
            when(accountsDao.addAccount(any(AccountBean.class))).thenReturn(ACCOUNT_ID);

            TimezoneBean timezoneBean = new TimezoneBean();
            timezoneBean.setId(TIMEZONE_TAG_ID);
            timezoneBean.setTimeZoneId(TIMEZONE_IDENTIFIER);
            timezoneBean.setOffset(TIMEZONE_OFFSET);
            when(masterDataDao.getTimeZoneWithId(TIMEZONE_IDENTIFIER)).thenReturn(timezoneBean);

            TagDetailsBean mockTagDetailsBean = mock(TagDetailsBean.class);
            when(mockTagDetailsBean.getId()).thenReturn(TIMEZONE_TAG_ID);
            when(tagsDao.getTagDetailsByName(Constants.TIME_ZONE_TAG)).thenReturn(mockTagDetailsBean);
            doThrow(new ControlCenterException("Tag mapping DB error")).when(tagsDao).addTagMappingDetails(any(TagMappingDetails.class));

            DataProcessingException exception = assertThrows(DataProcessingException.class,
                    () -> addAccountBL.process(validUtilityBean));

            assertEquals("DataProcessingException : DataProcessingException : Tag mapping DB error", exception.getMessage());

            verify(accountsDao, times(1)).getAccountByName(ACCOUNT_NAME);
            verify(dateTimeUtil, times(1)).getCurrentTimestampInGMT();
            verify(accountsDao, times(1)).addAccount(any(AccountBean.class));
            verify(accountsDao, times(1)).insertAccountAnomalyConfigurations(eq(ACCOUNT_ID), any(AccountAnomalyConfigurationBean.class), eq(USER_ID));
            verify(masterDataDao, times(1)).getTimeZoneWithId(TIMEZONE_IDENTIFIER);
            verify(tagsDao, times(1)).getTagDetailsByName(Constants.TIME_ZONE_TAG);
            verify(tagsDao, times(1)).addTagMappingDetails(any(TagMappingDetails.class));
            verifyNoMoreInteractions(accountRepo, notificationRepo, getNotificationSettingsBL, notificationsDao);
        }
    }

    /**
     * Tests the scenario where timezone mapping fails (e.g., addTagMappingDetails returns 0).
     * Expects a DataProcessingException.
     */
    @Test
    void testProcess_timezoneMappingFails_throwsDataProcessingException() throws DataProcessingException, ControlCenterException, NoSuchAlgorithmException {
        try (MockedStatic<KeyGenerator> mockedKeyGenerator = mockStatic(KeyGenerator.class)) {
            KeyPair mockKeyPair = mock(KeyPair.class);
            mockedKeyGenerator.when(KeyGenerator::generateKeys).thenReturn(mockKeyPair);
            mockedKeyGenerator.when(() -> KeyGenerator.getPrivateKey(null, mockKeyPair)).thenReturn(MOCKED_PRIVATE_KEY);
            mockedKeyGenerator.when(() -> KeyGenerator.getPublicKey(null, mockKeyPair)).thenReturn(MOCKED_PUBLIC_KEY);

            when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.from(Instant.parse(MOCKED_CREATED_TIME_ISO)));

            when(accountsDao.getAccountByName(ACCOUNT_NAME)).thenReturn(0);
            when(accountsDao.addAccount(any(AccountBean.class))).thenReturn(ACCOUNT_ID);

            TimezoneBean timezoneBean = new TimezoneBean();
            timezoneBean.setId(TIMEZONE_TAG_ID);
            timezoneBean.setTimeZoneId(TIMEZONE_IDENTIFIER);
            timezoneBean.setOffset(TIMEZONE_OFFSET);
            when(masterDataDao.getTimeZoneWithId(TIMEZONE_IDENTIFIER)).thenReturn(timezoneBean);

            TagDetailsBean mockTagDetailsBean = mock(TagDetailsBean.class);
            when(mockTagDetailsBean.getId()).thenReturn(TIMEZONE_TAG_ID);
            when(tagsDao.getTagDetailsByName(Constants.TIME_ZONE_TAG)).thenReturn(mockTagDetailsBean);
            when(tagsDao.addTagMappingDetails(any(TagMappingDetails.class))).thenReturn(0); // Simulate failure here

            DataProcessingException exception = assertThrows(DataProcessingException.class,
                    () -> addAccountBL.process(validUtilityBean));

            assertEquals("DataProcessingException : DataProcessingException : Timezone mapping to application failed", exception.getMessage());

            verify(accountsDao, times(1)).getAccountByName(ACCOUNT_NAME);
            verify(dateTimeUtil, times(1)).getCurrentTimestampInGMT();
            verify(accountsDao, times(1)).addAccount(any(AccountBean.class));
            verify(accountsDao, times(1)).insertAccountAnomalyConfigurations(eq(ACCOUNT_ID), any(AccountAnomalyConfigurationBean.class), eq(USER_ID));
            verify(masterDataDao, times(1)).getTimeZoneWithId(TIMEZONE_IDENTIFIER);
            verify(tagsDao, times(1)).getTagDetailsByName(Constants.TIME_ZONE_TAG);
            verify(tagsDao, times(1)).addTagMappingDetails(any(TagMappingDetails.class));

            verifyNoMoreInteractions(accountRepo, notificationRepo, getNotificationSettingsBL, notificationsDao);
        }
    }

    /**
     * Tests the scenario where `accountRepo.getAccounts()` returns a non-empty list,
     * covering the `else` branch of `if (existingAccountList.isEmpty())`.
     * Expects successful processing.
     */
    @Test
    void testProcess_existingAccountListIsNotEmpty_success() throws DataProcessingException, ControlCenterException, NoSuchAlgorithmException {
        try (MockedStatic<KeyGenerator> mockedKeyGenerator = mockStatic(KeyGenerator.class)) {
            KeyPair mockKeyPair = mock(KeyPair.class);
            mockedKeyGenerator.when(KeyGenerator::generateKeys).thenReturn(mockKeyPair);
            mockedKeyGenerator.when(() -> KeyGenerator.getPrivateKey(null, mockKeyPair)).thenReturn(MOCKED_PRIVATE_KEY);
            mockedKeyGenerator.when(() -> KeyGenerator.getPublicKey(null, mockKeyPair)).thenReturn(MOCKED_PUBLIC_KEY);

            when(dateTimeUtil.getCurrentTimestampInGMT()).thenReturn(Timestamp.from(Instant.parse(MOCKED_CREATED_TIME_ISO)));

            when(accountsDao.getAccountByName(ACCOUNT_NAME)).thenReturn(0);
            when(accountsDao.addAccount(any(AccountBean.class))).thenReturn(ACCOUNT_ID);

            TimezoneBean timezoneBean = new TimezoneBean();
            timezoneBean.setId(TIMEZONE_TAG_ID);
            timezoneBean.setTimeZoneId(TIMEZONE_IDENTIFIER);
            timezoneBean.setOffset(TIMEZONE_OFFSET);
            when(masterDataDao.getTimeZoneWithId(TIMEZONE_IDENTIFIER)).thenReturn(timezoneBean);

            TagDetailsBean mockTagDetailsBean = mock(TagDetailsBean.class);
            when(mockTagDetailsBean.getId()).thenReturn(TIMEZONE_TAG_ID);
            when(tagsDao.getTagDetailsByName(Constants.TIME_ZONE_TAG)).thenReturn(mockTagDetailsBean);
            when(tagsDao.addTagMappingDetails(any(TagMappingDetails.class))).thenReturn(1);

            List<com.heal.configuration.pojos.Account> existingAccounts = new ArrayList<>();
            existingAccounts.add(com.heal.configuration.pojos.Account.builder().id(999).name("Existing Account").build());
            when(accountRepo.getAccounts()).thenReturn(existingAccounts); // Covers existingAccountList.isEmpty() == false

            doNothing().when(accountRepo).updateAccount(anyString(), any(com.heal.configuration.pojos.Account.class));
            doNothing().when(accountRepo).updateAccounts(anyList());


            when(getNotificationSettingsBL.addDefaultNotificationSettings(anyInt(), anyString())).thenReturn(new int[]{1});
            when(notificationsDao.getEscalationSettingsForAccount(anyInt())).thenReturn(Collections.emptyList());
            doNothing().when(notificationRepo).updateNotificationSettingsInRedis(anyString(), anyList());

            Account result = addAccountBL.process(validUtilityBean);

            assertNotNull(result);

            verify(accountsDao, times(1)).getAccountByName(ACCOUNT_NAME);
            verify(dateTimeUtil, times(1)).getCurrentTimestampInGMT();
            verify(accountsDao, times(1)).addAccount(any(AccountBean.class));
            verify(accountsDao, times(1)).insertAccountAnomalyConfigurations(eq(ACCOUNT_ID), any(AccountAnomalyConfigurationBean.class), eq(USER_ID));
            verify(masterDataDao, times(1)).getTimeZoneWithId(TIMEZONE_IDENTIFIER);
            verify(tagsDao, times(1)).getTagDetailsByName(Constants.TIME_ZONE_TAG);
            verify(tagsDao, times(1)).addTagMappingDetails(any(TagMappingDetails.class));
            verify(accountRepo, times(1)).updateAccount(eq(ACCOUNT_IDENTIFIER), any(com.heal.configuration.pojos.Account.class));
            verify(accountRepo, times(1)).getAccounts();
            verify(accountRepo, times(1)).updateAccounts(anyList()); // This will be called with the list including the new account
            verify(getNotificationSettingsBL, times(1)).addDefaultNotificationSettings(eq(ACCOUNT_ID), eq(USER_ID));
            verify(notificationsDao, times(1)).getEscalationSettingsForAccount(eq(ACCOUNT_ID));
            verify(notificationRepo, times(1)).updateNotificationSettingsInRedis(eq(ACCOUNT_IDENTIFIER), anyList());
        }
    }
}