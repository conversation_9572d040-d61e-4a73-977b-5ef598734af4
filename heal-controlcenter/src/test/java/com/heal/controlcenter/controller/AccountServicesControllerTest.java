package com.heal.controlcenter.controller;

import com.heal.controlcenter.beans.AccountServiceValidationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAccountServicesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.pojo.ServiceListPage;
import com.heal.controlcenter.util.JsonFileParser;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AccountServicesControllerTest {

    @Mock
    private GetAccountServicesBL getAccountServicesBL;

    @Mock
    private JsonFileParser headersParser;

    @InjectMocks
    private AccountServiceController accountController;

    @Test
    void testGetAccountServices_Success() throws Exception {
        // Arrange
        String authorization = "Bearer test-token";
        String accountIdentifier = "test-account";
        Pageable pageable = PageRequest.of(0, 10);
        String searchTerm = "test";

        Page<ServiceListPage> mockPage = new PageImpl<>(Collections.singletonList(new ServiceListPage()));
        UtilityBean<Object> clientValidationBean = UtilityBean.builder().build();
        UtilityBean<AccountServiceValidationBean> serverValidationBean = UtilityBean.<AccountServiceValidationBean>builder().build();

        when(getAccountServicesBL.clientValidation(null, authorization, accountIdentifier, searchTerm)).thenReturn(clientValidationBean);
        when(getAccountServicesBL.serverValidation(any(UtilityBean.class))).thenReturn(serverValidationBean);
        when(getAccountServicesBL.process(any(UtilityBean.class))).thenReturn(mockPage);
        when(headersParser.loadHeaderConfiguration()).thenReturn(new HttpHeaders());

  /**    //   Act
        ResponseEntity<ResponsePojo<Page<ServiceListPage>>> response = accountController.getAccountServices(authorization, accountIdentifier, pageable, searchTerm);

        //  Assert
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("success", response.getBody().getMessage());
        assertEquals(mockPage, response.getBody().getData()); **/
    }

    @Test
    void testGetAccountServices_ClientException() throws Exception {
        // Arrange
        String authorization = "Bearer test-token";
        String accountIdentifier = "test-account";
        Pageable pageable = PageRequest.of(0, 10);
        String searchTerm = "test";

        when(getAccountServicesBL.clientValidation(null, authorization, accountIdentifier, searchTerm)).thenThrow(new ClientException("Invalid client data"));

        // Act & Assert
        assertThrows(ClientException.class, () -> {
//            accountController.getAccountServices(authorization, accountIdentifier, pageable, searchTerm);
        });
    }
}
