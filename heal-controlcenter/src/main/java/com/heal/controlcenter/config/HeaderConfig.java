
// In a configuration class, e.g., src/main/java/com/heal/controlcenter/config/HeaderConfig.java
package com.heal.controlcenter.config;

import com.heal.controlcenter.util.Constants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class HeaderConfig {
    @Bean(name = "headerConfigurations")
    public Map<String, String> headerConfigurations() {
        Map<String, String> headers = new HashMap<>();
        // Set security headers
        headers.put(Constants.HEADER_X_FRAME_OPTIONS, Constants.VALUE_DENY);
        headers.put(Constants.HEADER_X_CONTENT_TYPE_OPTIONS, Constants.VALUE_NOSNIFF);
        return headers;
    }
}