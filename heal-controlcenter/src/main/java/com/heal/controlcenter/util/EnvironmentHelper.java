package com.heal.controlcenter.util;

import com.heal.configuration.pojos.ViewTypes;
import com.heal.controlcenter.dao.redis.MasterDataRepo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

public class EnvironmentHelper {
    private static final Logger logger = LoggerFactory.getLogger(EnvironmentHelper.class);
    public MasterDataRepo masterDataRepo;

    public EnvironmentHelper() {
        this.masterDataRepo = new MasterDataRepo();
    }


    public List<ViewTypes> getEnvTypeDetails() {
        List<ViewTypes> typeDetails = masterDataRepo.getTypeDetailsByTypeName(Constants.ENVIRONMENT_TYPE_NAME);

        if (typeDetails == null || typeDetails.isEmpty()) {
            logger.error("Obtained empty results when queried for type details for type {} from Redis", Constants.ENVIRONMENT_TYPE_NAME);
            return Collections.emptyList();
        }

        return typeDetails;
    }

    public String getOrDefaultEnvironmentName(int envId) {
        List<ViewTypes> typeDetailsByTypeName = getEnvTypeDetails();

        if (typeDetailsByTypeName.isEmpty()) {
            logger.warn("Obtained empty results from Redis when queried for Type details for type 'ENVIRONMENT'. Returning default environment 'NONE'");
            return "NONE";
        }

        return typeDetailsByTypeName.stream()
                .filter(f -> f.getSubTypeId() == envId)
                .findAny()
                .map(ViewTypes::getSubTypeName)
                .orElse("NONE");
    }

}
