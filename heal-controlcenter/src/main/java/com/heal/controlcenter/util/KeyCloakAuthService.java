package com.heal.controlcenter.util;

import com.appnomic.appsone.keycloak.KeyCloakSessionValidator;
import com.appnomic.appsone.model.JWTData;
import com.appnomic.appsone.util.KeyCloakConnectionSpec;
import com.appnomic.appsone.util.KeyCloakUtility;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.User;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.dao.redis.UserRepo;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.KeyCloakUserDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
public class KeyCloakAuthService {

    @Autowired
    UserRepo userRepo;
    @Autowired
    UserDao userDao;
    @Autowired
    DateTimeUtil dateTimeUtil;

    private static KeyCloakSessionValidator keyCloakSessionValidator = null;
    private static KeyCloakUtility keyCloakUtility = null;
    private static final ObjectMapper objectMapper = CommonUtils.getObjectMapperWithHtmlEncoder();

    @Value("${keycloak.ip}")
    public String keycloakIp;
    @Value("${keycloak.port}")
    public String keycloakPort;
    @Value("${keycloak.user}")
    public String username;
    @Value("${keycloak.pwd}")
    public String encryptedPassword;

    public void init() {
        try {
            log.debug("Inside Session Validator method");
            KeyCloakConnectionSpec keyCloakConnectionSpec = this.getSpec();
            keyCloakSessionValidator = new KeyCloakSessionValidator(keyCloakConnectionSpec);
            keyCloakUtility = new KeyCloakUtility(keyCloakConnectionSpec);
        } catch (Exception e) {
            log.error("Error occurred while getting key cloak connection spec.", e);
        }
    }

    public KeyCloakConnectionSpec getSpec() {
        if (keycloakIp == null || keycloakPort == null || username == null || encryptedPassword == null) {
            log.error("Missing keycloak specific configuration in conf file.");
            System.exit(-1);
        }

        String decryptedPwd = CommonUtils.getDecryptedData(encryptedPassword);

        log.debug("Inside Session Validator method");
        KeyCloakConnectionSpec keyCloakConnectionSpec = new KeyCloakConnectionSpec()
                .setKeyCloakIP(keycloakIp)
                .setKeyCloakPort(keycloakPort)
                .setKeyCloakUsername(username)
                .setKeyCloakPassword(decryptedPwd)
                .createKeyCloakConnectionSpec();

        log.debug("KeycloakSpec : {}", keyCloakConnectionSpec);
        return keyCloakConnectionSpec;
    }

    public boolean isValidKey(String appToken) {
        try {
            if (keyCloakSessionValidator == null) {
                this.init();
            }
            boolean status = keyCloakSessionValidator.validateJwsToken(appToken);
            log.trace("Status of the token is [{}]", status);
            return status;

        } catch (Exception e) {
            log.error("Invalid token. Reason: ", e);
            return false;
        }
    }

    public JWTData extractUserDetails(String appToken) throws ControlCenterException {
        if (keyCloakUtility == null)
            this.init();

        if (keyCloakUtility == null) {
            throw new ControlCenterException("Unable to initialize the KeyCloak server. Kindly look into the appsone-cc logs.");
        }
        log.trace("Invoked method: extractUserDetails");
        JWTData jwtData = keyCloakUtility.extractUsername(appToken);

        if (jwtData == null) {
            throw new ControlCenterException("Unable to get the username from token. Kindly look into the appsone-cc logs.");
        }

        return jwtData;
    }

    public static KeyCloakUserDetails getKeyCloakUserDetails(String userData) {
        KeyCloakUserDetails keyCloakUserDetails = null;
        try {
            keyCloakUserDetails = objectMapper.readValue(userData, KeyCloakUserDetails.class);
        } catch (IOException e) {
            log.error("Error occurred while reading user details from keycloak service {}", e.getMessage());
            log.debug("trace: ", e);
        }

        return keyCloakUserDetails;
    }

    public KeyCloakUserDetails getKeycloakUserDataFromId(String userIdentifier) {
        KeyCloakUserDetails userData = null;
        if (keyCloakUtility == null) this.init();
        try {
            String userDetailsString = keyCloakUtility.fetchUserDetails(userIdentifier);
            userData = getKeyCloakUserDetails(userDetailsString);
        } catch (Exception e) {
            log.error("Error occurred while fetching username from keycloak: ", e);
        }
        return userData;
    }

    /**
     * Verifies the status of a user based on the provided authentication key.
     * Checks if the user exists, is active, and has a valid profile.
     * Updates the user's last login time in the database and Redis if valid.
     *
     * @param authKey the authentication token for the user
     * @return BasicUserDetails object with user status and details
     */
    public BasicUserDetails verifyUserStatus(String authKey) {
        String userIdentifier;
        User user;
        try {
            userIdentifier = getUserIdentifier(authKey);
            if (userIdentifier == null) {
                log.error("UserIdentifier is null");
                return BasicUserDetails.builder()
                        .userStatus(false)
                        .build();
            }

            user = userRepo.getUser(userIdentifier); //load
            if (user == null || user.getStatus() != 1) {
                log.error("User is inactive or not present");
                return BasicUserDetails.builder()
                        .userStatus(false)
                        .build();
            } else if (user.getProfileId() <= 0) {
                log.error("Invalid profile {} for user {}", user.getProfileId(), userIdentifier);
                return BasicUserDetails.builder()
                        .userStatus(false)
                        .build();
            } else {
                //update user last login time
                String lastLoginTime = dateTimeUtil.getTimeInGMT(System.currentTimeMillis());
                int mysqlUpdateCount = userDao.updateUserLoginTime(userIdentifier, lastLoginTime);
                if (mysqlUpdateCount != 1) {
                    log.error("Couldn't update last login time for user [{}] in percona. Skipping redis update.", userIdentifier);
                    return BasicUserDetails.builder()
                            .userStatus(false)
                            .build();
                } else {
                    userRepo.updateInRedis(user, lastLoginTime);
                }
            }
        } catch (Exception e) {
            log.error("Exception encountered while fetching userIdentifier from auth token. Reason: ", e);
            return BasicUserDetails.builder()
                    .userStatus(false)
                    .build();
        }

        return BasicUserDetails.builder()
                .authToken(authKey)
                .userIdentifier(userIdentifier)
                .userStatus(true)
                .profileId(user.getProfileId())
                .build();
    }

    /**
     * Extracts the user identifier (subject) from the provided authentication token.
     *
     * @param authToken the authentication token (JWT)
     * @return the user identifier if extraction is successful, otherwise null
     */
    public String getUserIdentifier(String authToken) {
        if (authToken == null) {
            return null;
        }

        try {
            JWTData jwtData = extractUserDetails(authToken);
            return jwtData.getSub().trim();
        } catch (Exception e) {
            log.error("Exception while fetching user details from JWT. Details: ", e);
            return null;
        }
    }
}
