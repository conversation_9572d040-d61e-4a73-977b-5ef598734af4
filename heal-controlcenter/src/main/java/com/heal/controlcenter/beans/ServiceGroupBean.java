package com.heal.controlcenter.beans;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * This bean represents a Service Group entity from the database.
 * Its fields directly map to the columns in the 'service_group' table.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceGroupBean {

    private int id;
    private String name;
    private String identifier;
    private int status;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
    private int accountId;
    private Integer serviceGroupId;
}