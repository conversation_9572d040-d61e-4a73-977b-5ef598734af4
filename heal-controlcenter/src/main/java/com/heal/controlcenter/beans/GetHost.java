package com.heal.controlcenter.beans;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetHost {

    private String id;
    private String hostName;
    private String hostIdentifier;
    private List<String> hostAddress;
    private String environment;
    private int componentId;
    private String componentName;
    private int componentVersionId;
    private String componentVersionName;
    private int commonVersionId;
    private String commonVersionName;
    private int componentTypeId;
    private String componentTypeName;
    private List<String> discoveredEntities;
    private DiscoveryStatus status;
    private int newHosts;
    private List<String> newHostIps;
    private String process;
    private String errors;
    private List<IdNamePojo> application;
    private List<IdNamePojo> service;
    private List<AgentDetails> mappedAgents;
    private long lastDiscoveryRunTime;
    private boolean newInstanceDiscovered;

}