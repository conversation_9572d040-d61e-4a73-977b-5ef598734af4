package com.heal.controlcenter.pojo;

import com.heal.controlcenter.beans.ServiceGroupBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServiceGroupListPage {
    private Integer id;
    private String name;
    private String identifier;
    private Set<IdPojo> application;
    private ServiceGroupBean linkedServiceGroup;
    private Map<String, Object> inbound;
    private Map<String, Object> outbound;
    private String createdBy;
    private Long createdOn;
    private String lastModifiedBy;
    private Long lastModifiedOn;
}