package com.heal.controlcenter.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.heal.configuration.pojos.ThresholdConfig;
import com.heal.controlcenter.util.Constants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class StaticThresholdRules {
    private int dataId;
    private String kpiId;
    private int categoryId;
    private String categoryName;
    private String kpiDataType;
    private String kpiUnit;
    private String kpiLevel;
    private String kpiName;
    private String kpiAttribute;
    private ComputedDetails computedDetails;
    private boolean generateAnomaly;
    private boolean userDefinedSOR;
    private ThresholdConfig lowThreshold;
    private ThresholdConfig warningThreshold;
    private ThresholdConfig errorThreshold;
    @JsonIgnore
    private Timestamp startTime;

    private String coverageWindow;

    private Map<String, String> errorMessage = new HashMap<>();
    private static final String OPERATION_TYPE ="operationType";
    private static final String THRESHOLDS_MAP = "thresholds";

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ComputedDetails {
        private String formula;
    }

    public void validate(String kpiType) {
        this.kpiAttribute = Constants.ALL;
        if (null == this.kpiId || this.kpiId.trim().isEmpty() || this.kpiId.trim().length() > 11) {
            errorMessage.put("kpiId", String.format("kpiId [%s] value can not be null or empty.", kpiId));
        }

        validateThresholdApplicability();

        validateThresholdsMap(kpiType);

        validateOperationType(lowThreshold, kpiType);
        validateOperationType(warningThreshold, kpiType);
        validateOperationType(errorThreshold, kpiType);
    }

    private void validateThresholdsMap(String kpiType) {
        if (kpiType.equals(Constants.CORE_KPI_TYPE)) {
            if (lowThreshold == null && warningThreshold == null && errorThreshold == null) {
                errorMessage.put(THRESHOLDS_MAP, "Thresholds are not defined.");
            } else if ((lowThreshold != null && !lowThreshold.validate()) || (warningThreshold != null && !warningThreshold.validate()) || (errorThreshold != null && !errorThreshold.validate())){
                errorMessage.put(THRESHOLDS_MAP, "Thresholds are invalid. Min value should be less than max value.");
            }
        }
    }

    private void validateOperationType(ThresholdConfig thresholdConfig, String kpiType) {
        if (kpiType.equals(Constants.CORE_KPI_TYPE) && thresholdConfig != null) {
            validOperation(thresholdConfig.getOperationType());
        }
    }

    private void validOperation(String operationType) {
        if (null == operationType || operationType.trim().isEmpty()) {
            errorMessage.put(OPERATION_TYPE, "operationType should not be empty or null");
        } else {
            OperationTypeEnum findByType = OperationTypeEnum.findByType(operationType.trim());
            if (findByType == null) {
                errorMessage.put(OPERATION_TYPE, "Invalid operation Type value");
            } else {
                if (!(OperationTypeEnum.NOT_BETWEEN.getType().equalsIgnoreCase(operationType)
                        || OperationTypeEnum.LESS_THAN.getType().equalsIgnoreCase(operationType)
                        || OperationTypeEnum.GREATER.getType().equalsIgnoreCase(operationType))) {
                    errorMessage.put(OPERATION_TYPE, "Invalid operation type.");
                }
            }
        }
    }

    private void validateThresholdApplicability() {
        if (null == this.kpiLevel || this.kpiLevel.trim().isEmpty()) {
            errorMessage.put("applicableTo", "applicableTo value can not be null or empty.");
        } else {
            try {
                Applicable.valueOf(kpiLevel.trim());
            } catch (IllegalArgumentException e) {
                errorMessage.put("applicableTo", "Invalid applicable value");
            }
        }
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof StaticThresholdRules)) return false;
        StaticThresholdRules that = (StaticThresholdRules) o;
        return kpiId.equals(that.kpiId) &&
                kpiLevel.equals(that.kpiLevel);
    }

    @Override
    public int hashCode() {
        return Objects.hash(kpiId, kpiLevel);
    }
}
