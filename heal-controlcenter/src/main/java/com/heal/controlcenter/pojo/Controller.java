package com.heal.controlcenter.pojo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Controller {

    private String appId;
    private String name;
    private String identifier;
    private int status;
    private int accountId;
    private int controllerTypeId;
    private long timeOffset;
    private String createdBY;
    private String createdOn;
    private String updatedTime;
    private List<ServiceConfig> serviceDetails = new ArrayList<>();
    private List<Integer> percentiles = new ArrayList<>();
    private int pluginSuppressionInterval;
    private boolean pluginWhitelisted;
    private String environment;
}
