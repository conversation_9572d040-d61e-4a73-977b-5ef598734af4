package com.heal.controlcenter.pojo;

import com.appnomic.appsone.common.enums.DiscoveryStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InstanceServiceApplicationDetailsPojo {

    private int instanceId;
    private String instanceName;
    private String instanceIdentifier;
    private String hostAddress;
    private int isDR;
    private int discovery;
    private DiscoveryStatus status;
    private String lastDiscoveryRunTime;
    private int componentId;
    private String componentName;
    private int componentVersionId;
    private String componentVersionName;
    private int commonVersionId;
    private String commonVersionName;
    private int componentTypeId;
    private String componentTypeName;
    private int clusterId;
    private String clusterName;
    private String clusterIdentifier;
    private int serviceId;
    private String serviceName;
    private String serviceIdentifier;
    private int applicationId;
    private String applicationName;
    private String applicationIdentifier;

    //Below fields only needed for component instances
    private int hostId;
    private String hostName;
    private String hostIdentifier;
    private String attributeName;
    private String attributeValue;
    private DiscoveryStatus hostStatus;
}
