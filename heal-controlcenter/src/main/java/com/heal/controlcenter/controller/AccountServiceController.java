package com.heal.controlcenter.controller;

import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.AccountServiceValidationBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetAccountServicesBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.heal.configuration.pojos.IdPojo;
import com.heal.configuration.pojos.ServiceConfiguration;
import com.heal.controlcenter.beans.ServiceBean;
import com.heal.controlcenter.businesslogic.AddAccountServiceBL;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Configuration
@RestController
public class AccountServiceController {

    @Autowired
    GetAccountServicesBL getAccountServicesBL;
    @Autowired
    JsonFileParser headersParser;
    @Autowired
    AddAccountServiceBL addAccountServiceBL;

    /**
     * Adds or updates one or more services for the specified account.
     * <p>
     * Validates the request, performs business logic for service creation or update, and returns the result.
     * </p>
     *
     * @param accountIdentifier  Identifier of the account for which services are being added/updated.
     * @param body               List of ServicePojo objects representing the services to add or update.
     * @return ResponseEntity containing a ResponsePojo with the list of IdPojo for the processed services.
     * @throws ClientException         if client-side validation fails.
     * @throws ServerException         if server-side validation fails.
     * @throws DataProcessingException if an error occurs during processing.
     */
    @Operation(
        summary = "Add or update a service for an account",
        description = "Validates and adds or updates one or more services for the specified account, including database persistence, linking, and Redis updates.",
        security = @io.swagger.v3.oas.annotations.security.SecurityRequirement(name = "bearerAuth"),
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "List of service details to add or update for the account.",
            required = true,
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ServicePojo.class)
            )
        ),
        responses = {
            @ApiResponse(responseCode = "200", description = "Service(s) added/updated successfully.",
                content = @Content(mediaType = "application/json", schema = @Schema(implementation = ResponsePojo.class))),
            @ApiResponse(responseCode = "400", description = "Invalid request payload or data.",
                content = @Content(schema = @Schema(implementation = ResponsePojo.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Missing or invalid authentication token.",
                content = @Content(schema = @Schema(implementation = ResponsePojo.class))),
            @ApiResponse(responseCode = "500", description = "Internal server error during service add/update.",
                content = @Content(schema = @Schema(implementation = ResponsePojo.class)))
        }
    )
    @AopCustomAnnotation
    @PostMapping(value = "/accounts/{accountIdentifier}/services")
    public ResponseEntity<ResponsePojo<List<IdPojo>>> addService(
            @PathVariable String accountIdentifier,
            @RequestBody List<ServicePojo> body, BasicUserDetails userDetails) throws ClientException, ServerException, DataProcessingException {
        log.trace("Method Invoked: addOrUpdateService");
        UtilityBean<List<ServicePojo>> utilityBean = addAccountServiceBL.clientValidation(body, accountIdentifier);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<List<ServiceBean>> updatedUtilityBean = addAccountServiceBL.serverValidation(utilityBean);
        List<IdPojo> result = addAccountServiceBL.process(updatedUtilityBean);
        ResponsePojo<List<IdPojo>> responseBean = new ResponsePojo<>("Service(s) added/updated successfully", result, HttpStatus.OK);
        return ResponseEntity.ok(responseBean);
    }

    @Operation(
            summary = "Retrieve Services for Account",
            responses = {
                    @ApiResponse(
                            responseCode = "200", description = "Services retrieved successfully",
                            content = @Content(
                                    schema = @Schema(
                                            implementation = Page.class
                                    )
                            )
                    ),
                    @ApiResponse(
                            responseCode = "400", description = "Error in retrieving services",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "500", description = "Internal Server Exception encountered while retrieving services",
                            content = @Content(schema = @Schema(implementation = ErrorResponsePojo.class))
                    )
            }
    )
    /**
     * Retrieves a paginated list of services for a given account, with optional search and sorting.
     *
     * @param authorization The Bearer token for authentication.
     * @param accountIdentifier The unique identifier of the account.
     * @param pageable Pagination and sorting information.
     * @param searchTerm An optional term to filter services by name or identifier.
     * @return A {@link ResponseEntity} containing a paginated list of {@link ServiceListPage} objects.
     * @throws ClientException If the client request is invalid.
     * @throws DataProcessingException If an error occurs during data processing.
     * @throws ServerException If a server-side error occurs.
     */
    @AopCustomAnnotation
    @GetMapping(value = "/accounts/{accountIdentifier}/services")
    public ResponseEntity<ResponsePojo<Page<ServiceListPage>>> getAccountServices(
            @Parameter(
                    name = "accountIdentifier",
                    description = "Unique identifier of the account",
                    required = true,
                    example = "account-001"
            )
            @PathVariable("accountIdentifier") String accountIdentifier,
            @Parameter(
                    description = "Pagination and sorting information. " +
                            "Use `page` (0-indexed), `size` (items per page), and `sort` (property[,asc|desc]). " +
                            "Example: `?page=0&size=10&sort=name,asc`",
                    required = false
            )
            Pageable pageable,
            @Parameter(
                    description = "Search term to filter services by name or identifier.",
                    required = false,
                    example = "TestService")
            @RequestParam(required = false) String searchTerm, BasicUserDetails userDetails)
            throws ClientException, DataProcessingException, ServerException {

        UtilityBean<Object> utilityBean = getAccountServicesBL.clientValidation(null, accountIdentifier, searchTerm);
        utilityBean.setPageable(pageable);
        utilityBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        UtilityBean<AccountServiceValidationBean> validatedBean = getAccountServicesBL.serverValidation(utilityBean);
        Page<ServiceListPage> services = getAccountServicesBL.process(validatedBean);

        ResponsePojo<Page<ServiceListPage>> responsePojo = new ResponsePojo<>("success",
                services, HttpStatus.OK);

        return ResponseEntity.ok().headers(headersParser.loadHeaderConfiguration()).body(responsePojo);
    }

}

