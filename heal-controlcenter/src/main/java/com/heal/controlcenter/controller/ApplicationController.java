package com.heal.controlcenter.controller;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.aop.AopCustomAnnotation;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.businesslogic.GetApplicationsBL;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.ApplicationPojo;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.pojo.ResponsePojo;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.JsonFileParser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST Controller for retrieving applications associated with an account.
 * This controller provides endpoints for fetching applications with optional cluster data and search filters.
 */
@Slf4j
@RestController
public class ApplicationController {
    @Autowired
    GetApplicationsBL getApplicationsBL;
    @Autowired
    JsonFileParser headersParser;

    @Operation(
            summary = "Retrieves applications for the specified account.",
            description = "Returns a paginated list of applications linked to the given account identifier. " +
                    "Optionally filters results using a search term and includes cluster data if requested.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Applications fetched successfully.",
                            content = @Content(schema = @Schema(implementation = ApplicationPojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Client-side error due to invalid input parameters or token.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "401",
                            description = "Unauthorized - Invalid or missing authentication token.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "Server-side error while processing application retrieval.",
                            content = @Content(schema = @Schema(implementation = ResponsePojo.class))
                    )
            }
    )
    /**
     * Retrieves paginated list of applications for the specified account.
     * <p>
     * This endpoint performs the following operations:
     * 1. Validates client input (authorization token, account identifier)
     * 2. Performs server-side validation of the account
     * 3. Retrieves applications with optional search filtering and cluster data inclusion
     * 4. Returns paginated results with standard response headers
     *
     * @param authorization         Bearer token for authentication (required)
     * @param accountIdentifier     Unique identifier of the account (required)
     * @param clusterDataRequired   Flag to include cluster data (default: true)
     * @param searchTerm            Optional term to filter applications by name/identifier
     * @param pageable             Pagination and sorting parameters
     * @return ResponseEntity containing paginated ApplicationPojo results
     * @throws ClientException         When invalid client input is detected
     * @throws ServerException        When server-side validation fails
     * @throws DataProcessingException When application processing fails
     * @throws ControlCenterException For unexpected system errors
     */
    @AopCustomAnnotation
    @GetMapping(value = "/accounts/{identifier}/applications")
    public ResponseEntity<ResponsePojo<Page<ApplicationPojo>>> getApplications(
            @Parameter(
                    description = "Account identifier to fetch associated applications",
                    required = true
            )
            @PathVariable(value = "identifier") String accountIdentifier,
            @Parameter(
                    description = "Boolean flag indicating whether cluster data is required",
                    required = false,
                    example = "true"
            )
            @RequestParam(value = "clusterDataRequired", required = false, defaultValue = "true") String clusterDataRequired,
            @Parameter(
                    description = "Search term to filter applications by name or identifier",
                    required = false,
                    example = "InventoryApp"
            )
            @RequestParam(value = "searchTerm", required = false, defaultValue = "") String searchTerm,
            @Parameter(
                    hidden = true
            )
            Pageable pageable, BasicUserDetails userDetails)
            throws ClientException, DataProcessingException, ServerException, ControlCenterException {

        log.info("Request received to fetch applications for account [{}] with parameters - clusterDataRequired: {}, searchTerm: {}, pageable: {}",
                accountIdentifier, clusterDataRequired, searchTerm, pageable);

        // Client validation
        log.debug("Performing client validation for account [{}]", accountIdentifier);
        UtilityBean<String> applicationBean = getApplicationsBL.clientValidation(null, accountIdentifier, clusterDataRequired, searchTerm);
        applicationBean.getMetadata().put(Constants.USER_ID_KEY, userDetails.getUserIdentifier());
        applicationBean.setPageable(pageable);

        // Server validation
        log.debug("Performing server validation for account [{}]", accountIdentifier);
        UtilityBean<Account> account = getApplicationsBL.serverValidation(applicationBean);

        // Process request
        log.debug("Fetching applications for account [{}] with pageable parameters", accountIdentifier);
        Page<ApplicationPojo> listOfApplications = getApplicationsBL.process(account);

        // Prepare response
        ResponsePojo<Page<ApplicationPojo>> responsePojo =
                new ResponsePojo<>("Applications fetched successfully", listOfApplications, HttpStatus.OK);

        log.info("Successfully fetched {} applications for account [{}]",
                listOfApplications.getNumberOfElements(), accountIdentifier);

        return ResponseEntity.ok()
                .headers(headersParser.loadHeaderConfiguration())
                .body(responsePojo);
    }
}