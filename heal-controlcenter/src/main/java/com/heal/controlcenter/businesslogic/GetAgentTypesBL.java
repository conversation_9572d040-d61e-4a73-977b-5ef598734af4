package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.AccountBean;
import com.heal.controlcenter.beans.ControllerBean;
import com.heal.controlcenter.beans.UtilityBean;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AgentTypePojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GetAgentTypesBL implements BusinessLogic<Integer, UtilityBean<Integer>, List<AgentTypePojo>> {

    @Autowired
    AccountsDao accountDao;
    @Autowired
    ControllerDao controllerDao;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    CompInstanceDao compInstanceDao;
    @Autowired
    AgentDao agentDao;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    @Override
    public UtilityBean<Integer> clientValidation(Integer requestBody, String... requestParams) throws ClientException {
        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        String serviceIdentifier = requestParams[2];
        clientValidationUtils.serviceIdentifierValidation(serviceIdentifier);

        int serviceId;
        try {
            serviceId = Integer.parseInt(requestParams[2]);
        } catch (NumberFormatException e) {
            log.error(String.format(UIMessages.SERVICE_ID_INVALID_INTEGER, requestParams[2]));
            throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
        }

        if(serviceId <= 0) {
            log.error(String.format(UIMessages.SERVICE_ID_INVALID_INTEGER, requestParams[2]));
            throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.<Integer>builder()
                .pojoObject(serviceId)
                .requestParams(requestParamsMap)
                .build();
    }

    @Override
    public UtilityBean<Integer> serverValidation(UtilityBean<Integer> utilityBean) throws ServerException {
        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = accountDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE, accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        utilityBean.setMetadata(Map.of(Constants.ACCOUNT, account));

        ControllerBean service = controllerDao.getServiceById(utilityBean.getPojoObject(), account.getId());
        if (null == service) {
            String message = String.format("ServiceId [%d] is unavailable for account [%s]", utilityBean.getPojoObject(),utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
            log.error(message);
            throw new ServerException(message);
        }

        return utilityBean;
    }

    @Override
    public List<AgentTypePojo> process(UtilityBean<Integer> bean) throws DataProcessingException {
        int serviceId = bean.getPojoObject();
        int accountId = ((AccountBean) bean.getMetadata().get(Constants.ACCOUNT)).getId();

        Set<Integer> agentIds = compInstanceDao.getAgentIdForService(serviceId, accountId);
        agentIds.addAll(compInstanceDao.getAgentIdForServiceFromTagMapping(serviceId, accountId));

        if(agentIds.isEmpty()) {
            log.warn("There are no agents mapped to the service [{}]", serviceId);
            return Collections.emptyList();
        }

        return agentDao.getAgentTypeListApartFromJimAndForensicAgents(agentIds)
                .parallelStream()
                .map(h -> AgentTypePojo.builder()
                        .id(h.getAgentTypeId())
                        .name(masterDataDao.getMstSubTypeBySubTypeId(h.getAgentTypeId()).getSubTypeName())
                        .build()).filter(Objects::nonNull)
                .sorted(Comparator.comparing(AgentTypePojo::getId)
                        .thenComparing(o -> o.getName().toLowerCase()))
                .distinct()
                .collect(Collectors.toList());
    }
}
