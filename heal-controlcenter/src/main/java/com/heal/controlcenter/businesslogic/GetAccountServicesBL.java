package com.heal.controlcenter.businesslogic;

import com.heal.configuration.pojos.Account;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.*;
import com.heal.controlcenter.util.*;
import com.heal.controlcenter.util.LogMessages;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetAccountServicesBL implements BusinessLogic<Object, UtilityBean<AccountServiceValidationBean>, Page<ServiceListPage>> {

    @Autowired
    private ControllerDao controllerDao;
    @Autowired
    private ConnectionDetailsDao connectionDetailsDao;
    @Autowired
    private UserDao userDao;
    @Autowired
    private ClientValidationUtils clientValidationUtils;
    @Autowired
    private ServerValidationUtils serverValidationUtils;
    @Autowired
    private MasterDataDao masterDataDao;
    @Autowired
    private KeyCloakAuthService keyCloakAuthService;
    @Autowired
    private ServiceDao serviceDao;
    @Autowired
    private AccountServiceDao accountServiceDao;

    /**
     * Validates the client request for retrieving account services.
     *
     * @param requestBody The request body (not used in this case).
     * @param requestParams The request parameters, including authorization, account identifier, and search term.
     * @return A {@link UtilityBean} containing the validated request parameters.
     * @throws ClientException If any validation fails.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<Object> clientValidation(Object requestBody, String... requestParams) throws ClientException {
        String accountIdentifier = requestParams[0];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        String searchTerm = requestParams.length > 1 ? requestParams[1] : null;
        if (searchTerm != null) {
            searchTerm = searchTerm.trim();
            if (searchTerm.isEmpty()) {
                log.warn("Search term is empty after trimming. Defaulting to empty string.");
                searchTerm = "";
            }
        } else {
            log.warn("Search term is null in requestParams. Defaulting to empty string.");
            searchTerm = "";
        }

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(null, accountIdentifier);
        requestParamsMap.put(Constants.SEARCH_TERM_KEY, searchTerm);

        return UtilityBean.builder()
                .requestParams(requestParamsMap)
                .metadata(new HashMap<>())
                .build();
    }

    /**
     * Performs server-side validation for the get account services request.
     *
     * @param utilityBean A {@link UtilityBean} containing request parameters.
     * @return An enriched {@link UtilityBean} with extracted metadata (e.g., userId, account).
     * @throws ServerException If validation fails or account is not found.
     */
    @Override
    @LogExecutionAnnotation
    public UtilityBean<AccountServiceValidationBean> serverValidation(UtilityBean<Object> utilityBean) throws ServerException {

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        Account account = serverValidationUtils.accountValidation(accountIdentifier);

        AccountServiceValidationBean accountServiceValidationBean = AccountServiceValidationBean.builder()
                .account(account)
                .build();

        return UtilityBean.<AccountServiceValidationBean>builder()
                .pojoObject(accountServiceValidationBean)
                .metadata(utilityBean.getMetadata())
                .pageable(utilityBean.getPageable())
                .requestParams(utilityBean.getRequestParams())
                .build();
    }

    /**
     * Executes the business logic to retrieve a paginated list of services for an account.
     *
     * @param utilityBean A validated {@link UtilityBean} containing metadata and request parameters.
     * @return A {@link Page} of services for the account.
     * @throws DataProcessingException If any error occurs during service retrieval from the database.
     */
    @Override
    @LogExecutionAnnotation
    public Page<ServiceListPage> process(UtilityBean<AccountServiceValidationBean> utilityBean) throws DataProcessingException {
        AccountServiceValidationBean accountServiceValidationBean = utilityBean.getPojoObject();
        Account account = accountServiceValidationBean.getAccount();
        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);

        Pageable pageable = utilityBean.getPageable();
        String searchTerm = utilityBean.getRequestParams().get(Constants.SEARCH_TERM_KEY);

        PaginationUtils.validatePagination(pageable);

        if (searchTerm == null) {
            log.warn("Search term is null in UtilityBean requestParams. Defaulting to empty string.");
            searchTerm = "";
        }
        log.info("Processing services for account {} with Pageable: {} and SearchTerm: '{}'", account.getIdentifier(), pageable, searchTerm);

        try {
            List<ViewApplicationServiceMappingBean> serviceAppMapping = userDao.getUserAccessibleApplicationsServices(userId, account.getIdentifier());
            if (serviceAppMapping.isEmpty()) {
                log.warn(LogMessages.USER_ACCESS_DETAILS_UNAVAILABLE, userId, account.getIdentifier());
                return new PageImpl<>(Collections.emptyList(), pageable, 0);
            }

            List<Integer> accessibleServiceIds = serviceAppMapping.stream()
                    .map(ViewApplicationServiceMappingBean::getServiceId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (accessibleServiceIds.isEmpty()) {
                log.warn(LogMessages.USER_HAS_NO_ACCESSIBLE_SERVICES, userId, account.getIdentifier());
                return new PageImpl<>(Collections.emptyList(), pageable, 0);
            }

            List<ControllerBean> services = controllerDao.getServicesList(account.getId(), searchTerm, accessibleServiceIds, pageable);
            int totalServices = controllerDao.getServicesListCount(account.getId(), searchTerm, accessibleServiceIds);

            if (services.isEmpty()) {
                log.info(LogMessages.NO_SERVICES_FOUND, account.getIdentifier());
                return new PageImpl<>(Collections.emptyList(), pageable, totalServices);
            }

            List<ConnectionDetailsBean> connections = connectionDetailsDao.getConnectionsByAccountId(account.getId());
            List<CompInstClusterDetailsBean> compInstClusterDetails = masterDataDao.getCompInstanceDetails(account.getId());
            Map<Integer, String> componentTypeMap = compInstClusterDetails.stream()
                    .collect(Collectors.toMap(CompInstClusterDetailsBean::getInstanceId, CompInstClusterDetailsBean::getComponentTypeName, (e, r) -> e));

            Map<Integer, List<ViewApplicationServiceMappingBean>> servicesAppMap = serviceAppMapping.stream()
                    .filter(sa -> sa.getServiceId() != null)
                    .collect(Collectors.groupingBy(ViewApplicationServiceMappingBean::getServiceId));

            Map<Integer, String> serviceIdToNameMap = services.stream()
                    .collect(Collectors.toMap(ControllerBean::getId, ControllerBean::getName));

            List<ServiceListPage> serviceListPages = services.stream()
                    .map(service -> {
                        ServiceListPage bean = new ServiceListPage();
                        bean.setId(service.getId());
                        bean.setName(service.getName());
                        bean.setIdentifier(service.getIdentifier());
                        bean.setComponentType(componentTypeMap.getOrDefault(service.getId(), "NA"));
                        bean.setStatus(String.valueOf(service.getStatus()));

                        // Fetch additional service details using AccountServiceDao
                        bean.setLayer(accountServiceDao.getTagValueForService(service.getId(), Constants.LAYER_TAG));
                        bean.setType(accountServiceDao.getTagValueForService(service.getId(), Constants.SERVICE_TYPE_TAG));
                        bean.setEntryPointService(accountServiceDao.isEntryPointService(service.getId()));
                        bean.setServiceGroup(accountServiceDao.getServiceGroupForService(service.getId()));

                        try {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            if (service.getCreatedTime() != null) {
                                bean.setCreatedOn(sdf.parse(service.getCreatedTime()).getTime());
                            }
                            if (service.getUpdatedTime() != null) {
                                bean.setLastModifiedOn(sdf.parse(service.getUpdatedTime()).getTime());
                            }
                        } catch (Exception e) {
                            log.warn(LogMessages.ERROR_PARSING_DATES_FOR_SERVICE, service.getName(), e.getMessage());
                        }

                        if (service.getLastModifiedBy() != null) {
                            KeyCloakUserDetails userDetails = keyCloakAuthService.getKeycloakUserDataFromId(service.getLastModifiedBy());
                            String username = userDetails != null ? userDetails.getUsername() : service.getLastModifiedBy();
                            bean.setLastModifiedBy(username);
                            // Set createdBy to the same value since database doesn't have separate created_by field
                            bean.setCreatedBy(username);
                        }

                        List<ViewApplicationServiceMappingBean> appMappings = servicesAppMap.get(service.getId());
                        if (appMappings != null) {
                            Set<IdPojo> serviceApps = appMappings.stream()
                                    .map(m -> IdPojo.builder().id(m.getApplicationId()).name(m.getApplicationName()).identifier(m.getApplicationIdentifier()).build())
                                    .collect(Collectors.toSet());
                            bean.setApplication(serviceApps);
                        }

                        updateInboundOutBoundServices(connections, service.getId(), bean, serviceIdToNameMap);
                        return bean;
                    })
                    .collect(Collectors.toList());

            return PaginationUtils.createPage(serviceListPages, pageable, totalServices);

        } catch (ControlCenterException e) {
            log.error("Error while fetching services for account: {}. Details: ", account.getIdentifier(), e);
            throw new DataProcessingException(e.getMessage());
        }
    }

    private void updateInboundOutBoundServices(List<ConnectionDetailsBean> connectionDetailsList, Integer serviceId, ServiceListPage bean, Map<Integer, String> serviceIdToNameMap) {
        Map<String, Object> inboundMap = new HashMap<>();
        Map<String, Object> outboundMap = new HashMap<>();

        if (Objects.nonNull(connectionDetailsList)) {
            List<Integer> inboundIds = connectionDetailsList.stream()
                    .filter(c -> serviceId.equals(c.getDestinationId()))
                    .map(ConnectionDetailsBean::getSourceId)
                    .collect(Collectors.toList());
            List<String> inboundNames = inboundIds.stream()
                    .map(serviceIdToNameMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            inboundMap.put("count", inboundNames.size());
            inboundMap.put("name", inboundNames);

            List<Integer> outboundIds = connectionDetailsList.stream()
                    .filter(c -> serviceId.equals(c.getSourceId()))
                    .map(ConnectionDetailsBean::getDestinationId)
                    .collect(Collectors.toList());
            List<String> outboundNames = outboundIds.stream()
                    .map(serviceIdToNameMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            outboundMap.put("count", outboundNames.size());
            outboundMap.put("name", outboundNames);
        } else {
            inboundMap.put("count", 0);
            inboundMap.put("name", Collections.emptyList());
            outboundMap.put("count", 0);
            outboundMap.put("name", Collections.emptyList());
        }

        bean.setInbound(inboundMap);
        bean.setOutbound(outboundMap);
    }
}
