package com.heal.controlcenter.businesslogic;

import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.*;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.AgentCommandArgumentsPojo;
import com.heal.controlcenter.pojo.AgentCommandsPojo;
import com.heal.controlcenter.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.heal.controlcenter.util.Constants.*;

@Slf4j
@Component
public class GetAgentCommandsBL implements BusinessLogic<List<String>, UtilityBean<List<String>>, AgentCommandsPojo> {

    @Autowired
    AccountsDao accountsDao;
    @Autowired
    MasterDataDao masterDataDao;
    @Autowired
    DateTimeUtil dateTimeUtil;
    @Autowired
    CommandDataDao commandDataDao;
    @Autowired
    ControllerDao controllerDao;
    @Autowired
    AgentDao agentDao;
    @Autowired
    ClientValidationUtils clientValidationUtils;
    @Autowired
    ServerValidationUtils serverValidationUtils;

    private ViewTypesBean jimAgentType;

    @Override
    public UtilityBean<List<String>> clientValidation(List<String> requestBody, String... requestParams) throws ClientException {

        String authKey = requestParams[0];
        clientValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = requestParams[1];
        clientValidationUtils.accountIdentifierValidation(accountIdentifier);

        String serviceIdentifier = requestParams[2];
        clientValidationUtils.serviceIdentifierValidation(serviceIdentifier);

        int serviceId;
        try {
            serviceId = Integer.parseInt(serviceIdentifier);
        } catch (NumberFormatException e) {
            log.error(String.format(UIMessages.SERVICE_ID_INVALID_INTEGER, serviceIdentifier));
            throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
        }

        if(serviceId <= 0) {
            log.error(String.format(UIMessages.SERVICE_ID_INVALID_INTEGER, serviceIdentifier));
            throw new ClientException(UIMessages.SERVICE_ID_INVALID_EXCEPTION_MESSAGE);
        }

        String agentType = requestParams[3];
        if (agentType.isEmpty()) {
            agentType = JIM_AGENT_SUB_TYPE;
        }

        List<String> pojoObject = new ArrayList<>();
        pojoObject.add(serviceIdentifier);
        pojoObject.add(agentType);

        HashMap<String, String> requestParamsMap = CommonUtils.buildRequestParams(authKey, accountIdentifier);

        return UtilityBean.<List<String>>builder()
                .requestParams(requestParamsMap)
                .pojoObject(pojoObject)
                .build();
    }

    @Override
    public UtilityBean<List<String>> serverValidation(UtilityBean<List<String>> utilityBean) throws ServerException {

        String authKey = utilityBean.getRequestParams().get(Constants.AUTH_KEY);
        serverValidationUtils.authKeyValidation(authKey);

        String accountIdentifier = utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER);
        AccountBean account = accountsDao.getAccountByIdentifier(accountIdentifier);
        if (account == null) {
            String message = String.format(UIMessages.ACCOUNT_IDENTIFIER_UNAVAILABLE,accountIdentifier);
            log.error(message);
            throw new ServerException(message);
        }

        utilityBean.setMetadata(Map.of(Constants.ACCOUNT, account));

        int serviceId = Integer.parseInt(utilityBean.getPojoObject().get(0));

        ControllerBean service = controllerDao.getServiceById(serviceId, account.getId());
        if (null == service) {
            String message = String.format("ServiceId [%d] is unavailable for account [%s]", serviceId, utilityBean.getRequestParams().get(Constants.ACCOUNT_IDENTIFIER));
            log.error(message);
            throw new ServerException(message);
        }

        return utilityBean;
    }

    @Override
    public AgentCommandsPojo process(UtilityBean<List<String>> bean) throws DataProcessingException {
        AgentCommandsPojo agentCommand = new AgentCommandsPojo();
        String agentType = bean.getPojoObject().get(1);
        int serviceId = Integer.parseInt(bean.getPojoObject().get(0));
        int accountId = ((AccountBean) bean.getMetadata().get(ACCOUNT)).getId();

        try {
            jimAgentType = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(AGENT_TYPE, agentType);
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }

        List<CommandDetailsBean> commandDetails = getCommandsByAgentType(agentType);

        AgentCommandArgumentsPojo agentCommandArguments = new AgentCommandArgumentsPojo();
        for (CommandDetailsBean command : commandDetails) {
            List<CommandArgumentBean> serviceCmdArgs = agentDao.getServiceCommandArguments(serviceId,
                    jimAgentType.getSubTypeId(), command.getId());

            Map<String, String> cmdsMap = serviceCmdArgs.parallelStream()
                    .collect(Collectors.toMap(CommandArgumentBean::getArgumentKey, CommandArgumentBean::getDefaultValue));

            getAgentCommandArgs(agentCommandArguments, cmdsMap, command.getName());
        }

        AgentModeConfigBean configBean = agentDao.getAgentModeConfig(serviceId, accountId, jimAgentType.getSubTypeId());

        CommandDetailsBean commandBean;
        if(configBean == null) {
            commandBean = getSelectedCommand(agentType, null);
        } else {
            commandBean = commandDetails.parallelStream()
                    .filter(c -> c.getId() == configBean.getCommandId())
                    .findAny()
                    .orElse(null);
            agentCommand.setLastUpdatedTime(String.valueOf(configBean.getUpdatedTime()));
        }

        if (commandBean == null) {
            agentCommand.setCommandMode(AGENT_MODE_AUTO);
            agentCommand.setCommandName(agentType);
        } else {
            agentCommand.setCommandMode(commandBean.getName());
            agentCommand.setCommandName(agentType);
            agentCommand.setIdentifier("JIMAgentIdentifier");
        }

        agentCommand.setServerTime(String.valueOf(dateTimeUtil.getCurrentTimestampInGMT()));
        agentCommand.setArguments(agentCommandArguments);

        return agentCommand;
    }

    public List<CommandDetailsBean> getCommandsByAgentType(String agentType) throws DataProcessingException {
        try {
            jimAgentType = masterDataDao.getViewTypesFromMstTypeAndSubTypeName(AGENT_TYPE, agentType);
        } catch (ControlCenterException e) {
            throw new DataProcessingException(e.getMessage());
        }

        List<CommandDetailsBean> commandDetails = commandDataDao.getCommandDetailsByAgentType(jimAgentType.getSubTypeId());
        if(commandDetails.isEmpty()) {
            log.error("No commands found to process the request. agentType:{}", agentType);
            throw new DataProcessingException("No commands found to process the request.");
        }
        return commandDetails;
    }

    public void getAgentCommandArgs(AgentCommandArgumentsPojo agentCommandArguments, Map<String, String> cmdArgsMap, String commandName) {
        String SNAPSHOT_COUNT = "snapshot-count";
        String SNAPSHOT_DURATION = "snapshot-duration";
        String SILENT_WINDOW = "silent-window";

        if(AGENT_MODE_AUTO.equalsIgnoreCase(commandName)) {
            agentCommandArguments.setAutoSnapshotDuration(Integer.parseInt(cmdArgsMap.get(SNAPSHOT_DURATION)));
            agentCommandArguments.setAutoSnapshotCount(Integer.parseInt(cmdArgsMap.get(SNAPSHOT_COUNT)));
            agentCommandArguments.setAutoSnapshotForException(Boolean.parseBoolean(cmdArgsMap.get("exceptions")));
            agentCommandArguments.setAutoSnapshotSilentWindow(Integer.parseInt(cmdArgsMap.getOrDefault(SILENT_WINDOW, Constants.AGENT_SILENT_WINDOW)));
            agentCommandArguments.setJvmCpuUtil(Integer.parseInt(cmdArgsMap.get("jvm_cpu")));
            agentCommandArguments.setJvmMemUtil(Integer.parseInt(cmdArgsMap.get("jvm_mem")));
            agentCommandArguments.setAutoSnapshotCollectionLevel(cmdArgsMap.get("collection-mode"));
        } else if(AGENT_MODE_VERBOSE.equalsIgnoreCase(commandName)) {
            agentCommandArguments.setVerboseSnapshotCount(Integer.parseInt(cmdArgsMap.get(SNAPSHOT_COUNT)));
            agentCommandArguments.setVerboseSnapshotDuration(Integer.parseInt(cmdArgsMap.get(SNAPSHOT_DURATION)));
            agentCommandArguments.setVerboseSnapshotSilentWindow(Integer.parseInt(cmdArgsMap.getOrDefault(SILENT_WINDOW,Constants.AGENT_SILENT_WINDOW)));
        }
    }

    public CommandDetailsBean getSelectedCommand(String agentType, String commandName) throws DataProcessingException {
        List<CommandDetailsBean> commandDetails = getCommandsByAgentType(agentType);
        if(commandDetails.isEmpty()) {
            log.error("No commands found to process the request. commandName:{}, agentType:{}", commandName, agentType);
            return null;
        }

        CommandDetailsBean commandSelected = null;
        if (commandName != null) {
            commandSelected = commandDetails.stream()
                    .filter(c -> c.getName().equalsIgnoreCase(commandName))
                    .findAny()
                    .orElse(null);
        }

        if(commandSelected == null) {
            commandSelected = commandDetails.stream()
                    .filter(c -> c.getIsDefault() == 1)
                    .findAny()
                    .orElse(null);
        }
        return commandSelected;
    }
}
