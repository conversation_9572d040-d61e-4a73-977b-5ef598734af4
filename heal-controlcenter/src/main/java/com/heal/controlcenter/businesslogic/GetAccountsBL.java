package com.heal.controlcenter.businesslogic;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.TenantDetails;
import com.heal.controlcenter.beans.*;
import com.heal.controlcenter.dao.mysql.AccountsDao;
import com.heal.controlcenter.dao.mysql.TagsDao;
import com.heal.controlcenter.dao.mysql.TenantsDao;
import com.heal.controlcenter.dao.mysql.UserDao;
import com.heal.controlcenter.exception.ClientException;
import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.DataProcessingException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.Account;
import com.heal.controlcenter.pojo.Tags;
import com.heal.controlcenter.pojo.ThresholdSeverity;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.PaginationUtils;
import com.heal.controlcenter.util.UIMessages;
import com.heal.controlcenter.util.LogMessages;
import com.heal.controlcenter.aop.LogExecutionAnnotation;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetAccountsBL implements BusinessLogic<String, UtilityBean<AccessDetailsBean>, Page<Account>> {

    @Autowired
    TagsDao tagsDao;
    @Autowired
    UserDao userDao;
    @Autowired
    AccountsDao accountDao;
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    TenantsDao tenantsDao;

    @Override
    public UtilityBean<String> clientValidation(String requestBody, String... requestParams) throws ClientException {
        long startTime = System.currentTimeMillis();
        try {
            String searchTerm = (requestParams.length > 0) ? requestParams[0] : "";
            searchTerm = (searchTerm != null) ? searchTerm.trim() : "";
            if (searchTerm.isEmpty()) log.warn(LogMessages.SEARCH_TERM_EMPTY_OR_NULL);

            HashMap<String, String> requestParamsMap = new HashMap<>();
            requestParamsMap.put(Constants.SEARCH_TERM_KEY, searchTerm);

            return UtilityBean.<String>builder()
                    .requestParams(requestParamsMap)
                    .metadata(new HashMap<>())
                    .build();
        } finally {
            log.info(LogMessages.CLIENT_VALIDATION_COMPLETED_IN_MS, System.currentTimeMillis() - startTime);
        }
    }

    @Override
    public UtilityBean<AccessDetailsBean> serverValidation(UtilityBean<String> utilityBean) throws ServerException {
        long startTime = System.currentTimeMillis();
        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
        try {
            UserAccessBean accessDetails = userDao.fetchUserAccessDetailsUsingIdentifier(userId);
            AccessDetailsBean accessDetailsBean = objectMapper.readValue(accessDetails.getAccessDetails(), AccessDetailsBean.class);
            if (accessDetailsBean == null) throw new ServerException(UIMessages.INVALID_USER_ACCESS_DETAILS);

            return UtilityBean.<AccessDetailsBean>builder()
                    .pojoObject(accessDetailsBean)
                    .metadata(Map.of(Constants.USER_ID_KEY, userId))
                    .pageable(utilityBean.getPageable())
                    .requestParams(utilityBean.getRequestParams())
                    .build();
        } catch (Exception e) {
            log.error(LogMessages.FAILED_TO_PROCESS_USER_ACCESS, userId, e);
            throw new ServerException(UIMessages.USER_ACCESS_DETAILS_NOT_FOUND);
        } finally {
            log.info(LogMessages.SERVER_VALIDATION_COMPLETED_IN_MS, System.currentTimeMillis() - startTime);
        }
    }

    @Override
    @LogExecutionAnnotation
    public Page<Account> process(UtilityBean<AccessDetailsBean> utilityBean) throws DataProcessingException {
        AccessDetailsBean accessDetails = utilityBean.getPojoObject();
        Pageable pageable = utilityBean.getPageable();
        String userId = (String) utilityBean.getMetadata().get(Constants.USER_ID_KEY);
        String searchTerm = utilityBean.getRequestParams().get(Constants.SEARCH_TERM_KEY);

        PaginationUtils.validatePagination(pageable);

        if (searchTerm == null) {
            log.warn(LogMessages.SEARCH_TERM_NULL_WARNING);
            searchTerm = "";
        }
        log.info(LogMessages.PROCESSING_ACCOUNTS_WITH_PAGEABLE_AND_SEARCH_TERM, pageable, searchTerm);
        List<AccountBean> accessibleAccounts;
        int totalCount;
        try {
            log.info(LogMessages.PROCESSING_ACCOUNTS_FOR_USER_ID, userId, searchTerm, pageable);

            accessibleAccounts = accountDao.getAccounts(searchTerm, pageable);
            totalCount = accountDao.countAccounts(searchTerm);

            // Apply account-level access filtering
            if (!accessDetails.getAccounts().contains("*")) {
                accessibleAccounts = accessibleAccounts.stream()
                        .filter(a -> accessDetails.getAccounts().contains(a.getIdentifier()))
                        .collect(Collectors.toList());
                totalCount = accessibleAccounts.size();
            }
            log.info(LogMessages.FOUND_ACCESSIBLE_ACCOUNTS, accessibleAccounts.size(), userId, searchTerm);
            List<Account> finalAccounts = new ArrayList<>();
            for (AccountBean accountBean : accessibleAccounts) {
                try {
                    TimezoneBean tz = accountDao.getAccountTimezoneDetails(accountBean.getId());
                    if (tz == null) continue;

                    AccountAnomalyConfigurationBean config = null;
                    try {
                        config = accountDao.getAccountAnomalyConfiguration(accountBean.getId());
                    } catch (ControlCenterException ignored) {
                    }

                    List<Tags> tags = tagsDao.getTagsByObjectId(accountBean.getId(), "account")
                            .stream()
                            .map(t -> new Tags(t.getType(), t.getKey()))
                            .collect(Collectors.toList());

                    ThresholdSeverity severity = new ThresholdSeverity(
                            config != null && config.isLowEnable(),
                            config != null && config.isMediumEnable(),
                            config != null && config.isHighEnable());

                    log.debug(LogMessages.ATTEMPTING_TO_FETCH_TENANT_ID, accountBean.getId());
                    Optional<Integer> optionalTenantId = tenantsDao.getTenantIdByAccountId(accountBean.getId());

                    TenantDetails tenantDetails = null;
                    if (optionalTenantId.isPresent()) {
                        int tenantId = optionalTenantId.get();
                        log.debug(LogMessages.MAPPED_TENANT_ID, accountBean.getId(), tenantId);

                        log.debug(LogMessages.FETCHING_TENANT_DETAILS, tenantId);
                        tenantDetails = tenantsDao.getTenantDetailsById(tenantId);

                        if (tenantDetails != null) {
                            log.debug(LogMessages.SUCCESSFULLY_RETRIEVED_TENANT_DETAILS, tenantId);
                        } else {
                            log.warn(LogMessages.NO_TENANT_DETAILS_FOUND, tenantId);
                        }
                    } else {
                        log.warn(LogMessages.NO_TENANT_MAPPING_FOUND, accountBean.getId());
                    }

                    Account account = Account.builder()
                            .accountId(accountBean.getId())
                            .accountName(accountBean.getName())
                            .identifier(accountBean.getIdentifier())
                            .privateKey(accountBean.getPrivateKey())
                            .publicKey(accountBean.getPublicKey())
                            .updatedTime(accountBean.getUpdatedTime() != null ? Timestamp.valueOf(accountBean.getUpdatedTime()).getTime() : null)
                            .lastModifiedBy(userDao.getUsernameFromIdentifier(accountBean.getLastModifiedBy()))
                            .timezoneMilli(tz.getOffset())
                            .timeZoneString(tz.getTimeZoneId())
                            .status(accountBean.getStatus())
                            .dateFormat("YYYY-MM-DD")
                            .timeFormat("HH:mm")
                            .tags(tags)
                            .closingWindow(config != null ? config.getClosingWindow() : 0)
                            .maxDataBreaks(config != null ? config.getMaxDataBreaks() : 0)
                            .thresholdSeverity(severity)
                            .tanantDetails(tenantDetails)
                            .build();

                    finalAccounts.add(account);
                } catch (Exception e) {
                    log.warn(LogMessages.SKIPPING_ACCOUNT_DUE_TO_PROCESSING_ERROR, accountBean.getId(), e);
                }
            }

            if (pageable.getSort().isSorted() && !finalAccounts.isEmpty()) {
                log.debug(LogMessages.APPLYING_IN_MEMORY_SORTING, pageable.getSort());
                Comparator<Account> comparator = getAccountComparator(pageable);
                if (comparator != null) {
                    finalAccounts.sort(comparator);
                    log.debug(LogMessages.SORTING_COMPLETE, finalAccounts.get(0).getIdentifier());
                }
            }

            return PaginationUtils.createPage(finalAccounts, pageable, totalCount);

        } catch (Exception e) {
            log.error(LogMessages.EXCEPTION_OCCURRED_IN_PROCESSING_ACCOUNTS, e);
            throw new DataProcessingException("Error processing accounts: " + e.getMessage());
        }
    }

    @Nullable
    private static Comparator<Account> getAccountComparator(Pageable pageable) {
        Comparator<Account> comparator = null;
        for (org.springframework.data.domain.Sort.Order order : pageable.getSort()) {
            Comparator<Account> currentComparator = null;
            if (order.getProperty().equalsIgnoreCase("accountName")) {
                currentComparator = Comparator.comparing(Account::getAccountName, Comparator.naturalOrder());
            } else if (order.getProperty().equalsIgnoreCase("lastModifiedBy")) {
                currentComparator = Comparator.comparing(
                        Account::getLastModifiedBy,
                        Comparator.nullsLast(Comparator.naturalOrder())
                );
            } else if (order.getProperty().equalsIgnoreCase("updatedTime")) {
                currentComparator = Comparator.comparing(
                        Account::getUpdatedTime,
                        Comparator.nullsLast(Comparator.naturalOrder())
                );
            }
            if (currentComparator != null) {
                if (!order.isAscending()) {
                    currentComparator = currentComparator.reversed();
                }
                comparator = comparator == null ? currentComparator : comparator.thenComparing(currentComparator);
            }
        }
        return comparator;
    }
}