package com.heal.controlcenter.dao.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.heal.configuration.pojos.Account;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.Component;
import com.heal.configuration.pojos.DatePickerRangeDetails;
import com.heal.controlcenter.util.CommonUtils;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

import java.util.*;

/**
 * <AUTHOR> on 07-03-2022
 */
@Repository
@Slf4j
public class AccountRepo {

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    RedisUtilities redisUtilities;

    public List<Account> getAccounts() {
        String key = "/accounts";
        String hashKey = "ACCOUNT_DATA";
        try {
            String accDetails = redisUtilities.getKey(key, hashKey);
            if (accDetails == null || accDetails.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                return null;
            }

            return objectMapper.readValue(accDetails, new TypeReference<List<Account>>() {
            });

        } catch (Exception e) {
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return null;
        }
    }

    public Account getAccount(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier;
        String hashKey = "ACCOUNT_DATA_" + accountIdentifier;
        try {
            String accountDetail = redisUtilities.getKey(key, hashKey);
            if (accountDetail == null || accountDetail.trim().isEmpty()) {
                log.error("Account details unavailable for identifier [{}]", accountIdentifier);
                return null;
            }
            return objectMapper.readValue(accountDetail, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting account details for account [{}]. Details: ", accountIdentifier, e);
            return null;
        }
    }

    public Account getAccountById(int accountId) {
        List<Account> accounts = this.getAccounts();
        if (accounts == null || accounts.isEmpty()) {
            return null;
        }
        return accounts.parallelStream()
                .filter(a -> a.getId() == accountId)
                .findAny()
                .orElse(null);
    }

    public List<Component> getComponents(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/components";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_COMPONENTS";
        try {
            String componentList = redisUtilities.getKey(key, hashKey);
            if (componentList == null || componentList.trim().isEmpty()) {
                log.error("Component list unavailable for account identifier [{}]", accountIdentifier);
                return Collections.emptyList();
            }
            return objectMapper.readValue(componentList, new TypeReference<List<Component>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting component details for account [{}]. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    /**
     * Updates the Redis cache with the given account data for the specified account identifier.
     *
     * @param accountIdentifier Unique identifier of the account
     * @param account Account object to be updated in Redis
     */
    public void updateAccount(String accountIdentifier, Account account) {
        String key = "/accounts";
        String hashKey = "ACCOUNT_DATA";
        try {
            redisUtilities.updateKey(key + "/" + accountIdentifier,
                    hashKey + "_" + accountIdentifier,
                    account);
            log.debug("Updated the account with id {}, name {}", account.getId(), account.getName());
        } catch (Exception e) {
            log.error("Error occurred while updating account details {} for account [{}]. Details: ", account, accountIdentifier, e);
        }
    }

    /**
     * Updates the Redis cache with the list of account data.
     *
     * @param accounts List of Account objects to be updated in Redis
     */
    public void updateAccounts(List<Account> accounts) {
        String key = "/accounts";
        String hashKey = "ACCOUNT_DATA";
        try {
            redisUtilities.updateKey(key, hashKey, accounts);
            log.debug("Updated the account details");
        } catch (Exception e) {
            log.error("Error occurred while updating the account details  ");
        }
    }

    public List<DatePickerRangeDetails> getDatePickerRangeDetails(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/datepickerrangedetails";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_DATEPICKERRANGEDETAILS";
        try {
            String datePickerList = redisUtilities.getKey(key, hashKey);
            if (datePickerList == null || datePickerList.trim().isEmpty()) {
                if (accountIdentifier.equals(Constants.GLOBAL_ACCOUNT_IDENTIFIER_DEFAULT)) {
                    log.error("Date picker range list unavailable for global account identifier [{}]", accountIdentifier);
                } else {
                    log.warn("Date picker range list unavailable for account identifier [{}]", accountIdentifier);
                }
                return new ArrayList<>();
            }
            return objectMapper.readValue(datePickerList, new TypeReference<List<DatePickerRangeDetails>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting date picker range details for account [{}]. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public Map<Integer, Set<BasicEntity>> getAccountOutbounds(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/outbounds";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_OUTBOUNDS";
        try {
            String accountOutBounds = redisUtilities.getKey(key, hashKey);
            if (accountOutBounds == null || accountOutBounds.trim().isEmpty()) {
                log.error("Account outbound connections list unavailable for account identifier [{}]", accountIdentifier);
                return new HashMap<>();
            }
            return objectMapper.readValue(accountOutBounds, new TypeReference<Map<Integer, Set<BasicEntity>>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting account outbound connections list for account [{}]. Details: ", accountIdentifier, e);
            return new HashMap<>();
        }
    }

    public Account getAccountWithAccountIdentifier(String accountIdentifier) {
        String ACCOUNTS_KEY = "/accounts/" + accountIdentifier;
        String ACCOUNTS_HASH = "ACCOUNT_DATA_" + accountIdentifier;
        try {
            String accountBean = redisUtilities.getKey(ACCOUNTS_KEY, ACCOUNTS_HASH);
            if (accountBean == null) {
                log.debug("Account details not found");
                return null;
            }
            return CommonUtils.getObjectMapperWithHtmlEncoder().readValue(accountBean, new TypeReference<Account>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting account Details", e);
            return null;
        }
    }

    public List<BasicEntity> getAllApplications(String accIdentifier) {
        String key = "/accounts/" + accIdentifier + "/applications";
        String hashKey = "ACCOUNTS_" + accIdentifier + "_APPLICATIONS";
        try {
            String applicationDetails = redisUtilities.getKey(key, hashKey);

            if (applicationDetails == null || applicationDetails.trim().isEmpty()) {
                log.debug("Application details not found for account: [{}]", accIdentifier);
                return Collections.emptyList();
            }

            return objectMapper.readValue(applicationDetails, new TypeReference<List<BasicEntity>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while fetching application details for account [{}]:", accIdentifier, e);
            return Collections.emptyList();
        }
    }

}
