package com.heal.controlcenter.dao.mysql;

import com.heal.configuration.entities.KpiCategoryDetailsBean;
import com.heal.configuration.pojos.KpiCategoryDetails;
import com.heal.controlcenter.dao.mysql.entity.KpiDetailsBean;
import com.heal.controlcenter.exception.ControlCenterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class KPIDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public Integer getKpiCountForAccount(int accountId) throws ControlCenterException {
        try {
            String query = "select count(1) from mst_kpi_details where account_id in (1, " + accountId + ")";
            return jdbcTemplate.queryForObject(query, Integer.class);
        } catch (Exception e) {
            log.error("Exception while getting KPI count for accountId [{}]. Details: ", accountId, e);
            throw new ControlCenterException("Error occurred while getting KPI count.");
        }
    }

    public List<KpiDetailsBean> getAllKpiDetailsKpiList() {
        String query = "SELECT distinct kvm.mst_kpi_details_id id, mk.name name,kvm.mst_component_id componentId,mk.kpi_type_id typeId," +
                "mk.cluster_operation clusterOperation, mk.measure_units measureUnits,mk.data_type dataType, formula computedFormula, kvm.mst_common_version_id commonVersionId " +
                "FROM mst_component_version_kpi_mapping kvm " +
                "LEFT JOIN mst_kpi_details mk ON kvm.mst_kpi_details_id = mk.id " +
                "LEFT JOIN mst_computed_kpi_details mckd ON mckd.mst_kpi_details_id=mk.id " +
                "WHERE kvm.status = 1";
        try {
            log.debug("getting kpi list.");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(KpiDetailsBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching all kpi details list ", e);
        }
        return Collections.emptyList();
    }

    public KpiCategoryDetails getKpiCategoryDetails(int kpiId) {
        String query = "select category_id id, name, category_identifier categoryId, if(is_workload=1,true,false) isWorkLoad " +
                "from view_kpi_category_details where kpi_id = ?";
        try {
            log.debug("getting computed expression.");
            return jdbcTemplate.queryForObject(query, new BeanPropertyRowMapper<>(KpiCategoryDetails.class), kpiId);
        } catch (Exception e) {
            log.error("Error occurred while fetching ComputerExpression [{}], Stack trace: ", kpiId, e);
        }
        return null;
    }

    public List<KpiCategoryDetailsBean> getAllKpiCategoryDetails() {
        String query = "select kpi_id, category_id as id, name, category_identifier as identifier, if(is_workload=1,true,false) isWorkLoad, " +
                "kpi_type_id from view_kpi_category_details";
        try {
            log.trace("Fetching kpi category details");
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(KpiCategoryDetailsBean.class));
        } catch (Exception e) {
            log.error("Error occurred while fetching kpi category details", e);
        }
        return Collections.emptyList();
    }
}
