package com.heal.controlcenter.dao.mysql;

import com.heal.configuration.pojos.TenantDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Repository
@Slf4j
public class TenantsDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Fetches the total number of tenants present in the `tenant_details` table.
     * <p>
     * Scenario:
     * - Used during application startup or setup to check if any tenants exist.
     * - If count is 0, a default tenant can be inserted.
     */
    public int getTenantCount() {
        String sql = "SELECT COUNT(*) FROM tenant_details";
        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
            log.debug("Fetched tenant count: {}", count);
            return count != null ? count : 0;
        } catch (DataAccessException e) {
            log.error("Database error while fetching tenant count", e);
            throw e;
        }
    }

    /**
     * Inserts a new tenant into the `tenant_details` table and returns the generated tenant ID.
     * <p>
     * Scenario:
     * - Used during first-time system initialization to insert a default tenant.
     * - Can also be used when manually adding tenants in a multi-tenant environment.
     */
    public int insertTenant(String name, String identifier, int status, String userDetailsId) {
        String sql = "INSERT INTO tenant_details (name, identifier, status, created_time, updated_time, user_details_id) " +
                "VALUES (?, ?, ?, now(), now(), ?)";
        KeyHolder keyHolder = new GeneratedKeyHolder();
        try {
            jdbcTemplate.update(connection -> {
                PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
                ps.setString(1, name);
                ps.setString(2, identifier);
                ps.setInt(3, status);
                ps.setString(4, userDetailsId);
                return ps;
            }, keyHolder);
            int tenantId = keyHolder.getKey().intValue();
            log.info("Inserted new tenant with ID: {}", tenantId);
            return tenantId;
        } catch (DataAccessException e) {
            log.error("Database error while inserting tenant with name: {}", name, e);
            throw e;
        }
    }

    /**
     * Retrieves the default tenant ID stored in the `a1_installation_attributes` table.
     * <p>
     * Scenario:
     * - Used when logic needs to determine which tenant is marked as default.
     * - Helps in mapping accounts to the default tenant.
     */
    public int getDefaultTenantIdFromAttributes() {
        String sql = "SELECT value FROM a1_installation_attributes WHERE name = 'DefaultTenantId'";
        try {
            Integer defaultTenantId = jdbcTemplate.queryForObject(sql, Integer.class);
            log.debug("Fetched default tenant ID from attributes: {}", defaultTenantId);
            return defaultTenantId != null ? defaultTenantId : 0; // Or handle NoResultException if appropriate
        } catch (DataAccessException e) {
            log.error("Database error while fetching default tenant ID from attributes", e);
            throw e;
        }
    }

    /**
     * Inserts a new attribute into the `a1_installation_attributes` table.
     * <p>
     * Scenario:
     * - Used to persist metadata like 'DefaultTenantId' during system initialization.
     *
     * @param name          Name of the attribute (e.g., 'DefaultTenantId')
     * @param value         Value of the attribute (e.g., '101')
     * @param userDetailsId ID of the user/system who is inserting the data
     */
    public void insertInstallationAttribute(String name, String value, String userDetailsId) {
        String sql = """
                    INSERT INTO a1_installation_attributes 
                        (name, value, user_details_id, created_time, updated_time)
                    VALUES (?, ?, ?, now(), now())
                """;
        try {
            int rowsInserted = jdbcTemplate.update(sql, name, value, userDetailsId);
            if (rowsInserted == 1) {
                log.info("Successfully inserted installation attribute: {} with value: {}", name, value);
            } else {
                log.warn("Unexpected insert result: {} row(s) inserted for attribute: {}", rowsInserted, name);
            }
        } catch (DataAccessException e) {
            log.error("Database error while inserting installation attribute: {}", name, e);
            throw e;
        }
    }

    /**
     * Creates a mapping between an account and a tenant in `account_tenant_details`.
     * <p>
     * Scenario:
     * - Used immediately after account creation to associate it with a tenant.
     * - Ensures that every account has a tenant reference.
     */
    public void insertAccountTenantMapping(int accountId, int tenantId, String userDetailsId) {
        String sql = "INSERT INTO account_tenant_details (account_id, tenant_id, user_details_id, created_time, updated_time) " +
                "VALUES (?, ?, ?, now(), now())";
        try {
            int rowsAffected = jdbcTemplate.update(sql, accountId, tenantId, userDetailsId);
            if (rowsAffected > 0) {
                log.info("Successfully inserted account-tenant mapping for account ID: {} and tenant ID: {}", accountId, tenantId);
            } else {
                log.warn("No rows affected when inserting account-tenant mapping for account ID: {} and tenant ID: {}", accountId, tenantId);
            }
        } catch (DataAccessException e) {
            log.error("Database error while inserting account-tenant mapping for account ID: {} and tenant ID: {}", accountId, tenantId, e);
            throw e;
        }
    }

    /**
     * Inserts OpenSearch configuration data mapped to a tenant.
     * <p>
     * Scenario:
     * - Called when a tenant is being configured to connect to OpenSearch clusters.
     * - Required for analytics and observability per tenant.
     */
    public void insertOpenSearchTenantMapping(int tenantId, String clusterName, String nodeAddress,
                                              String protocol, int zoneId, int perRouteConnections,
                                              int maxConnections, int connTimeout, int socketTimeout,
                                              int keepAliveSecs, String username, String password,
                                              String userDetailsId) {
        String sql = """
                    INSERT INTO opensearch_tenant_mapping (
                        tenant_id, cluster_name, node_address, protocol, zone_id,
                        per_route_connections, max_connections, connection_timeout_ms,
                        socket_timeout_ms, keep_alive_secs, status, username, password,
                        created_time, updated_time, user_details_id
                    )
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?, now(), now(), ?)
                """;
        try {
            int rowsAffected = jdbcTemplate.update(sql, tenantId, clusterName, nodeAddress, protocol, zoneId,
                    perRouteConnections, maxConnections, connTimeout, socketTimeout,
                    keepAliveSecs, username, password, userDetailsId);
            if (rowsAffected > 0) {
                log.info("Successfully inserted OpenSearch tenant mapping for tenant ID: {} and zone ID: {}", tenantId, zoneId);
            } else {
                log.warn("No rows affected when inserting OpenSearch tenant mapping for tenant ID: {} and zone ID: {}", tenantId, zoneId);
            }
        } catch (DataAccessException e) {
            log.error("Database error while inserting OpenSearch tenant mapping for tenant ID: {} and zone ID: {}", tenantId, zoneId, e);
            throw e;
        }
    }

    /**
     * Retrieves a map of default zone IDs and their names from the `mst_sub_type` table.
     * <p>
     * Scenario:
     * - Used to fetch all default zones for processing.
     * - Helps in identifying zones that are active and available for tenant mapping.
     */
    public Map<Integer, String> getDefaultZonesWithNames() {
        String sql = "SELECT id, name FROM mst_sub_type WHERE mst_type_id = 122 AND status = 1";
        try {
            return jdbcTemplate.query(sql, rs -> {
                Map<Integer, String> result = new HashMap<>();
                while (rs.next()) {
                    result.put(rs.getInt("id"), rs.getString("name"));
                }
                return result;
            });
        } catch (DataAccessException e) {
            log.error("Error fetching default zone IDs with names", e);
            throw e;
        }
    }

    /**
     * Fetches tenant details for a given tenant ID from the `tenant_details` table.
     * <p>
     * Scenario:
     * - Used to populate tenant metadata in Redis-compatible account object.
     */
    public TenantDetails getTenantDetailsById(int tenantId) {
        String sql = "SELECT id, name, identifier, status, created_time, updated_time, user_details_id FROM tenant_details WHERE id = ?";
        try {
            return jdbcTemplate.queryForObject(sql, (rs, rowNum) -> {
                TenantDetails details = new TenantDetails();
                details.setTenantId(rs.getInt("id"));
                details.setTenantName(rs.getString("name"));
                details.setTenantIdentifier(rs.getString("identifier"));
                details.setStatus(rs.getInt("status"));
                details.setCreatedTime(String.valueOf(rs.getTimestamp("created_time")));
                details.setUpdatedTime(String.valueOf(rs.getTimestamp("updated_time")));
                details.setUserDetailsId(rs.getString("user_details_id"));
                return details;
            }, tenantId);
        } catch (DataAccessException e) {
            log.error("Error fetching tenant details for tenantId: {}", tenantId, e);
            throw e;
        }
    }

    /**
     * Retrieves the tenant ID associated with the given account ID from the `account_tenant_details` table.
     * <p>
     * Scenario:
     * - Used when reading an account to identify the tenant it is mapped to.
     * - Complements {@link #insertAccountTenantMapping(int, int, String)} for reverse lookup during fetch operations.
     * - Ensures the system can dynamically resolve tenant context during account read, update, or display.
     * <p>
     * Behavior:
     * - Returns {@link Optional#empty()} if no tenant mapping exists for the account.
     * - Avoids exception propagation in case of missing data, making it safe to use in downstream logic.
     *
     * @param accountId the account ID whose tenant mapping needs to be fetched
     * @return an {@link Optional} containing the tenant ID if mapping exists, otherwise {@link Optional#empty()}
     */
    public Optional<Integer> getTenantIdByAccountId(int accountId) {
        String sql = "SELECT tenant_id FROM account_tenant_details WHERE account_id = ?";
        try {
            Integer tenantId = jdbcTemplate.queryForObject(sql, Integer.class, accountId);
            log.debug("Fetched tenant ID {} for account ID {}", tenantId, accountId);
            return Optional.ofNullable(tenantId);
        } catch (DataAccessException e) {
            log.warn("No tenant mapping found for account ID: {}", accountId); // Not an error if it's missing
            return Optional.empty();
        }
    }
}