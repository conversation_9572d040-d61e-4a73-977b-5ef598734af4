package com.heal.controlcenter.dao.mysql.entity;

import com.heal.controlcenter.beans.CompClusterMappingBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Slf4j
@Repository
public class ComponentDao {

    @Autowired
    JdbcTemplate jdbcTemplate;

    public List<CompClusterMappingBean> getInstanceClusterMapping(int accountId) {
        String query = "SELECT comp_instance_id AS compInstanceId, cluster_id AS clusterId " +
                "FROM component_cluster_mapping " +
                "WHERE account_id = ?";

        try {
            log.debug("Fetching instance-cluster mappings for accountId: {}", accountId);
            return jdbcTemplate.query(query, new BeanPropertyRowMapper<>(CompClusterMappingBean.class), accountId);
        } catch (Exception e) {
            log.error("Error occurred while fetching instance-cluster mappings for accountId: {}. Details:", accountId, e);
            return Collections.emptyList();
        }
    }

}
