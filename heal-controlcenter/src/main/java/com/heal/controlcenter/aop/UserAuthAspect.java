package com.heal.controlcenter.aop;

import com.heal.controlcenter.exception.ControlCenterException;
import com.heal.controlcenter.exception.ServerException;
import com.heal.controlcenter.pojo.BasicUserDetails;
import com.heal.controlcenter.util.Constants;
import com.heal.controlcenter.util.HealthMetrics;
import com.heal.controlcenter.util.KeyCloakAuthService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class UserAuthAspect {

    @Autowired
    private KeyCloakAuthService keyCloakAuthService;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private HttpServletResponse response;

    @Autowired
    private HealthMetrics healthMetrics;

    @Autowired
    @Qualifier("headerConfigurations")
    private Map<String, String> headerConfigs;

    /**
     * Pointcut that matches any controller method annotated with @AopCustomAnnotation.
     */
    @Pointcut("@annotation(com.heal.controlcenter.aop.AopCustomAnnotation)")
    public void controllerClassMethodsPointcut() {
    }

    /**
     * Aspect advice that runs before controller methods matched by the pointcut.
     * <p>
     * Performs authentication and authorization checks using Keycloak, validates the user,
     * injects user details into the controller method, and handles exceptions and metrics.
     *
     * @param joinPoint the join point representing the intercepted method
     * @return the result of the intercepted method execution
     * @throws Throwable if authentication fails or the intercepted method throws an exception
     */
    @Around("controllerClassMethodsPointcut()")
    public Object beforeControllerAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        String currentAPI = joinPoint.getSignature().getName();
        long startTime = System.currentTimeMillis();
        Object result = null;

        log.trace("AuthAspect triggered for API: {}", currentAPI);

        if (request == null) {
            log.error("Request object is null");
            healthMetrics.updateHealApiServiceErrors();
            throw new Exception("Request is null");
        }

        String authKey = request.getHeader(Constants.AUTHORIZATION);
        if (authKey == null || authKey.trim().isEmpty()) {
            log.warn("Missing Authorization header in API: {}", currentAPI);
            healthMetrics.updateUnauthorizedRequests();
            throw new ControlCenterException("Missing or invalid Authorization token", String.valueOf(HttpStatus.UNAUTHORIZED.value()));
        }

        if (!keyCloakAuthService.isValidKey(authKey)) {
            log.warn("Invalid Keycloak token in API: {}", currentAPI);
            healthMetrics.updateUnauthorizedRequests();
            throw new ControlCenterException("Invalid Keycloak token", String.valueOf(HttpStatus.UNAUTHORIZED.value()));
        }

        BasicUserDetails userDetails = keyCloakAuthService.verifyUserStatus(authKey);
        if (userDetails == null || userDetails.getUserIdentifier() == null || !userDetails.getUserStatus()) {
            log.warn("Inactive or invalid user for API: {}", currentAPI);
            healthMetrics.updateUnauthorizedRequests();
            throw new ControlCenterException("User not active or identifier missing", String.valueOf(HttpStatus.UNAUTHORIZED.value()));
        }

        // Inject userDetails into the method argument of type BasicUserDetails
        Object[] args = joinPoint.getArgs();
        if (joinPoint.getSignature() instanceof MethodSignature signature) {
            Class<?>[] parameterTypes = signature.getParameterTypes();

            for (int i = 0; i < parameterTypes.length; i++) {
                if (parameterTypes[i].isAssignableFrom(BasicUserDetails.class)) {
                    args[i] = userDetails;
                    break; // Stop after finding the first matching argument
                }
            }
        }
        try {
            // Proceed with method execution
            result = joinPoint.proceed(args);
            return result;

        } catch (ControlCenterException e) {
            log.error("ControlCenterException in aspect for API [{}]: {}", currentAPI, e.getMessage(), e);
            throw new ServerException(e, "ControlCenterException occurred in UserAuthAspect: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected exception in aspect for API [{}]: {}", currentAPI, e.getMessage(), e);
            throw new ServerException(e, "Unexpected exception in UserAuthAspect: " + e.getMessage());
        } finally {
            long elapsed = System.currentTimeMillis() - startTime;
            log.debug("Execution time for API [{}]: {} ms", currentAPI, elapsed);
            healthMetrics.updateResponseTime(currentAPI, elapsed);
            headerConfigs.forEach(response::setHeader);
        }
    }

    /**
     * TODO: After Percona updates, revisit this method for GraphQL query access checks.
     * This method is currently commented out and should be updated or removed post-Percona.
     */
   /*private boolean checkUserAccess(int profileId, String gqlQuery) {
       List<AccessProfileGqlQueries> gqlQueries = cacheWrapper.getAccessProfileGqlQueries(profileId);
       if (gqlQueries == null || gqlQueries.isEmpty()) {
           return false;
       }

       Optional<AccessProfileGqlQueries> accessibleQueries = gqlQueries.stream()
               .filter(accessProfileGqlQueries -> accessProfileGqlQueries.getGqlQueryName().equals(gqlQuery))
               .findFirst();

       return accessibleQueries.isPresent();
   }*/
}