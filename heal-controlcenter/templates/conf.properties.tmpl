# =====================================
# PATHS
# =====================================
server.servlet.context-path={{ key "service/heal-cc/context-path" }}
setup.type=
filename.headers.properties=headers_details.json
filename.keycloak.details=keycloak_details.json

# =====================================
# KEYCLOAK CONFIG
# KeyCloak parameters, these are used for session management
# =====================================
keycloak.ip={{ key "service/keycloak/hostname" }}
keycloak.port={{ key "service/keycloak/port/https" }}
keycloak.user=appsoneadmin
keycloak.pwd={{ key "service/keycloak/password" }}

# =====================================
# SPRING DATASOURCE CONFIG
# =====================================
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=jdbc:mysql://{{ key "service/perconadb/ip" }}:{{ key "service/perconadb/port" }}/appsone?{{ key "service/keycloak/jdbcparams" }}
spring.datasource.username={{ key "service/perconadb/username" }}
spring.datasource.password={{ key "service/perconadb/password_ui" }}
spring.datasource.hikari.connection-timeout=5000
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=5
spring.datasource.hikari.poolName=Heal_ControlCenter_Pool

# ==========================================================
# Redis Server Configuration
# ==========================================================
spring.redis.cluster.nodes={{ key "service/redis/nodes" }}
spring.redis.ssl={{ key "service/redis/sslenabled" }}
spring.redis.max.idle.connections={{ key "service/redis/healcontrolcenter/max/idle/connections" }}
spring.redis.min.idle.connections={{ key "service/redis/healcontrolcenter/min/idle/connections" }}
spring.redis.max.total.connections={{ key "service/redis/healcontrolcenter/max/total/connections" }}
spring.redis.max.wait.secs={{ key "service/redis/healcontrolcenter/connections/max/wait/secs" }}
spring.redis.share.native.connection={{ key "service/redis/healcontrolcenter/share/native/connection" }}
spring.redis.username={{ key "service/redis/username" }}
spring.redis.password={{ key "service/redis/password/encrypted" }}
spring.redis.cluster.mode={{ key "service/redis/cluster/mode" }}

# ==========================================================
# RabbitMQ server configuration
# Basic configuration needed to communicate with RabbitMQ.
# HTTP mode of communication will be utilized in this case.
# ==========================================================
spring.rabbitmq.addresses={{ key "service/rabbitmq/addresses" }}
spring.rabbitmq.port={{ key "service/rabbitmq/port" }}
spring.rabbitmq.username={{ key "service/rabbitmq/username" }}
spring.rabbitmq.password={{ key "service/rabbitmq/password/encrypted" }}
spring.rabbitmq.ssl.enabled={{ key "service/rabbitmq/sslenable" }}
spring.rabbitmq.ssl.algorithm={{ key "service/rabbitmq/sslprotocol" }}
spring.rabbitmq.outputQueueName={{ key "service/rabbitmq/outputQueueName" }}

# ==========================================================
# SERVER SSL CONFIGURATION
# ==========================================================
server.port={{ key "service/heal-controlcenter/server/port" }}
server.ssl.key-store={{ key "service/heal-controlcenter/server/ssl/keystorepath" }}
server.ssl.key-store-type={{ key "service/heal-controlcenter/server/ssl/keystoretype" }}
server.ssl.key-store-password={{ key "service/heal-controlcenter/server/ssl/keystorepassword" }}
server.ssl.trust.store={{ key "service/heal-controlcenter/server/ssl/truststorepath" }}
server.ssl.trust.store.password={{ key "service/heal-controlcenter/server/ssl/truststorepassword" }}
ds.keycloak.ip={{ key "service/keycloak/hostname" }}
ds.keycloak.port={{ key "service/keycloak/port/https" }}
ds.keycloak.user=appsoneadmin
ds.keycloak.pwd={{ key "service/keycloak/password" }}

record.cache.stats={{ key "service/record/cache/stats" }}

redis.cache.mode={{ key "service/redis/cache/mode" }}
opensearch.cache.mode={{ key "service/opensearch/cache/mode" }}
percona.cache.mode={{ key "service/percona/cache/mode" }}

opensearch.cache.max.size={{ key "service/opensearch/cache/max/size" }}
opensearch.cache.expire.interval.minutes={{ key "service/opensearch/cache/expire/interval/minutes" }}

accounts_id_tenants.cache.max.size={{ key "service/accounts_id_tenants/cache/max/size" }}
accounts_id_tenants.cache.expire.interval.minutes={{ key "service/accounts_id_tenants/cache/expire/interval/minutes" }}

heal_index_zones.cache.max.size={{ key "service/heal_index_zones/cache/max/size" }}
heal_index_zones.cache.expire.interval.minutes={{ key "service/heal_index_zones/cache/expire/interval/minutes" }}

view_types.cache.max.size={{ key "service/view_types/cache/max/size" }}
view_types.cache.expire.interval.minutes={{ key "service/view_types/cache/expire/interval/minutes" }}

# =====================================
# OPENSEARCH NODES CONFIGURATION
# =====================================
opensearch.nodes={{ key "service/opensearch/nodes" }}
opensearch.nodes.eum={{ key "service/opensearch/nodes/eum" }}
opensearch.nodes.jaeger={{ key "service/opensearch/nodes/jaeger" }}
opensearch.nodes.jim={{ key "service/opensearch/nodes/jim" }}
opensearch.nodes.kpi={{ key "service/opensearch/nodes/kpi" }}
opensearch.nodes.misc={{ key "service/opensearch/nodes/misc" }}
opensearch.nodes.txn={{ key "service/opensearch/nodes/txn" }}

# =====================================
# OPENSEARCH CREDENTIALS CONFIGURATION
# =====================================
opensearch.username={{ key "service/opensearch/username" }}
opensearch.username.eum={{ key "service/opensearch/username/eum" }}
opensearch.username.jim={{ key "service/opensearch/username/jim" }}
opensearch.username.kpi={{ key "service/opensearch/username/kpi" }}
opensearch.username.misc={{ key "service/opensearch/username/misc" }}
opensearch.username.txn={{ key "service/opensearch/username/txn" }}

opensearch.password.encrypted={{ key "service/opensearch/password/encrypted" }}
opensearch.password.encrypted.eum={{ key "service/opensearch/password/encrypted/eum" }}
opensearch.password.encrypted.jim={{ key "service/opensearch/password/encrypted/jim" }}
opensearch.password.encrypted.kpi={{ key "service/opensearch/password/encrypted/kpi" }}
opensearch.password.encrypted.misc={{ key "service/opensearch/password/encrypted/misc" }}
opensearch.password.encrypted.txn={{ key "service/opensearch/password/encrypted/txn" }}