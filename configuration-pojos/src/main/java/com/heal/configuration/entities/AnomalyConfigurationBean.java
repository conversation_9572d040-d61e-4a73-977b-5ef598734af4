package com.heal.configuration.entities;

import com.heal.configuration.pojos.AnomalyConfiguration;
import com.heal.configuration.pojos.ApplicationAnomalyConfiguration;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnomalyConfigurationBean {
    /**
     * The unique identifier for the configuration record.
     * Maps to the 'id' column.
     */
    private int id;

    /**
     * The identifier of the account this configuration belongs to.
     * Maps to the 'account_id' column.
     */
    private int accountId;

    /**
     * The identifier of the application this configuration applies to.
     * Maps to the 'application_id' column.
     */
    private int applicationId;

    /**
     * The identifier of the user who last modified this record.
     * Maps to the 'user_details_id' column.
     */
    private String userDetailsId;

    /**
     * The timestamp when this record was created.
     * Maps to the 'created_time' column.
     */
    private String createdTime;

    /**
     * The timestamp when this record was last updated.
     * Maps to the 'updated_time' column.
     */
    private String updatedTime;

    /**
     * Flag to enable or disable anomaly detection for the 'Low' severity level.
     * Maps to the 'low_enable' column.
     */
    private boolean lowEnable;

    /**
     * Flag to enable or disable anomaly detection for the 'Medium' severity level.
     * Maps to the 'medium_enable' column.
     */
    private boolean mediumEnable;

    /**
     * Flag to enable or disable anomaly detection for the 'High' severity level.
     * Maps to the 'high_enable' column.
     */
    private boolean highEnable;

    /**
     * The window (in evaluation cycles) for closing an open anomaly.
     * Maps to the 'closing_window' column.
     */
    private int closingWindow;

    /**
     * The maximum number of consecutive missing data points before an anomaly is closed.
     * Maps to the 'max_data_breaks' column.
     */
    private int maxDataBreaks;

    public ApplicationAnomalyConfiguration mapToAnomalyConfiguration() {
        return ApplicationAnomalyConfiguration.builder()
                .lastModifiedBy(this.userDetailsId)
                .createdTime(this.createdTime)
                .updatedTime(this.updatedTime)
                .lowEnable(this.lowEnable)
                .mediumEnable(this.mediumEnable)
                .highEnable(this.highEnable)
                .closingWindow(this.closingWindow)
                .maxDataBreaks(this.maxDataBreaks)
                .applicationId(this.applicationId)
                .build();
    }

    public AnomalyConfiguration mapToAccountAnomalyConfiguration() {
        return AnomalyConfiguration.builder()
                .lastModifiedBy(this.userDetailsId)
                .createdTime(this.createdTime)
                .updatedTime(this.updatedTime)
                .lowEnable(this.lowEnable)
                .mediumEnable(this.mediumEnable)
                .highEnable(this.highEnable)
                .closingWindow(this.closingWindow)
                .maxDataBreaks(this.maxDataBreaks)
                .accountId(this.accountId)
                .build();
    }
}
