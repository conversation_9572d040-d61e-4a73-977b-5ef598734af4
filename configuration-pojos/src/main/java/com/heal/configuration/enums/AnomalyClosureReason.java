package com.heal.configuration.enums;

import io.netty.util.internal.logging.MessageFormatter;
import lombok.Getter;

/**
 * Represents possible reasons for closing an anomaly in the system.
 * Each reason includes a description explaining why the anomaly was closed.
 */
@Getter
public enum AnomalyClosureReason {
    /**
     * Violation configuration is missing or invalid.
     * Used By: Job Executor
     */
    METRIC_THRESHOLD_NOT_AVAILABLE("Violation configuration unavailable"),

    /**
     * Attribute threshold is not available.
     * Used By: Job Executor
     */
    ATTRIBUTE_THRESHOLD_NOT_AVAILABLE("Attribute threshold is not available "),

    /**
     * Violation is in service level, but we don't have service details
     * Used By: Job Executor
     */
    SERVICES_NOT_AVAILABLE("Violation is in service level but we don't have service details"),

    /**
     * Persistence and suppression value is zero
     * Used By: Job Executor
     */
    PERSISTENCE_SUPPRESSION_ZERO("persistence and suppression value is zero"),

    /**
     * Incomplete or missing data detected.
     * Used By: Job Executor
     */
    DATA_GAP("Data gap detected"),

    /**
     * Metric violation details are missing.
     * Used By: Job Executor
     */
    MISSING_VIOLATION_DETAILS("Metric violation details unavailable"),

    /**
     * Change in persistence-suppression configuration detected.
     * Used By: Event Detector, Job Executor
     */
    MISSING_MISMATCHED_PERSISTENCE_SUPPRESSION("Change in persistence-suppression configuration detected"),

    /**
     * Change in persistence-suppression configuration detected.
     * Used By: Event Detector, Job Executor
     */
    MISSING_MISMATCHED_ATTRIBUTE("Change in attribute configuration detected"),

    /**
     * Change in threshold configuration detected
     * Used By: Event Detector, Job Executor
     */
    MISMATCHED_OPERATION_NAME_THRESHOLDS("Change in threshold configuration detected"),

    /**
     * No data collected for the configured time window
     * Used By: Job Executor
     */
    MAX_DATA_BREAKS_REACHED("No data collected for the configured time window"),

    /**
     * No threshold violations observed within the configured close window
     * Used By: Event Detector, Data Emitter
     */
    CLOSING_WINDOW_COUNT_REACHED("No threshold violations observed within the configured close window"),

    /**
     * Metric details required for anomaly analysis are unavailable;
     * Used By: Job Executor
     */
    METRIC_DETAILS_UNAVAILABLE("Metric details unavailable"),

    /**
     * Violation configuration has expired (no longer valid);
     * Used By: Job Executor
     */
    CONFIG_EXPIRED("Config expired"),


    /**
     * Service-level violation cannot use instance-level or transaction-level thresholds;
     * Used By: Job Executor
     */
    SERVICE_VIOLATION_MISCONFIGURED("Service-level violation cannot use instance-level or transaction-level thresholds"),

    /**
     * Entity type is unrecognized or unsupported in the system;
     * Used By: Job Executor
     */
    UNKNOWN_ENTITY_TYPE("Unknown entity type");

    private final String description;

    AnomalyClosureReason(String description) {
        this.description = description;
    }

    /**
     * Formats a detailed message for this anomaly closure reason using SLF4J-style `{}` placeholders.
     * <p>
     * This method combines the enum name, its description, and a formatted message constructed
     * from the provided template and arguments. It works similarly to SLF4J's logging message format.
     * </p>
     *
     * @param template the message template containing `{}` placeholders for arguments
     * @param args     the arguments to replace placeholders in the template
     * @return a formatted string in the format: ENUM_NAME - description | formatted details
     *
     */
    public String format(String template, Object... args) {
        return this.name() + " - " + description + " | " +
                MessageFormatter.arrayFormat(template, args).getMessage();
    }
}