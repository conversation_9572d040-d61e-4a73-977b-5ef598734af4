# ==========================================================
# RabbitMQ server configuration
# Basic configuration needed to communicate with RabbitMQ.
# HTTP mode of communication will be utilized in this case.
# ==========================================================
spring.rabbitmq.addresses={{ key "service/rabbitmq/addresses" }}
spring.rabbitmq.username={{ key "service/rabbitmq/username" }}
spring.rabbitmq.password={{ key "service/rabbitmq/password/encrypted" }}
spring.rabbitmq.ssl.enabled={{ key "service/rabbitmq/sslenable" }}
spring.rabbitmq.ssl.algorithm={{ key "service/rabbitmq/sslprotocol" }}
spring.rabbitmq.aggregatedKpiInputQueueName={{ key "service/rabbitmq/staticaggregatedkpiqueue" }}
spring.rabbitmq.violatedEventInputQueueName={{ key "service/rabbitmq/violatedeventsqueue" }}
spring.rabbitmq.norThresholdQueueName={{ key "service/rabbitmq/northresholdsqueue" }}
spring.rabbitmq.norClosedThresholdQueueName={{ key "service/rabbitmq/norclosedthresholdqueue"}}
spring.rabbitmq.anomalyOutputSignalQueueName={{ key "service/rabbitmq/anomalysignalsqueue" }}
spring.rabbitmq.anomalyOutputActionQueueName={{ key "service/rabbitmq/anomalyactionsqueue" }}
spring.rabbitmq.anomalyOutputMLESignalQueueName={{ key "service/rabbitmq/anomalyOutputMLESignalQueueName" }}
spring.rabbitmq.anomalyMessagesQueueName={{ key "service/rabbitmq/anomaly-messages/queue/name" }}
spring.rabbitmq.anomaly.closure.events.queue.name={{ key "service/rabbitmq/anomaly/closure/events/queue/name" }}
spring.rabbitmq.anomaly.closure.prefetch.count={{ key "service/eventdetector/rabbitmq/anomaly/closure/prefetch/count" }}
spring.rabbitmq.anomaly.closure.acknowledgement.mode={{ key "service/eventdetector/rabbitmq/anomaly/closure/acknowledgement/mode" }}
spring.rabbitmq.anomaly.closure.concurrent.consumer.size={{ key "service/eventdetector/rabbitmq/anomaly/closure/concurrent/consumer/size" }}
spring.rabbitmq.aggregatedKpi.prefetchCount={{ key "service/eventdetector/rabbitmq/aggregatedKpi/prefetchCount" }}
spring.rabbitmq.aggregatedKpi.acknowledgementMode={{ key "service/eventdetector/rabbitmq/aggregatedKpi/ackMode" }}
spring.rabbitmq.aggregatedKpi.concurrentConsumerSize={{ key "service/eventdetector/rabbitmq/aggregatedKpi/consumerSize" }}
spring.rabbitmq.violatedEvent.prefetchCount={{ key "service/eventdetector/rabbitmq/violatedEvent/prefetchCount" }}
spring.rabbitmq.violatedEvent.acknowledgementMode={{ key "service/eventdetector/rabbitmq/violatedEvent/ackMode" }}
spring.rabbitmq.violatedEvent.concurrentConsumerSize={{ key "service/eventdetector/rabbitmq/violatedEvent/consumerSize" }}
spring.rabbitmq.norThreshold.prefetchCount={{ key "service/eventdetector/rabbitmq/norThreshold/prefetchCount" }}
spring.rabbitmq.norThreshold.acknowledgementMode={{ key "service/eventdetector/rabbitmq/norThreshold/ackMode" }}
spring.rabbitmq.norThreshold.concurrentConsumerSize={{ key "service/eventdetector/rabbitmq/norThreshold/consumerSize" }}
spring.rabbitmq.norClosedThreshold.prefetchCount={{ key "service/eventdetector/rabbitmq/norClosedThreshold/prefetchCount" }}
spring.rabbitmq.norClosedThreshold.acknowledgementMode={{ key "service/eventdetector/rabbitmq/norClosedThreshold/ackMode" }}
spring.rabbitmq.norClosedThreshold.concurrentConsumerSize={{ key "service/eventdetector/rabbitmq/norClosedThreshold/consumerSize" }}
spring.rabbitmq.thresholdForAggregatedKPIInSecs={{ key "service/eventdetector/rabbitmq/thresholdForAggregatedKPIInSecs" }}
spring.rabbitmq.thresholdForViolatedEventInSecs={{ key "service/eventdetector/rabbitmq/thresholdForViolatedEventInSecs" }}
spring.rabbitmq.thresholdForNorThresholdInSecs={{ key "service/eventdetector/rabbitmq/thresholdForNorThresholdInSecs" }}
spring.rabbitmq.thresholdForNorClosedThresholdInSecs={{ key "service/eventdetector/rabbitmq/thresholdForNorClosedThresholdInSecs" }}

# ==========================================================
#Redis Server Configuration
# ==========================================================
spring.redis.cluster.nodes={{ key "service/redis/nodes" }}
spring.redis.ssl={{ key "service/redis/sslenable" }}
spring.redis.max.idle.connections={{ key "service/eventdetector/redis/maxidleconnections" }}
spring.redis.min.idle.connections={{ key "service/eventdetector/redis/minidleconnections" }}
spring.redis.max.total.connections={{ key "service/eventdetector/redis/maxtotalconnections" }}
spring.redis.max.wait.secs={{ key "service/eventdetector/redis/connections/maxwaittimeinsec" }}
spring.redis.share.native.connection={{ key "service/eventdetector/redis/share/native/connection" }}
spring.redis.username={{ key "service/redis/username" }}
spring.redis.password={{ key "service/redis/password/encrypted" }}
spring.redis.cluster.mode= {{ key "service/redis/cluster/mode" }}

# ==========================================================
# Thread Pool Configuration
# ==========================================================
thread.pool.core.size={{key "service/eventdetector/threadpoolcoresize"}}
thread.pool.max.size={{key "service/eventdetector/threadpoolmaxsize"}}
thread.pool.queue.capacity={{key "service/eventdetector/threadpoolqueuecapacity"}}

# ==========================================================
# Miscellaneous
# batch.job.collection.interval in secs
# opensearch.data.push.schedule.initial.delay in secs
# opensearch.data.push.schedule.interval in secs
# ==========================================================
batch.job.suppression.value={{key "service/eventdetector/batchjobsuppressionvalue"}}
batch.job.collection.interval={{key "service/eventdetector/batchjobcollectioninterval"}}
signal.severity.high={{key "service/eventdetector/signalseverityhigh"}}
signal.severity.low={{key "service/eventdetector/signalseveritylow"}}
signal.severity.id.high={{key "service/eventdetector/signal/severity/id/high"}}
signal.severity.id.medium={{key "service/eventdetector/signal/severity/id/medium"}}
signal.severity.id.low={{key "service/eventdetector/signal/severity/id/low"}}
entity.type.instance={{key "service/eventdetector/entity/type/instance"}}
entity.type.transaction={{key "service/eventdetector/entity/type/transaction"}}
entity.type.service={{key "service/eventdetector/entity/type/service"}}
heal.transaction.component.identifier={{key "service/heal/transaction/component/identifier"}}
heal.batch.component.identifier={{key "service/heal/batch/component/identifier"}}
heal.global.account.identifier={{key "service/heal/global/account/identifier"}}
opensearch.data.push.schedule.initial.delay={{key "service/eventdetector/opensearch/data/push/schedule/initial/delay"}}
opensearch.data.push.schedule.interval={{key "service/eventdetector/opensearch/data/push/schedule/interval"}}
opensearch.batch.size={{key "service/eventdetector/opensearch/batch/size"}}
opensearch.batch.queue.max.size={{key "service/eventdetector/opensearch/batch/queue/max/size"}}
offset.from.gmt={{key "service/eventdetector/offset/from/gmt"}}

# =====================
# Cache configurations
# =====================
local.cache.timeout.minute={{ key "service/eventdetector/local/cache/timeout/minute" }}
delay.threshold.queue.max.size={{ key "service/eventdetector/delay/threshold/queue/max/size" }}
# ==========================================================
# Violated Event Out Of Order Details
# ==========================================================
kpi.data.outoforder.mins={{key "service/eventdetector/outOfBoundValueinMins"}}

# ==========================================================
# OpenSearch Details
# ==========================================================
opensearch.kpi.violations.index={{key "service/eventdetector/opensearch/index/kpi/violations" }}
opensearch.transaction.violations.index={{key "service/eventdetector/opensearch/index/transaction/violations" }}
opensearch.batchjob.violations.index={{key "service/eventdetector/opensearch/index/batchjob/violations" }}
opensearch.anomalies.index={{key "service/eventdetector/opensearch/index/anomalies" }}
opensearch.alerts.index={{key "service/eventdetector/opensearch/index/alerts" }}
opensearch.kpi.thresholds.index={{key "service/eventdetector/opensearch/index/kpi/thresholds" }}
opensearch.transaction.thresholds.index={{key "service/eventdetector/opensearch/index/transaction/thresholds" }}
opensearch.script.update={{key "service/eventdetector/opensearch/script/update" }}
opensearch.connection.io.reactor.size={{ key "service/eventdetector/opensearch/connection/io/reactor/size" }}

# ==========================================================
# Health Metrics Details
# ==========================================================
health.metrics.update.interval.milliseconds={{key "service/eventdetector/health/metrics/updateinterval/milliseconds" }}
health.metrics.log.interval.milliseconds={{key "service/eventdetector/health/metrics/loginterval/milliseconds" }}
management.endpoints.web.exposure.include={{key "service/eventdetector/management/endpoints/web/exposure/include" }}
management.endpoints.jmx.exposure.include={{key "service/eventdetector/management/endpoints/jmx/exposure/include" }}
management.endpoint.health.enabled={{key "service/eventdetector/management/endpoints/health/enabled" }}
management.endpoints.web.base-path=/measure
management.server.port={{key "service/eventdetector/management/server/port" }}
server.port={{key "service/eventdetector/server/port" }}
spring.jmx.enabled={{key "service/eventdetector/jmx/enabled" }}

# ==========================================================
# Local Cache Details
# ==========================================================
# Data is retrieved from local cache if mode is 0, and the redis cache if mode is 1.
redis.cache.mode={{ key "service/eventdetector/redis/cache/mode" }}
account.configuration.cache.expire.interval.minutes={{key "service/eventdetector/account/configurationcache/expire/interval/minutes" }}
account.configuration.cache.max.size={{key "service/eventdetector/account/configurationcache/max/size" }}
instance.configuration.cache.expire.interval.minutes={{key "service/eventdetector/instance/configurationcache/expire/interval/minutes" }}
instance.configuration.cache.max.size={{key "service/eventdetector/instance/configurationcache/max/size" }}
component.kpi.configuration.cache.expire.interval.minutes={{key "service/eventdetector/component/kpi/configurationcache/expire/interval/minutes" }}
component.kpi.configuration.cache.max.size={{key "service/eventdetector/component/kpi/configurationcache/max/size" }}
instance.kpi.configuration.cache.expire.interval.minutes={{key "service/eventdetector/instance/kpi/configurationcache/expire/interval/minutes" }}
instance.kpi.configuration.cache.max.size={{key "service/eventdetector/instance/kpi/configurationcache/max/size" }}
heal.types.configuration.cache.expire.interval.minutes={{key "service/eventdetector/heal/types/configurationcache/expire/interval/minutes" }}
heal.types.configuration.cache.max.size={{key "service/eventdetector/heal/types/configurationcache/max/size" }}
service.configuration.cache.expire.interval.minutes={{key "service/eventdetector/service/configurationcache/expire/interval/minutes" }}
service.configuration.cache.max.size={{key "service/eventdetector/service/configurationcache/max/size" }}
service.kpi.configuration.cache.expire.interval.minutes={{key "service/eventdetector/service/kpi/configurationcache/expire/interval/minutes" }}
service.kpi.configuration.cache.max.size={{key "service/eventdetector/service/kpi/configurationcache/max/size" }}
transactions.configuration.cache.expire.interval.minutes={{key "service/eventdetector/transactions/configurationcache/expire/interval/minutes" }}
transactions.configuration.cache.max.size={{key "service/eventdetector/transactions/configurationcache/max/size" }}
transactions.violationConfig.configuration.cache.expire.interval.minutes={{key "service/eventdetector/transactions/violationconfig/configurationcache/expire/interval/minutes" }}
transactions.violationConfig.configuration.cache.max.size={{key "service/eventdetector/transactions/violationconfig/configurationcache/max/size" }}
instance.kpis.configuration.cache.expire.interval.minutes={{key "service/eventdetector/instance/kpis/configurationcache/expire/interval/minutes" }}
instance.kpis.configuration.cache.max.size={{key "service/eventdetector/instance/kpis/configurationcache/max/size" }}
component.kpis.configuration.cache.expire.interval.minutes={{key "service/eventdetector/component/kpis/configurationcache/expire/interval/minutes" }}
component.kpis.configuration.cache.max.size={{key "service/eventdetector/component/kpis/configurationcache/max/size" }}
applications.configuration.cache.expire.interval.minutes={{key "service/eventdetector/applications/configurationcache/expire/interval/minutes" }}
applications.configuration.cache.max.size={{key "service/eventdetector/applications/configurationcache/max/size" }}
application.services.configuration.cache.expire.interval.minutes={{key "service/eventdetector/application/services/configurationcache/expire/interval/minutes" }}
application.services.configuration.cache.max.size={{key "service/eventdetector/application/services/configurationcache/max/size" }}
service.instances.configuration.cache.expire.interval.minutes={{key "service/eventdetector/service/instances/configurationcache/expire/interval/minutes" }}
service.instances.configuration.cache.max.size={{key "service/eventdetector/service/instances/configurationcache/max/size" }}
service.transactions.configuration.cache.expire.interval.minutes={{key "service/eventdetector/service/transactions/configurationcache/expire/interval/minutes" }}
service.transactions.configuration.cache.max.size={{key "service/eventdetector/service/transactions/configurationcache/max/size" }}


# ==========================================================
# Instance KPI threshold Closure
# ==========================================================
closed.thresholds.duration.days={{key "service/eventdetector/closed/thresholds/duration/days" }}

# ==========================================================
# Raw Anomalies Alerts
# ==========================================================
forward.raw.anomalies={{key "service/eventdetector/forward/raw/anomalies" }}
