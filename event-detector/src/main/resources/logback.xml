<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/event-detector.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/event-detector_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>15MB</maxFileSize>
            <totalSizeCap>100MB</totalSizeCap>
            <maxHistory>5</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="CORE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/event-detector-core.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/event-detector-core_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>15MB</maxFileSize>
            <totalSizeCap>100MB</totalSizeCap>
            <maxHistory>5</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="STATS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/event-detector-stats.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/event-detector-stats_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>15MB</maxFileSize>
            <totalSizeCap>100MB</totalSizeCap>
            <maxHistory>5</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.heal.event.detector" level="DEBUG" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="com.heal.event.detector.scheduler.StatisticsScheduler" level="DEBUG" additivity="false">
        <appender-ref ref="STATS_FILE"/>
    </logger>

    <root level="DEBUG">
        <appender-ref ref="CORE_FILE"/>
    </root>
</configuration>