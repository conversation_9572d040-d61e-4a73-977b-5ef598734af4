package com.heal.event.detector.core;

import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.Alerts;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.pojos.enums.PersistenceSuppressionStatus;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.StringUtils;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PersistenceSuppression {

    @Autowired
    RedisUtilities redisUtilities;
    @Autowired
    CacheWrapper cacheWrapper;
    @Autowired
    AnomalyManagementService anomalyManagementService;

    @Value("${signal.severity.id.high:433}")
    String highSeverityIdSignal;

    @Value("${signal.severity.id.medium:432}")
    String mediumSeverityIdSignal;

    @Value("${signal.severity.id.low:431}")
    String lowSeverityIdSignal;

    @Value("${entity.type.instance:INSTANCE}")
    String entityTypeInstance;

    @Value("${entity.type.transaction:TRANSACTION}")
    String entityTypeTransaction;

    @Value("${entity.type.service:SERVICE}")
    String entityTypeService;

    @Value("${heal.transaction.component.identifier:Transaction}")
    String transactionComponentIdentifier;

    private static final String AE_PREFIX = "AE";
    private static final String AE_SPLITTER = "-";

    /**
     * This method applies persistence suppression logic to create or update anomalies based on the violated data.
     * It checks the violation count against the configured persistence and suppression values.
     *
     * @param violatedData The data that has violated the KPI thresholds.
     *
     * @return AnomalyAccountPojo if an anomaly is created or updated, null otherwise.
     */
    public AnomalyAccountPojo applyPersistenceSuppressionCreateUpdateAnomaly(ViolatedData violatedData) {

        int isInformatic = 0;
        String entityType = entityTypeInstance;
        String categoryId = "";
        String kpiIdentifier = "";
        if (violatedData.getEventType().equals(ViolationEventType.KPI_VIOLATION)) {
            CompInstKpiEntity kpiDetails = cacheWrapper.getInstanceKPIDetails(violatedData.getAccountId(), violatedData.getInstanceId(), Integer.parseInt(violatedData.getKpiId()));
            if (kpiDetails == null) {
                log.error("No CompInstKpiEntity found for accountId: {}, instanceId: {}, kpiId: {}", violatedData.getAccountId(), violatedData.getInstanceId(), violatedData.getKpiId());
                return null;
            }
            isInformatic = kpiDetails.getIsInfo();
            categoryId = kpiDetails.getCategoryDetails().getIdentifier();
            kpiIdentifier = kpiDetails.getIdentifier();
        }
        if (violatedData.getEventType().equals(ViolationEventType.TXN_VIOLATION)) {
            ComponentKpiEntity componentKPIDetails = cacheWrapper.getComponentKPIDetails(violatedData.getAccountId(), transactionComponentIdentifier, violatedData.getKpiId());
            if (componentKPIDetails == null) {
                log.error("No ComponentKpiEntity found for accountId: {}, kpiId: {}", violatedData.getAccountId(), violatedData.getKpiId());
                return null;
            }
            isInformatic = componentKPIDetails.getIsInfo();
            entityType = entityTypeTransaction;
            categoryId = componentKPIDetails.getCategoryDetails().getIdentifier();
            kpiIdentifier = componentKPIDetails.getIdentifier();
        }

        // Defensive null checks
        ViolationDetails violationDetails = violatedData.getViolationDetails();
        if (violationDetails == null) {
            log.error("No ViolationDetails found for violatedData: {}", violatedData);
            return null;
        }
        ViolationStatus violationStatusHighestSeverity = violationDetails.getViolationStatusMap()
                .get(violationDetails.getHighestViolatedSeverity());
        if (violationStatusHighestSeverity == null) {
            log.error("No ViolationStatus found for highest severity {} in violatedData: {}", violationDetails.getHighestViolatedSeverity(), violatedData);
            return null;
        }

        // Defensive copy of metaData to avoid side effects
        Map<String, String> metaData = new HashMap<>(violatedData.getMetaData());
        metaData.put("isMaintenanceExcluded", violatedData.isMaintenanceExcluded() ? "1" : "0");
        metaData.put("violationLevel", violationDetails.getViolationLevel());
        if (!metaData.containsKey("serviceIdentifier")) {
            metaData.put("serviceIdentifier", String.join(", ", violatedData.getServiceList()));
        }
        metaData.put("isInformatic", String.valueOf(isInformatic));
        metaData.putIfAbsent("kpiType", violatedData.getKpiType().name());
        metaData.put("attributeName", violatedData.getKpiAttribute());
        metaData.put("anomalyLevel", violationDetails.getViolationLevel());
        metaData.put("closingWindow", String.valueOf(violationDetails.getClosingWindowCount()));
        metaData.put("maxDataBreaks", String.valueOf(violationDetails.getDataBreakCount()));
        metaData.put("remaindersCount", "0");
        metaData.put("closeWindowResetCount", String.valueOf(violationDetails.getCloseWindowResetCount()));
        metaData.put("dataBreakResetCount", String.valueOf(violationDetails.getDataBreakResetCount()));

        Anomalies anomalies = Anomalies.builder()
                .anomalyStartTime(violationStatusHighestSeverity.getViolationStartTime())
                .anomalyEndTime(violationStatusHighestSeverity.getLastViolationTime())
                .entityId(entityType.equalsIgnoreCase(entityTypeTransaction) ? violatedData.getTransactionId() : violatedData.getInstanceId())
                .entityType(entityType)
                .kpiId(Long.parseLong(violatedData.getKpiId()))
                .kpiAttribute(violatedData.getKpiAttribute())
                .categoryId(categoryId)
                .serviceId(new HashSet<>(violatedData.getServiceList()))
                .thresholdType(violatedData.getThresholdType())
                .operationType(violatedData.getOperationType())
                .metadata(metaData)
//                 TODO: Is identifiedTime value being set correct?
                .identifiedTime(violatedData.getViolationTime())
                .lastSeverityId(Integer.parseInt(violationDetails.getHighestViolatedSeverity()))
                .violationCountsReset(violationDetails.getViolationCountsReset())
                .severitiesEnabled(violationDetails.getSeveritiesEnabled())
                .low(violationDetails.getViolationStatusMap().get(lowSeverityIdSignal))
                .medium(violationDetails.getViolationStatusMap().get(mediumSeverityIdSignal))
                .high(violationDetails.getViolationStatusMap().get(highSeverityIdSignal))
                .build();

        AnomalyAccountPojo anomalyAccountPojo = AnomalyAccountPojo.builder()
                .accountIdentifier(violatedData.getAccountId())
                .anomalyDetails(anomalies)
                .build();

        Alerts alerts = Alerts.builder()
                .identifiedTime(violatedData.getViolationTime())
                .entityId(entityType.equalsIgnoreCase(entityTypeTransaction) ? violatedData.getTransactionId() : violatedData.getInstanceId())
                .entityType(entityType)
                .severityId(violationDetails.getHighestViolatedSeverity())
                .categoryId(categoryId)
                .kpiId(Long.parseLong(violatedData.getKpiId()))
                .kpiAttribute(violatedData.getKpiAttribute())
                .kpiIdentifier(kpiIdentifier)
                .serviceId(new HashSet<>(violatedData.getServiceList()))
                .metadata(metaData)
                .operationType(violatedData.getOperationType())
                .thresholdType(violatedData.getThresholdType())
                .persistence(String.valueOf(violationStatusHighestSeverity.getPersistence()))
                .suppression(String.valueOf(violationStatusHighestSeverity.getSuppression()))
                .thresholds(violatedData.getThresholds())
                .anomalyScore(metaData.get("anomalyScore"))
                .value(violatedData.getValue())
                .build();

        int violationCount = violationStatusHighestSeverity.getViolationCount();
        int persistence = violationStatusHighestSeverity.getPersistence();
        int suppression = violationStatusHighestSeverity.getSuppression();

        if (violationCount < persistence) {
            log.info("Violation count {} is less than persistence {} for kpiId {} mapped to instance {} and service {}. No anomaly created.",
                    violationCount, persistence, violatedData.getKpiId(), violatedData.getInstanceId(), violatedData.getServiceList());
            return null;
        }

        if (violationCount == persistence) {
            log.info("Violation count {} is equal to persistence {} for kpiId {} mapped to instance {} and service {}. Anomaly created.",
                    violationCount, persistence, violatedData.getKpiId(), violatedData.getInstanceId(), violatedData.getServiceList());

            anomalies.setStartSeverityId(Integer.parseInt(violationDetails.getHighestViolatedSeverity()));
            anomalyAccountPojo.setAnomalyDetails(anomalies);
            alerts.setAlertType(PersistenceSuppressionStatus.PERSISTENCE.name().toLowerCase());

            if (violationDetails.getAnomalyEventStatus() != null && violationDetails.getAnomalyEventStatus().getAnomalyEventId() != null) {
                return anomalyManagementService.updateAnomaly(anomalyAccountPojo, alerts, violatedData.getAppIds(), violationDetails);
            } else {
                return anomalyManagementService.createAnomaly(anomalyAccountPojo, alerts, violatedData.getAppIds(), violationDetails);
            }
        } else if ((violationCount - persistence) % suppression == suppression - 1) {
            log.info("Violation count {} is greater than persistence {} + suppression {} for kpiId {} mapped to instance {} and service {}. Anomaly updated.",
                    violationCount, persistence, suppression, violatedData.getKpiId(), violatedData.getInstanceId(), violatedData.getServiceList());

            alerts.setAlertType(PersistenceSuppressionStatus.SUPPRESSION.name().toLowerCase());

            return anomalyManagementService.updateAnomaly(anomalyAccountPojo, alerts, violatedData.getAppIds(), violationDetails);
        } else {
            log.info("Violation count {} is suppressed for persistence {} and suppression {} for kpiId {} mapped to instance {} and service {}. No anomaly created.",
                    violationCount, persistence, suppression, violatedData.getKpiId(), violatedData.getInstanceId(), violatedData.getServiceList());
        }
        return null;
    }

    /**
     * Returns persistence and suppression values for the given context (account,
     * instance/txn, kpi, service, severity).
     * Looks up the correct ServiceConfiguration and severity
     *
     * @param entityType              INSTANCE or TRANSACTION
     * @param accountIdentifier       account id
     * @param instanceIdentifier      instance id (or txn id for TRANSACTION)
     * @param kpiId                   KPI id
     * @param serviceId               service id
     * @param severityId              severity id (string)
     * @return Map with keys "persistence", "suppression" and "collectionInterval"
     *         or null if not found
     */
    public Map<String, Integer> getPersSuppAndCollectionIntervalAtServiceConf(String entityType, String accountIdentifier, String instanceIdentifier,
                                                                              String kpiId, String serviceId, String severityId) {
        int persistence = 0;
        int suppression = 0;
        int collectionInterval = 60;

        Map<String, Integer> persSupp = new HashMap<>();

        if (entityType.equals(entityTypeInstance)) {
            CompInstKpiEntity kpiDetails = cacheWrapper.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, Integer.parseInt(kpiId));
            if (kpiDetails == null) {
                return persSupp;
            }
            collectionInterval = kpiDetails.getCollectionInterval();
        }

        if (entityType.equals(entityTypeTransaction)) {
            ComponentKpiEntity componentKPIDetails = cacheWrapper.getComponentKPIDetails(accountIdentifier, transactionComponentIdentifier, kpiId);
            if (componentKPIDetails == null) {
                return persSupp;
            }
            collectionInterval = componentKPIDetails.getCommonVersionDetails().get(0).getCollectionInterval();
        }

        persSupp.put("collectionInterval", collectionInterval);

        PersistenceSuppressionConfiguration persistenceSuppressionConfiguration;
        try {
            persistenceSuppressionConfiguration = getPersistenceSuppressionServiceConf(accountIdentifier, serviceId, collectionInterval);
        } catch (Exception e) {
            log.error("Error fetching service configuration for account: {}, instance: {}, kpiId: {}, collection interval: {}, and services: {}. Details: ",
                    accountIdentifier, instanceIdentifier, kpiId, collectionInterval, serviceId, e);
            return persSupp;
        }

        if (persistenceSuppressionConfiguration == null) {
            log.warn("Persistence suppression conf unavailable for account: {}, instance: {}, kpiId: {}, collection interval: {}, and services: {}",
                    accountIdentifier, instanceIdentifier, kpiId, collectionInterval, serviceId);
            return persSupp;
        }

        if (severityId != null) {
            if (severityId.equals(lowSeverityIdSignal)) {
                persistence = persistenceSuppressionConfiguration.getLowPersistence();
                suppression = persistenceSuppressionConfiguration.getLowSuppression();
            } else if (severityId.equals(mediumSeverityIdSignal)) {
                persistence = persistenceSuppressionConfiguration.getMediumPersistence();
                suppression = persistenceSuppressionConfiguration.getMediumSuppression();
            } else if (severityId.equals(highSeverityIdSignal)) {
                persistence = persistenceSuppressionConfiguration.getHighPersistence();
                suppression = persistenceSuppressionConfiguration.getHighSuppression();
            }
        }

        persSupp.put("persistence", persistence);
        persSupp.put("suppression", suppression);

        return persSupp;
    }

    /**
     * This method checks if the persistence suppression condition is met for a given
     * severity ID in the ViolationDetails.
     *
     * @param violationDetails The ViolationDetails object containing the violation status map.
     * @param severityId       The severity ID to check against.
     * @return                 PersistenceSuppressionStatus indicating whether the persistence suppression condition is met
     */
    public PersistenceSuppressionStatus persistenceSuppressionMetForSeverity(ViolationDetails violationDetails, String severityId) {
        if (violationDetails == null || violationDetails.getViolationStatusMap() == null) {
            log.error("ViolationDetails or ViolationStatusMap is null for severityId: {}", severityId);
            return PersistenceSuppressionStatus.ERROR;
        }
        if (!violationDetails.getViolationStatusMap().containsKey(severityId)) {
            log.error("SeverityId {} not found in ViolationStatusMap", severityId);
            return PersistenceSuppressionStatus.ERROR;
        }

        ViolationStatus violationStatus = violationDetails.getViolationStatusMap().get(severityId);
        if (violationStatus == null) {
            log.error("No ViolationStatus found for severityId: {}", severityId);
            return PersistenceSuppressionStatus.ERROR;
        }

        int violationCount = violationStatus.getViolationCount();
        int persistence = violationStatus.getPersistence();
        int suppression = violationStatus.getSuppression();

        if (violationCount < persistence) {
            return PersistenceSuppressionStatus.PERSISTENCE;
        }
        if (violationCount == persistence) {
            return PersistenceSuppressionStatus.CONTINUE;
        }
        return (violationCount - persistence) % suppression == suppression - 1 ? PersistenceSuppressionStatus.CONTINUE : PersistenceSuppressionStatus.SUPPRESSION;
    }

    /**
     * Retrieves the ServiceConfiguration for a given account and service.
     *
     * @param accountId The account ID.
     * @param serviceId       The service name.
     * @return The ServiceConfiguration or null if not found.
     */
    public ServiceConfiguration getServiceConfiguration(String accountId, String serviceId) {
        com.heal.configuration.pojos.Service service = cacheWrapper.getServiceDetails(accountId, serviceId);
        if (service == null) {
            return null;
        }

        ServiceConfiguration serviceConfiguration = service.getServiceConfiguration();

        if (Objects.isNull(serviceConfiguration)) {
            return null;
        }

        return serviceConfiguration;
    }

    /**
     * Retrieves the PersistenceSuppressionConfiguration for a given account, service, and collection interval.
     *
     * @param accountId          The account ID.
     * @param serviceId                The service name.
     * @param collectionInterval The collection interval in seconds.
     * @return The PersistenceSuppressionConfiguration or null if not found.
     */
    public PersistenceSuppressionConfiguration getPersistenceSuppressionServiceConf(String accountId, String serviceId, int collectionInterval) {
        ServiceConfiguration serviceConfiguration = getServiceConfiguration(accountId, serviceId);

        if (serviceConfiguration == null) {
            return null;
        }

        long collectionIntervalInMinute = collectionInterval / 60;

        return serviceConfiguration.getPersistenceSuppressionConfigurations().stream()
                .filter(conf -> conf.getStartCollectionInterval() <= collectionIntervalInMinute &&
                        collectionIntervalInMinute <= conf.getEndCollectionInterval())
                .findFirst()
                .orElse(null);
    }

    public String getAnomalyIdentifier(ViolatedData value, boolean isTxn) {
        String accountId = value.getAccountId();
        String instId = value.getInstanceId();

        Account accountData = cacheWrapper.getAccountDetails(accountId);

        String anomalyEvent = null;

        if (isTxn) {
            Transaction txnData = cacheWrapper.getTransactionDetails(accountId, value.getTransactionId());
            if (Objects.nonNull(accountData) && Objects.nonNull(txnData)) {
                anomalyEvent = AE_PREFIX + AE_SPLITTER + accountData.getId() +
                        AE_SPLITTER +
                        txnData.getId() +
                        AE_SPLITTER +
                        value.getKpiId() +
                        AE_SPLITTER +
                        "T" +
                        AE_SPLITTER +
                        (value.getViolationFor().equals("SOR") ? "S" : "N") +
                        AE_SPLITTER +
                        TimeUnit.MILLISECONDS.toMinutes(value.getViolationTime());
            }
        } else {
            boolean isGroupKpi = value.getMetaData() != null && Boolean.parseBoolean(value.getMetaData().getOrDefault("isGroupKpi", (StringUtils.isEmpty(value.getKpiAttribute())
                    && !value.getKpiAttribute().trim().equals(Constants.COMMON_ATTRIBUTE)) ? "true" : "false"));

            CompInstClusterDetails instData = cacheWrapper.getInstanceDetails(accountId, instId);
            if (Objects.nonNull(accountData) && Objects.nonNull(instData)) {
                anomalyEvent = AE_PREFIX + AE_SPLITTER + accountData.getId() +
                        AE_SPLITTER +
                        instData.getId() +
                        AE_SPLITTER +
                        value.getKpiId() +
                        AE_SPLITTER +
                        "C" +
                        AE_SPLITTER +
                        (value.getViolationFor().equals("SOR") ? "S" : "N") +
                        AE_SPLITTER +
                        (!StringUtils.isEmpty(value.getKpiAttribute()) ? (isGroupKpi ? String.valueOf(value.getKpiAttribute().trim().hashCode()) : value.getKpiAttribute().trim()).concat(AE_SPLITTER) : "") +
                        TimeUnit.MILLISECONDS.toMinutes(value.getViolationTime());
            }
        }

        if (anomalyEvent == null) {
            String anomalyIdPrefix = value.getViolationFor().equals("SOR") ? "AE-S-" : "AE-N-";
            anomalyEvent = anomalyIdPrefix + UUID.randomUUID();
        }

        return anomalyEvent;
    }
}
