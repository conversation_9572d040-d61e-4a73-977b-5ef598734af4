package com.heal.event.detector.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Slf4j
@Configuration
public class ExecutorConfig {

    @Value("${thread.pool.core.size:50}")
    private int corePoolSize;

    @Value("${thread.pool.max.size:100}")
    private int maxPoolSize;

    @Value("${thread.pool.queue.capacity:5000}")
    private int queueCapacity;

    @Bean("ThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor getAsyncExecutor() {
        log.info("Setting up thread executor");

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setThreadNamePrefix("ED-Worker-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(10);
        executor.setQueueCapacity(queueCapacity);
        executor.initialize();

        return executor;
    }
}
