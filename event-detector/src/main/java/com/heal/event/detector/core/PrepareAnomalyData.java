package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.pojos.ViolationStatus;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 25-01-2022
 */
@Slf4j
@Component
public class PrepareAnomalyData {

    @Value("${entity.type.transaction:TRANSACTION}")
    private String entityTypeTransaction;

    public List<AnomalyEventProtos.AnomalyEvent> getAllAnomalies(List<AnomalyAccountPojo> anomalyAccountPojos) {
        try {
            return anomalyAccountPojos.parallelStream()
                    .map(anomalyAccountPojo -> {
                        Anomalies anomalies = anomalyAccountPojo.getAnomalyDetails();
                        List<String> appIds = Arrays.asList(anomalies.getMetadata().get("appIds").split(","));

                        // Build AnomalyEvent from AnomalyAccountPojo
                        AnomalyEventProtos.AnomalyEvent.Builder anomalyEventBuilder = AnomalyEventProtos.AnomalyEvent.newBuilder();
                        anomalyEventBuilder.setAccountId(anomalyAccountPojo.getAccountIdentifier());
                        anomalyEventBuilder.addAllAppId(appIds);
                        anomalyEventBuilder.setAnomalyId(anomalies.getAnomalyId());
                        anomalyEventBuilder.setThresholdType(anomalies.getThresholdType());
                        anomalyEventBuilder.setOperationType(anomalies.getOperationType());
                        anomalyEventBuilder.setStartTimeGMT(anomalies.getAnomalyStartTime());
                        anomalyEventBuilder.setEndTimeGMT(anomalies.getAnomalyEndTime());
                        anomalyEventBuilder.setAnomalyTriggerTimeGMT(anomalies.getAnomalyCreatedTime());

                        // Build KpiInfo
                        AnomalyEventProtos.KpiInfo.Builder kpiInfo = AnomalyEventProtos.KpiInfo.newBuilder();
                        kpiInfo.setInstanceId(anomalies.getEntityId());
                        kpiInfo.setKpiId(String.valueOf(anomalies.getKpiId()));
                        kpiInfo.setKpiAttribute(anomalies.getKpiAttribute());
                        kpiInfo.setIsWorkload(anomalies.getEntityType().equals(entityTypeTransaction));
                        kpiInfo.addAllSvcId(anomalies.getServiceId());
                        anomalies.getMetadata().put("anomalyStatus", anomalies.getAnomalyStatus());
                        kpiInfo.putAllMetadata(anomalies.getMetadata());

                        kpiInfo.putAllMetadata(convertViolationStatusToMap(anomalies.getLow(), "low"));
                        kpiInfo.putAllMetadata(convertViolationStatusToMap(anomalies.getMedium(), "medium"));
                        kpiInfo.putAllMetadata(convertViolationStatusToMap(anomalies.getHigh(), "high"));

                        anomalyEventBuilder.setKpis(kpiInfo.build());

                        log.debug("Converted AnomalyAccountPojo to AnomalyEvent: {}", anomalyEventBuilder.build());
                        return anomalyEventBuilder.build();
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Exception while converting AnomalyAccountPojo to AnomalyEvent.", e);
        }
        return new ArrayList<>();
    }

    @NotNull
    private Map<String, String> convertViolationStatusToMap(ViolationStatus status, String level) {
        Map<String, String> map = new HashMap<>();
        if (status == null) return map;

        map.put(level+"_persistence", String.valueOf(status.getPersistence()));
        map.put(level+"_suppression", String.valueOf(status.getSuppression()));
        map.put(level+"_thresholdOperation", status.getOperationName());
        map.put(level+"_thresholdValue", String.valueOf(status.getViolationCount()));
        map.put(level+"_violationsResetCount", String.valueOf(status.getViolationsResetCount()));
        map.put(level+"_persistenceMeetCount", String.valueOf(status.getPersistenceMeetCount()));
        map.put(level+"_suppressionMeetCount", String.valueOf(status.getSuppressionMeetCount()));

        return map;
    }
}
