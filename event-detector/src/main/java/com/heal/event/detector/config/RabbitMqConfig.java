package com.heal.event.detector.config;

import com.heal.event.detector.service.EventReceiverFromQueue;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.connection.RabbitConnectionFactoryBean;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;

@Configuration
@Slf4j
public class RabbitMqConfig {

    @Value("${spring.rabbitmq.aggregatedKpiInputQueueName:static-aggregated-kpi}")
    public String aggregatedKpiInputQueueName;
    @Value("${spring.rabbitmq.violatedEventInputQueueName:violated-events}")
    public String violatedEventInputQueueName;
    @Value("${spring.rabbitmq.anomalyOutputSignalQueueName:anomaly-event-signal-messages}")
    public String anomalyOutputSignalQueueName;
    @Value("${spring.rabbitmq.anomalyOutputActionQueueName:anomaly-event-action-messages}")
    public String anomalyOutputActionQueueName;
    @Value("${spring.rabbitmq.anomalyOutputMLESignalQueueName:anomaly-event-mle-signal-messages}")
    public String anomalyOutputMLESignalQueueName;
    @Value("${spring.rabbitmq.anomalyMessagesQueueName:anomaly-messages}")
    public String anomalyMessagesQueueName;
    @Value("${spring.rabbitmq.anomaly.closure.events.queue.name:anomaly-closure-events}")
    public String anomalyClosureEventsQueueName;
    @Value("${spring.rabbitmq.norThresholdQueueName:nor-thresholds}")
    public String norThresholdQueueName;
    @Value("${spring.rabbitmq.norClosedThresholdQueueName:nor-closed-thresholds}")
    public String norClosedThresholdQueueName;
    @Value("${spring.rabbitmq.aggregatedKpi.prefetchCount:10}")
    public int prefetchCountAggKpi;
    @Value("${spring.rabbitmq.aggregatedKpi.acknowledgementMode:AUTO}")
    public String ackModeAggKpi;
    @Value("${spring.rabbitmq.aggregatedKpi.concurrentConsumerSize:1}")
    public int consumerSizeAggKpi;
    @Value("${spring.rabbitmq.violatedEvent.prefetchCount:10}")
    public int prefetchCountViolatedEvents;
    @Value("${spring.rabbitmq.violatedEvent.acknowledgementMode:AUTO}")
    public String ackModeViolatedEvents;
    @Value("${spring.rabbitmq.violatedEvent.concurrentConsumerSize:1}")
    public int consumerSizeViolatedEvents;
    @Value("${spring.rabbitmq.norThreshold.prefetchCount:10}")
    public int prefetchCountNorThresholds;
    @Value("${spring.rabbitmq.norThreshold.acknowledgementMode:AUTO}")
    public String ackModeNorThresholds;
    @Value("${spring.rabbitmq.norThreshold.concurrentConsumerSize:1}")
    public int consumerSizeNorThresholds;
    @Value("${spring.rabbitmq.norClosedThreshold.prefetchCount:10}")
    public int prefetchCountNorClosedThresholds;
    @Value("${spring.rabbitmq.norClosedThreshold.acknowledgementMode:AUTO}")
    public String ackModeNorClosedThresholds;
    @Value("${spring.rabbitmq.norClosedThreshold.concurrentConsumerSize:1}")
    public int consumerSizeNorClosedThresholds;
    @Value("${spring.rabbitmq.anomaly.closure.prefetch.count:10}")
    public int prefetchCountAnomalyClosure;
    @Value("${spring.rabbitmq.anomaly.closure.acknowledgement.mode:AUTO}")
    public String ackModeAnomalyClosure;
    @Value("${spring.rabbitmq.anomaly.closure.concurrent.consumer.size:1}")
    public int consumerSizeAnomalyClosure;
    @Autowired
    private HealthMetrics metrics;
    @Lazy
    @Autowired
    EventReceiverFromQueue receiver;

    @Qualifier("aggregatedKpiInputQueue")
    @Bean
    public Queue createAggregatedKpiInputQueue() {
        metrics.setReadAggregatedKpiQueueName(aggregatedKpiInputQueueName);
        return new Queue(aggregatedKpiInputQueueName, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null);
    }

    @Qualifier("anomalyInputQueue")
    @Bean
    public Queue createViolatedEventInputQueue() {
        metrics.setReadViolatedEventQueueName(violatedEventInputQueueName);
        return new Queue(violatedEventInputQueueName, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null);
    }

    @Qualifier("anomalyOutputSignalQueue")
    @Bean
    public Queue createAnomalyOutputSignalQueue() {
        metrics.setWriteAnomalyOutputSignalQueueName(anomalyOutputSignalQueueName);
        return new Queue(anomalyOutputSignalQueueName, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null);
    }

    @Qualifier("anomalyOutputActionQueue")
    @Bean
    public Queue createAnomalyOutputActionQueue() {
        metrics.setWriteAnomalyOutputActionQueueName(anomalyOutputActionQueueName);
        return new Queue(anomalyOutputActionQueueName, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null);
    }

    @Qualifier("anomalyOutputMLESignalQueue")
    @Bean
    public Queue createAnomalyOutputMLESignalQueue() {
        metrics.setWriteAnomalyOutputMLESignalQueueName(anomalyOutputMLESignalQueueName);
        return new Queue(anomalyOutputMLESignalQueueName, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null);
    }

    @Qualifier("norThresholdQueue")
    @Bean
    public Queue createNorThresholdQueue() {
        metrics.setReadNorThresholdQueueName(norThresholdQueueName);
        return new Queue(norThresholdQueueName, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null);
    }

    @Qualifier("norClosedThresholdQueue")
    @Bean
    public Queue createNorClosedThresholdQueue() {
        metrics.setReadNorClosedThresholdQueueName(norClosedThresholdQueueName);
        return new Queue(norClosedThresholdQueueName, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null);
    }

    @Qualifier("anomalyMessagesQueue")
    @Bean
    public Queue createAnomalyMessagesQueue() {
        metrics.setWriteAnomalyMessagesQueueName(anomalyMessagesQueueName);
        return new Queue(anomalyMessagesQueueName, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null);
    }

    @Qualifier("anomalyClosureEventsQueue")
    @Bean
    public Queue createAnomalyClosureEventsQueueName() {
        return new Queue(anomalyClosureEventsQueueName, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null);
    }

    @Bean
    public ConnectionFactory createSslConnectionFactory(RabbitProperties rabbitProperties) {
        RabbitConnectionFactoryBean factory = new RabbitConnectionFactoryBean();
        if (rabbitProperties.getUsername() != null) {
            factory.setUsername(rabbitProperties.getUsername());
        }
        if (rabbitProperties.getPassword() != null) {
            factory.setPassword(new String(Base64.getDecoder().decode(rabbitProperties.getPassword().trim()), StandardCharsets.UTF_8));
        }

        RabbitProperties.Ssl ssl = rabbitProperties.getSsl();
        if (ssl.getEnabled()) {
            factory.setUseSSL(true);
            factory.setEnableHostnameVerification(false);
            factory.setSslAlgorithm(ssl.getAlgorithm());
        }
        factory.setAutomaticRecoveryEnabled(true);
        factory.afterPropertiesSet();

        CachingConnectionFactory connectionFactory = null;
        try {
            connectionFactory = new CachingConnectionFactory(Objects.requireNonNull(factory.getRabbitConnectionFactory()));
            connectionFactory.setAddresses(rabbitProperties.getAddresses());
        } catch (Exception e) {
            log.error("Exception occurred while creating ConnectionFactory. " +
                    "Details: Addresses:{}, SSL:{} ", rabbitProperties.getAddresses(), rabbitProperties.getSsl(), e);
        }
        return connectionFactory;
    }

    @Qualifier("aggregatedKpiDataContainer")
    @Bean
    SimpleMessageListenerContainer aggregatedKpiContainer(ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(aggregatedKpiInputQueueName);
        container.setMessageListener(new MessageListenerAdapter(receiver, "receiveAggregatedKPIsData"));
        container.setAcknowledgeMode(AcknowledgeMode.valueOf(ackModeAggKpi));
        container.setConcurrentConsumers(consumerSizeAggKpi);
        container.setPrefetchCount(prefetchCountAggKpi);
        container.afterPropertiesSet();
        return container;
    }

    @Qualifier("violatedEventsContainer")
    @Bean
    SimpleMessageListenerContainer violatedEventsContainer(ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(violatedEventInputQueueName);
        container.setMessageListener(new MessageListenerAdapter(receiver, "receiveViolatedEventsData"));
        container.setPrefetchCount(prefetchCountViolatedEvents);
        container.setAcknowledgeMode(AcknowledgeMode.valueOf(ackModeViolatedEvents));
        container.setConcurrentConsumers(consumerSizeViolatedEvents);
        container.afterPropertiesSet();
        return container;
    }

    @Qualifier("thresholdDataContainer")
    @Bean
    SimpleMessageListenerContainer thresholdDataContainer(ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(norThresholdQueueName);
        container.setMessageListener(new MessageListenerAdapter(receiver, "receiveNorThresholdData"));
        container.setPrefetchCount(prefetchCountNorThresholds);
        container.setAcknowledgeMode(AcknowledgeMode.valueOf(ackModeNorThresholds));
        container.setConcurrentConsumers(consumerSizeNorThresholds);
        container.afterPropertiesSet();
        return container;
    }

    @Qualifier("closedThresholdDataContainer")
    @Bean
    SimpleMessageListenerContainer closedThresholdDataContainer(ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(norClosedThresholdQueueName);
        container.setMessageListener(new MessageListenerAdapter(receiver, "receiveNorClosedThresholdData"));
        container.setPrefetchCount(prefetchCountNorClosedThresholds);
        container.setAcknowledgeMode(AcknowledgeMode.valueOf(ackModeNorClosedThresholds));
        container.setConcurrentConsumers(consumerSizeNorClosedThresholds);
        container.afterPropertiesSet();
        return container;
    }

    @Qualifier("anomalyCloseContainer")
    @Bean
    SimpleMessageListenerContainer anomalyCloseContainer(ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(anomalyClosureEventsQueueName);
        container.setMessageListener(new MessageListenerAdapter(receiver, "receiveAnomalyCloseData"));
        container.setAcknowledgeMode(AcknowledgeMode.valueOf(ackModeAnomalyClosure));
        container.setConcurrentConsumers(consumerSizeAnomalyClosure);
        container.setPrefetchCount(prefetchCountAnomalyClosure);
        container.afterPropertiesSet();
        return container;
    }
}
