package com.heal.event.detector.utility;

import com.heal.configuration.pojos.opensearch.InstanceKpiThresholds;
import com.heal.configuration.pojos.opensearch.TransactionKpiThresholds;
import com.heal.event.detector.pojos.DelayThresholdQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.DelayQueue;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class LocalCache {

    @Value("${delay.threshold.queue.max.size:30000}")
    private int delayThresholdQueueMaxSize;

    @Autowired
    HealthMetrics metrics;

    public ConcurrentMap<String, InstanceKpiThresholds> instanceKpiThresholdsConcurrentHashMap = new ConcurrentHashMap<>();
    public ConcurrentMap<String, TransactionKpiThresholds> transactionKpiThresholdsConcurrentHashMap = new ConcurrentHashMap<>();

    public final BlockingQueue<DelayThresholdQueue> delayThresholdQueue = new DelayQueue<>();

    public void pushKpiThresholdData(DelayThresholdQueue data, InstanceKpiThresholds instanceKpiThresholds, TransactionKpiThresholds transactionKpiThresholds) {
        if (delayThresholdQueue.size() >= delayThresholdQueueMaxSize) {
            log.error("Maximum delay request queue limit {} breached.", delayThresholdQueueMaxSize);
            return;
        }

        Optional<DelayThresholdQueue> delay = delayThresholdQueue.stream().filter(c -> c.getKey().equalsIgnoreCase(data.getKey())).findFirst();
        if (delay.isPresent()) {
            metrics.updateDelayQueueRemovedKey();
            delayThresholdQueue.remove(delay.get());
        }


        delayThresholdQueue.offer(data);

        if (instanceKpiThresholds != null) {
            instanceKpiThresholdsConcurrentHashMap.put(data.getKey(), instanceKpiThresholds);
        }

        if (transactionKpiThresholds != null) {
            transactionKpiThresholdsConcurrentHashMap.put(data.getKey(), transactionKpiThresholds);
        }
    }

    public long getInstanceKpiThresholdsConcurrentHashMapSize() {
        return instanceKpiThresholdsConcurrentHashMap.size();
    }

    public long getTransactionKpiThresholdsConcurrentHashMapSize() {
        return transactionKpiThresholdsConcurrentHashMap.size();
    }

    public long getDelayThresholdQueueSize() {
        return delayThresholdQueue.size();
    }

    public long getDelayThresholdQueueMaxSize() {
        return delayThresholdQueueMaxSize;
    }
}
