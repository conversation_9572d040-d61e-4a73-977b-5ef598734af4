package com.heal.event.detector.repo;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.*;
import com.heal.event.detector.pojos.PersistenceSuppressionPojo;
import com.heal.event.detector.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class RedisUtilities {
    @Autowired
    RedisTemplate<String, Object> redisTemplate;

    @Autowired
    HealthMetrics metrics;

    @Autowired
    public ObjectMapper objectMapper;

    @Value("${entity.type.transaction:TRANSACTION}")
    String entityTypeTransaction;

    public Account getAccountDetails(String accIdentifier) {
        try {
            String key = "/accounts/" + accIdentifier;
            String hashKey = "ACCOUNT_DATA_" + accIdentifier;

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Account details unavailable for account identifier [{}]", accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier, 1);
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting to account details for account [{}]. ", accIdentifier, e);
            return null;
        }
    }

    public List<TenantOpenSearchDetails> getTenantOpenSearchDetails(String tenantIdentifier) {
        try {
            String key = "/tenants/" + tenantIdentifier + "/opensearch";
            String hashKey = "TENANTS_" + tenantIdentifier + "_OPENSEARCH";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Tenant details unavailable for tenant identifier [{}]", tenantIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots(key, 1);
                return new ArrayList<>();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting to tenant opensearch mapping details for tenant identifier [{}]. ", tenantIdentifier, e);
            return new ArrayList<>();
        }
    }

    public CompInstClusterDetails getInstanceDetails(String accIdentifier, String instance) {
        try {
            String key = "/accounts/" + accIdentifier + "/instances/" + instance;
            String hashKey = "ACCOUNTS_" + accIdentifier + "_INSTANCES_" + instance;

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Instance details unavailable for instance identifier [{}] mapped to account [{}]", instance, accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/instances/" + instance, 1);
                return null;
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error while getting instance details for instance identifier [{}] mapped to account [{}]. Details: ", instance, accIdentifier, e);
            return null;
        }
    }

    public List<BasicEntity> getServicesMappedToInstance(String accIdentifier, String instance) {
        try {
            String key = "/accounts/" + accIdentifier + "/instances/" + instance + "/services";
            String hashKey = "ACCOUNTS_" + accIdentifier + "_INSTANCES_" + instance + "_SERVICES";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Mapped Service details unavailable for instance identifier [{}] mapped to account [{}]", instance, accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/instances/" + instance + "/services", 1);
                return new ArrayList<>();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error while getting mapped service details for instance identifier [{}] mapped to account [{}]. Details: ", instance, accIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<BasicEntity> getApplicationsMappedToService(String accIdentifier, String serviceIdentifier) {
        try {
            String key = "/accounts/" + accIdentifier + "/services/" + serviceIdentifier + "/applications";
            String hashKey = "ACCOUNTS_" + accIdentifier + "_SERVICES_" + serviceIdentifier + "_APPLICATIONS";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Mapped Application details unavailable for service identifier [{}] mapped to account [{}]", serviceIdentifier, accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/services/" + serviceIdentifier + "/applications", 1);
                return new ArrayList<>();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error while getting mapped application details for service identifier [{}] mapped to account [{}]. Details: ", serviceIdentifier, accIdentifier, e);
            return new ArrayList<>();
        }
    }

    public CompInstKpiEntity getInstanceKPIDetails(String accIdentifier, String instanceIdentifier, int kpiId) {
        try {
            String key = "/accounts/" + accIdentifier + "/instances/" + instanceIdentifier + "/kpis/" + kpiId;
            String hashKey = "ACCOUNTS_" + accIdentifier + "_INSTANCES_" + instanceIdentifier + "_KPIS_" + kpiId;

            Object kpiDetailsObject = redisTemplate.opsForHash().get(key, hashKey);

            if (kpiDetailsObject == null) {
                log.error("KPI information unavailable for kpiId [{}] mapped to instance [{}] and account [{}].", kpiId, instanceIdentifier, accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/instances/" + instanceIdentifier + "/kpis/" + kpiId, 1);
                return null;
            }

            return objectMapper.readValue(kpiDetailsObject.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting to KPI details for kpiId [{}] mapped to instance [{}], account [{}].", kpiId, instanceIdentifier, accIdentifier, e);
            return null;
        }
    }

    public ComponentKpiEntity getComponentKPIDetails(String accIdentifier, String componentIdentifier, String kpiId) {
        try {
            String key = "/accounts/" + accIdentifier + "/components/" + componentIdentifier + "/kpis";
            String hashKey = "ACCOUNTS_" + accIdentifier + "_COMPONENTS_" + componentIdentifier + "_KPIS";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Component details unavailable in redis cache for account identifier {}, component Identifier {}.",
                        accIdentifier, componentIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/components/" + componentIdentifier + "/kpis", 1);
                return null;
            }

            List<ComponentKpiEntity> componentKpisList = objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });

            ComponentKpiEntity kpiEntity = componentKpisList.parallelStream().filter(k -> String.valueOf(k.getId()).equalsIgnoreCase(kpiId)).findAny().orElse(null);
            if (kpiEntity != null && kpiEntity.getStatus() == 0) {
                log.error("KPI is disabled for name:{}, identifier:{}, component:{}.", kpiEntity.getName(), kpiEntity.getIdentifier(), componentIdentifier);
                return null;
            }

            return kpiEntity;
        } catch (Exception e) {
            log.error("Error occurred while getting component details, account identifier:{}, component identifier:{}, " +
                    "Kpi identifier:{}.", accIdentifier, componentIdentifier, kpiId, e);
            return null;
        }
    }


    public List<ViewTypes> getMstTypes() {
        try {
            String key = "/heal/types";
            String hashKey = "HEAL_TYPES";

            Object mstTypes = redisTemplate.opsForHash().get(key, hashKey);

            if (mstTypes == null) {
                log.error("Master Types information unavailable for Heal.");
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/heal/types", 1);
                return new ArrayList<>();
            }

            return objectMapper.readValue(mstTypes.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting Master Types information for Heal.", e);
            return new ArrayList<>();
        }
    }

    public Service getServiceDetails(String accIdentifier, String service) {
        try {
            String key = "/accounts/" + accIdentifier + "/services/" + service;
            String hashKey = "ACCOUNTS_" + accIdentifier + "_SERVICES_" + service;

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Service details unavailable for service identifier [{}] mapped to account [{}]", service, accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/services/" + service, 1);
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting service details for service identifier [{}] mapped to account [{}].", service, accIdentifier, e);
            return null;
        }
    }

    public List<BasicEntity> getAllServices(String accIdentifier) {
        try {
            String key = "/accounts/" + accIdentifier + "/services";
            String hashKey = "ACCOUNTS_" + accIdentifier + "_SERVICES";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Service details unavailable for account [{}]", accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/services", 1);
                return new ArrayList<>();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting service details for account [{}].", accIdentifier, e);
            return new ArrayList<>();
        }
    }

    public BasicEntity getServiceDetailsFromServiceId(String accIdentifier, int serviceId) {
        List<BasicEntity> allServices = getAllServices(accIdentifier);
        return allServices.stream().filter(c -> c.getId() == serviceId).findFirst().orElse(null);
    }

    public KpiDetails getServiceKPIDetails(String accIdentifier, String srvIdentifier, int kpiId) {

        try {
            String key = "/accounts/" + accIdentifier + "/services/" + srvIdentifier + "/kpis/" + kpiId;
            String hashKey = "ACCOUNTS_" + accIdentifier + "_SERVICES_" + srvIdentifier + "_KPIS_" + kpiId;

            Object kpiDetailsObject = redisTemplate.opsForHash().get(key, hashKey);

            if (kpiDetailsObject == null) {
                log.debug("KPI [{}] information unavailable for service [{}] mapped to account [{}]", kpiId, srvIdentifier, accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/services/" + srvIdentifier + "/kpis/" + kpiId, 1);
                return null;
            }

            return objectMapper.readValue(kpiDetailsObject.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting service wise kpi details for KPI [{}] mapped to service [{}] and account [{}].", kpiId, srvIdentifier, accIdentifier, e);
            return null;
        }
    }

    public Transaction getTransactionDetails(String accIdentifier, String txn) {
        try {
            String key = "/accounts/" + accIdentifier + "/transactions/" + txn.hashCode();
            String hashKey = "ACCOUNTS_" + accIdentifier + "_TRANSACTIONS_" + txn.hashCode();

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);

            if (obj == null) {
                log.error("Transaction details unavailable for transaction identifier [{}] mapped to account [{}]", txn, accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/transactions/" + txn.hashCode(), 1);
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting transaction details for transaction [{}] mapped to account [{}].", txn, accIdentifier, e);
            return null;
        }
    }

    public List<TxnKPIViolationConfig> getTransactionViolationConfigDetails(String accIdentifier, String txn) {
        try {
            String key = "/accounts/" + accIdentifier + "/transactions/" + txn.hashCode() + "/violationConfig";
            String hashKey = "ACCOUNTS_" + accIdentifier + "_TRANSACTIONS_" + txn.hashCode() + "_VIOLATIONCONFIG";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);

            if (obj == null) {
                log.error("Transaction violation config details unavailable for transaction identifier [{}] mapped to account [{}]", txn, accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/transactions/" + txn.hashCode() + "/violationConfig", 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting transaction violation config details for transaction [{}] mapped to account [{}].", txn, accIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<MaintenanceDetails> getServiceMaintenanceDetails(String accountIdentifier, String serviceIdentifier) {
        try {
            String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/maintenanceDetails";
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_MAINTENANCE_DETAILS";

            Object maintenanceDetailsObject = redisTemplate.opsForHash().get(key, hashKey);

            if (maintenanceDetailsObject == null) {
                log.debug("Maintenance details information unavailable for service [{}] mapped to account [{}]", serviceIdentifier, accountIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/maintenanceDetails", 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(maintenanceDetailsObject.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting maintenance details for service [{}] mapped to account [{}].", serviceIdentifier, accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<MaintenanceDetails> getInstanceMaintenanceDetails(String accountIdentifier, String instanceIdentifier) {

        try {
            String key = "/accounts/" + accountIdentifier + "/instances/" + instanceIdentifier + "/maintenanceDetails";
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + instanceIdentifier + "_MAINTENANCE_DETAILS";

            Object maintenanceDetailsObject = redisTemplate.opsForHash().get(key, hashKey);

            if (maintenanceDetailsObject == null) {
                log.debug("Maintenance details information unavailable for instance [{}] mapped to account [{}]", instanceIdentifier, accountIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accountIdentifier + "/instances/" + instanceIdentifier + "/maintenanceDetails", 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(maintenanceDetailsObject.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting maintenance details for instance [{}] mapped to account [{}]. Details: ", instanceIdentifier, accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public PersistenceSuppressionPojo getPersistenceSuppressionDetails(String suffixKey) {
        String key = "/eventDetector/" + suffixKey;
        String hashKey = "EVENTDETECTOR_" + suffixKey;

        try {
            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.debug("Persistence Suppression details unavailable for key [{}] in redis cache.", key);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots(key, 1);
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting persistence suppression details for key [{}].", key, e);
            return null;
        }
    }

    public void deletePersistenceSuppressionDetails(String suffixKey) {
        String key = "/eventDetector/" + suffixKey;
        String hashKey = "EVENTDETECTOR_" + suffixKey;

        try {
            long count = redisTemplate.opsForHash().delete(key, hashKey);
            if (count > 0) {
                log.debug("{} removed successfully from redis cache.", key);
            } else {
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots(key, 1);
                log.debug("Persistence suppression related key {} unavailable in redis cache. Unable to delete it.", key);
            }
        } catch (Exception e) {
            log.error("Error occurred while deleting the key [{}] from cache.", key, e);
        }
    }

    public void putPersistenceSuppressionDetails(String suffixKey, PersistenceSuppressionPojo persistenceSuppressionPojo) {
        String persistenceSuppression;
        String key = "/eventDetector/" + suffixKey;
        String hashKey = "EVENTDETECTOR_" + suffixKey;
        try {
            persistenceSuppression = objectMapper.writeValueAsString(persistenceSuppressionPojo);
            redisTemplate.opsForHash().put(key, hashKey, persistenceSuppression);

            log.debug("Persistence suppression details pushed to redis cache. Key: {}", key);
        } catch (Exception e) {
            log.error("Error occurred while pushing key:[{}] into redis cache.", key, e);
        }
    }

    public PersistenceSuppressionPojo syncAndGetPersistenceSuppressionDetails(String key, long currentTimeCollectionTime) {
        PersistenceSuppressionPojo persistenceSuppressionPojo = getPersistenceSuppressionDetails(key);
        if (persistenceSuppressionPojo == null) {
            persistenceSuppressionPojo = new PersistenceSuppressionPojo();
        }

        persistenceSuppressionPojo.setViolationCount(persistenceSuppressionPojo.getViolationCount() + 1);
        persistenceSuppressionPojo.setLastViolationTime(currentTimeCollectionTime);
        putPersistenceSuppressionDetails(key, persistenceSuppressionPojo);

        return getPersistenceSuppressionDetails(key);
    }

    /**
     * Generates the Redis key and hashKey for violation details based on entity type.
     */
    private Map<String, String> generateViolationKeyAndHashKey(String accountIdentifier, String entityIdentifier, String entityType, String kpiId, String kpiAttributeName, String violationFor) {
        String kpiAttributeNameEncrypt = Base64.getUrlEncoder().encodeToString(kpiAttributeName.getBytes());
        String key = "/accounts/" + accountIdentifier + "/instances/" + entityIdentifier + "/kpis/" + kpiId + "/attributes/" + kpiAttributeNameEncrypt + "/violations/" + violationFor.toLowerCase();
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + entityIdentifier + "_KPIS_" + kpiId + "_ATTRIBUTES_" + kpiAttributeNameEncrypt + "_VIOLATIONS_" + violationFor;
        if (entityType.equalsIgnoreCase(entityTypeTransaction)) {
            String txnIdEncrypt = Base64.getUrlEncoder().encodeToString(entityIdentifier.getBytes());
            key = "/accounts/" + accountIdentifier + "/transactions/" + txnIdEncrypt + "/kpis/" + kpiId + "/attributes/" + kpiAttributeNameEncrypt + "/violations/" + violationFor.toLowerCase();
            hashKey = "ACCOUNTS_" + accountIdentifier + "_TRANSACTIONS_" + txnIdEncrypt + "_KPIS_" + kpiId + "_ATTRIBUTES_" + kpiAttributeNameEncrypt + "_VIOLATIONS_" + violationFor;
        }
        Map<String, String> result = new HashMap<>();
        result.put("key", key);
        result.put("hashKey", hashKey);
        return result;
    }

    public ViolationDetails getViolationDetails(String accountIdentifier, String entityIdentifier, String entityType, String kpiId, String kpiAttributeName, String violationFor) {
        Map<String, String> keyHashKeyPair = generateViolationKeyAndHashKey(accountIdentifier, entityIdentifier, entityType, kpiId, kpiAttributeName, violationFor);
        String key = keyHashKeyPair.get("key");
        String hashKey = keyHashKeyPair.get("hashKey");

        try {
            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.debug("violationDetails unavailable for key [{}] in redis cache.", key);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots(key, 1);
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting violationDetails for key [{}].", key, e);
            return null;
        }
    }

    public void deleteViolationDetails(String accountIdentifier, String entityIdentifier, String entityType, String kpiId, String kpiAttributeName, String violationFor) {
        Map<String, String> keyHashKeyPair = generateViolationKeyAndHashKey(accountIdentifier, entityIdentifier, entityType, kpiId, kpiAttributeName, violationFor);
        String key = keyHashKeyPair.get("key");
        String hashKey = keyHashKeyPair.get("hashKey");

        try {
            long count = redisTemplate.opsForHash().delete(key, hashKey);
            if (count > 0) {
                log.debug("{} removed successfully from redis cache.", key);
            } else {
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots(key, 1);
                log.debug("No keys named {} found in redis cache. Unable to delete it.", key);
            }
        } catch (Exception e) {
            log.error("Error occurred while deleting the key [{}] from cache.", key, e);
        }
    }

    public void putViolationDetails(String accountIdentifier, String entityIdentifier, String entityType, String kpiId, String kpiAttributeName, String violationFor, ViolationDetails violationDetailsNew) {
        String violationDetails;
        Map<String, String> keyHashKeyPair = generateViolationKeyAndHashKey(accountIdentifier, entityIdentifier, entityType, kpiId, kpiAttributeName, violationFor);
        String key = keyHashKeyPair.get("key");
        String hashKey = keyHashKeyPair.get("hashKey");
        try {
            violationDetails = objectMapper.writeValueAsString(violationDetailsNew);
            redisTemplate.opsForHash().put(key, hashKey, violationDetails);

            log.debug("{} info pushed to redis cache.", key);
        } catch (Exception e) {
            log.error("Error occurred while pushing key:[{}] into redis cache.", key, e);
        }
    }

    public List<CompInstKpiEntity> getInstanceKPIDetails(String accIdentifier, String instanceIdentifier) {
        try {
            String key = "/accounts/" + accIdentifier + "/instances/" + instanceIdentifier + "/kpis";
            String hashKey = "ACCOUNTS_" + accIdentifier + "_INSTANCES_" + instanceIdentifier + "_KPIS";

            Object kpiDetailsObject = redisTemplate.opsForHash().get(key, hashKey);

            if (kpiDetailsObject == null) {
                log.error("KPI information unavailable for instance [{}] and account [{}].", instanceIdentifier, accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/instances/" + instanceIdentifier + "/kpis", 1);
                return Collections.emptyList();
            }
            return objectMapper.readValue(kpiDetailsObject.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting to KPI details for instance [{}], account [{}].", instanceIdentifier, accIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<ComponentKpiEntity> getComponentKPIs(String accountIdentifier, String componentName) {
        try {
            String key = "/accounts/" + accountIdentifier + "/components/" + componentName + "/kpis";
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_COMPONENTS_" + componentName + "_KPIS";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);

            if (obj == null) {
                log.error("Component kpi details unavailable for component [{}] mapped to account [{}]", componentName, accountIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accountIdentifier + "/components/" + componentName + "/kpis", 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting Component kpi details for component [{}] mapped to account [{}].", componentName, accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public Application getApplicationByIdentifier(String accountIdentifier, String applicationIdentifier) {
        try {
            String key = "/accounts/" + accountIdentifier + "/applications/" + applicationIdentifier;
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_APPLICATIONS_" + applicationIdentifier;

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);

            if (obj == null) {
                log.error("Application details unavailable for application identifier [{}] mapped to account [{}]", applicationIdentifier, accountIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accountIdentifier + "/applications/" + applicationIdentifier, 1);
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting application details for identifier [{}] mapped to account [{}].", applicationIdentifier, accountIdentifier, e);
            return null;
        }
    }

    public List<BasicEntity> getServicesMappedToApplication(String accountIdentifier, String applicationIdentifier) {
        try {
            String key = "/accounts/" + accountIdentifier + "/applications/" + applicationIdentifier + "/services";
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_APPLICATIONS_" + applicationIdentifier + "_SERVICES";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);

            if (obj == null) {
                log.error("Application services details unavailable for application [{}] mapped to account [{}]", applicationIdentifier, accountIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accountIdentifier + "/applications/" + applicationIdentifier + "/services", 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting application services details for application [{}] mapped to account [{}].", applicationIdentifier, accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicInstanceBean> getServiceInstances(String accountIdentifier, String serviceIdentifier, boolean includeCluster) {
        try {
            String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/instances";
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_INSTANCES";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);

            if (obj == null) {
                log.error("Service instances detail unavailable for service [{}] mapped to account [{}]", serviceIdentifier, accountIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/instances", 1);
                return Collections.emptyList();
            }

            List<BasicInstanceBean> instances = objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });

            if (!includeCluster) {
                return instances;
            }

            Map<String, CompInstClusterDetails> instancesMap = getInstancesByAccount(accountIdentifier)
                    .stream()
                    .collect(Collectors.toMap(CompInstClusterDetails::getIdentifier, Function.identity()));

            List<BasicInstanceBean> clusterInstances = instances.parallelStream()
                    .filter(c -> c.getClusterIdentifier() != null && !c.getClusterIdentifier().trim().isEmpty())
                    .map(BasicInstanceBean::getClusterIdentifier)
                    .distinct()
                    .map(instancesMap::get)
                    .filter(Objects::nonNull)
                    .map(c -> BasicInstanceBean.builder()
                            .id(c.getId())
                            .name(c.getName())
                            .identifier(c.getIdentifier())
                            .status(c.getStatus())
                            .createdTime(c.getCreatedTime())
                            .updatedTime(c.getUpdatedTime())
                            .lastModifiedBy(c.getLastModifiedBy())
                            .accountId(c.getAccountId())
                            .componentId(c.getComponentId())
                            .componentTypeId(c.getComponentTypeId())
                            .componentVersionId(c.getComponentVersionId())
                            .commonVersionId(c.getCommonVersionId())
                            .build())
                    .collect(Collectors.toList());

            instances.addAll(clusterInstances);

            return instances;
        } catch (Exception e) {
            log.error("Error occurred while getting service instances detail for service [{}] mapped to account [{}].", serviceIdentifier, accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<BasicTransactionEntity> getServiceWiseTransaction(String accountIdentifier, String serviceIdentifier) {
        try {
            String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/transactions";
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_TRANSACTIONS";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);

            if (obj == null) {
                log.error("Service wise transactions unavailable for service [{}] mapped to account [{}]", serviceIdentifier, accountIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/instances", 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting service wise transactions detail for service [{}] mapped to account [{}].", serviceIdentifier, accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public List<CompInstClusterDetails> getInstancesByAccount(String accIdentifier) {
        String key = "/accounts/" + accIdentifier + "/instances";
        String hashKey = "ACCOUNTS_" + accIdentifier + "_INSTANCES";
        try {
            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Account wise instances unavailable for account [{}]", accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/instances", 1);
                return Collections.emptyList();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return Collections.emptyList();
        }
    }

    public List<OSIndexZoneDetails> getHealIndexZones() {
        try {
            String key = "/heal/index/zones";
            String hashKey = "HEAL_INDEX_ZONES";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Heal opensearch index to zone mapping unavailable");
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/heal/index/zones", 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting Heal opensearch index to zone mapping.", e);
            return Collections.emptyList();
        }
    }

    public ApplicationSettings getApplicationSettings(String accountIdentifier, String applicationIdentifier) {
        try {
            String key = "/accounts/" + accountIdentifier + "/applications/" + applicationIdentifier + "/settings";
            String hashKey = "ACCOUNTS_" + accountIdentifier + "_APPLICATIONS_" + applicationIdentifier + "_SETTINGS";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);

            if (obj == null) {
                log.error("Application settings unavailable for application [{}] mapped to account [{}]", applicationIdentifier, accountIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accountIdentifier + "/applications/" + applicationIdentifier + "/settings", 1);
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting application settings for application [{}] mapped to account [{}].", applicationIdentifier, accountIdentifier, e);
            return null;
        }
    }
}