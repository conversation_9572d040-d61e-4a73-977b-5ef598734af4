package com.heal.event.detector.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.heal.event.detector.utility.cache.CacheConstants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class CacheConfig {

    @Value("${account.configuration.cache.max.size:20}")
    private int accountCacheMaxsize;
    @Value("${account.configuration.cache.expire.interval.minutes:5}")
    private int accountCacheExpirationTime;
    @Value("${instance.configuration.cache.max.size:5000}")
    private int instanceCacheMaxsize;
    @Value("${instance.configuration.cache.expire.interval.minutes:5}")
    private int instanceCacheExpirationTime;
    @Value("${instance.kpi.configuration.cache.max.size:1000}")
    private int instanceKpiCacheMaxsize;
    @Value("${instance.kpi.configuration.cache.expire.interval.minutes:5}")
    private int instanceKpiCacheExpirationTime;
    @Value("${component.kpi.configuration.cache.max.size:1000}")
    private int componentKpiCacheMaxsize;
    @Value("${component.kpi.configuration.cache.expire.interval.minutes:5}")
    private int componentKpiCacheExpirationTime;
    @Value("${heal.types.configuration.cache.max.size:500}")
    private int healTypesCacheMaxsize;
    @Value("${heal.types.configuration.cache.expire.interval.minutes:60}")
    private int healTypesCacheExpirationTime;
    @Value("${service.configuration.cache.max.size:500}")
    private int serviceCacheMaxsize;
    @Value("${service.configuration.cache.expire.interval.minutes:5}")
    private int serviceCacheExpirationTime;
    @Value("${service.kpi.configuration.cache.max.size:1000}")
    private int serviceKpiCacheMaxsize;
    @Value("${service.kpi.configuration.cache.expire.interval.minutes:5}")
    private int serviceKpiCacheExpirationTime;
    @Value("${transactions.configuration.cache.max.size:5000}")
    private int transactionsCacheMaxsize;
    @Value("${transactions.configuration.cache.expire.interval.minutes:5}")
    private int transactionsCacheExpirationTime;
    @Value("${transactions.violationConfig.configuration.cache.max.size:5000}")
    private int transactionsViolationCacheMaxsize;
    @Value("${transactions.violationConfig.configuration.cache.expire.interval.minutes:5}")
    private int transactionsViolationCacheExpirationTime;
    @Value("${instance.kpis.configuration.cache.max.size:5000}")
    private int instanceKPIsCacheMaxsize;
    @Value("${instance.kpis.configuration.cache.expire.interval.minutes:5}")
    private int instanceKPIsCacheExpirationTime;

    @Value("${component.kpis.configuration.cache.max.size:5000}")
    private int componentKPIsCacheMaxsize;
    @Value("${component.kpis.configuration.cache.expire.interval.minutes:5}")
    private int componentKPIsCacheExpirationTime;

    @Value("${applications.configuration.cache.max.size:5000}")
    private int applicationsCacheMaxsize;
    @Value("${applications.configuration.cache.expire.interval.minutes:5}")
    private int applicationsCacheExpirationTime;

    @Value("${application.services.configuration.cache.max.size:5000}")
    private int applicationServicesCacheMaxsize;
    @Value("${application.services.configuration.cache.expire.interval.minutes:5}")
    private int applicationServicesCacheExpirationTime;

    @Value("${service.instances.configuration.cache.max.size:5000}")
    private int serviceInstancesCacheMaxsize;
    @Value("${service.instances.configuration.cache.expire.interval.minutes:5}")
    private int serviceInstancesCacheExpirationTime;

    @Value("${service.transactions.configuration.cache.max.size:5000}")
    private int serviceTransactionsCacheMaxsize;
    @Value("${service.transactions.configuration.cache.expire.interval.minutes:5}")
    private int serviceTransactionsCacheExpirationTime;

    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.registerCustomCache(CacheConstants.ACCOUNT, createCacheConfig(accountCacheMaxsize, accountCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.INSTANCE, createCacheConfig(instanceCacheMaxsize, instanceCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.INSTANCE_KPI, createCacheConfig(instanceKpiCacheMaxsize, instanceKpiCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.COMPONENT_KPI, createCacheConfig(componentKpiCacheMaxsize, componentKpiCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.HEAL_TYPES, createCacheConfig(healTypesCacheMaxsize, healTypesCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.SERVICE, createCacheConfig(serviceCacheMaxsize, serviceCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.SERVICE_KPI, createCacheConfig(serviceKpiCacheMaxsize, serviceKpiCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.TRANSACTIONS, createCacheConfig(transactionsCacheMaxsize, transactionsCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.TRANSACTIONS_VIOLATION, createCacheConfig(transactionsViolationCacheMaxsize, transactionsViolationCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.INSTANCE_KPIS, createCacheConfig(instanceKPIsCacheMaxsize, instanceKPIsCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.COMPONENT_KPIS, createCacheConfig(componentKPIsCacheMaxsize, componentKPIsCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.APPLICATIONS, createCacheConfig(applicationsCacheMaxsize, applicationsCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.APPLICATION_SERVICES, createCacheConfig(applicationServicesCacheMaxsize, applicationServicesCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.SERVICE_INSTANCES, createCacheConfig(serviceInstancesCacheMaxsize, serviceInstancesCacheExpirationTime).build());
        cacheManager.registerCustomCache(CacheConstants.SERVICE_TRANSACTIONS, createCacheConfig(serviceTransactionsCacheMaxsize, serviceTransactionsCacheExpirationTime).build());
        return cacheManager;
    }

    private Caffeine<Object, Object> createCacheConfig(int maxSize, int expireTime) {
        return Caffeine.newBuilder()
                .initialCapacity(maxSize)
                .maximumSize(maxSize)
                .expireAfterWrite(expireTime, TimeUnit.MINUTES);
    }
}
