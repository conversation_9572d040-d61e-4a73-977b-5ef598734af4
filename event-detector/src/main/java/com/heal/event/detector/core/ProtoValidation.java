package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AggregatedKpiProtos;
import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.appnomic.appsone.common.protbuf.ThresholdProtos;
import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.heal.configuration.protbuf.AnomalySummaryProtos;
import com.heal.configuration.protbuf.ThresholdsClosedProtos;
import com.heal.event.detector.pojos.GenericValidationObject;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ProtoValidation {
    @Autowired
    ViolatedEventsProcess violatedEventsProcess;
    @Autowired
    AnomalyEventsProcess anomalyEventsProcess;
    @Autowired
    ThresholdDataProcess thresholdDataProcess;
    @Autowired
    ClosedNorThresholdDataProcess closedNorThresholdDataProcess;
    @Autowired
    AnomalyCloseDataProcess anomalyCloseDataProcess;
    @Autowired
    GetViolatedData getViolatedData;
    @Autowired
    HealthMetrics metrics;
    @Autowired
    CacheWrapper cacheWrapper;

    public void validateAndProcessInputAggregatedKPI(AggregatedKpiProtos.AggregatedKpi aggregatedKpi) {

        long aggKpiProcessStartTime = System.currentTimeMillis();
        try {
            GenericValidationObject<AggregatedKpiProtos.AggregatedKpi> validateObject = violatedEventsProcess.validateAggregatedKpiData(aggregatedKpi);
            if (!validateObject.isValid()) {
                return;
            }
            aggregatedKpi = validateObject.getProto();

            List<ViolatedData> violatedDataList = violatedEventsProcess.processAggregatedKpiData(aggregatedKpi);
            if (violatedDataList.isEmpty()) {
                return;
            }

            log.info("Processed AggregatedKpi proto into ViolatedData successfully. Sending it for further processing. " +
                    "Violations size : {} for instanceId:{}, kpiId:{} and accountId:{}", violatedDataList.size(), aggregatedKpi.getInstanceId(), aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getAccountId());
            metrics.updateViolationCount(violatedDataList.size());

            List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedDataList, true);
            if (anomalyEventList.isEmpty()) {
                return;
            }
            log.info("Processed Violated data into Anomaly Event proto successfully. " +
                    "Sending it to queue for further processing. Anomalies size:{}", anomalyEventList.size());

        } catch (Exception e) {
            log.error("Exception while processing violated kpi data into anomaly kpi data for" +
                    " kpi details {}.", aggregatedKpi, e);
            metrics.updateAggregatedKpiProcessingErrors();
        } finally {
            log.info("Time taken for process aggregated kpi data for anomaly processing is {} ms, instanceId:{}, kpiId:{}, accountId:{}.", (System.currentTimeMillis() - aggKpiProcessStartTime),
                    aggregatedKpi.getInstanceId(), aggregatedKpi.getKpiData().getKpiUid(), aggregatedKpi.getAccountId());
            metrics.updateSlowAggregatedKpiEvents(System.currentTimeMillis() - aggKpiProcessStartTime);
        }
    }

    public void validateAndProcessInputViolatedEvents(ViolatedEventProtos.ViolatedEvent violatedEvent) {

        long violatedEventProcessStartTime = System.currentTimeMillis();
        try {
            GenericValidationObject<ViolatedEventProtos.ViolatedEvent> validateObject = anomalyEventsProcess.validateAnomalyEvent(violatedEvent);
            if (!validateObject.isValid()) {
                return;
            }
            violatedEvent = validateObject.getProto();

            List<ViolatedData> violatedDataList = getViolatedData.getViolatedDataObject(violatedEvent);
            if (violatedDataList.isEmpty()) {
                log.warn("No valid violated data found to process further for account [{}], kpi list {}, txn list {}", violatedEvent.getAccountId(), violatedEvent.getKpisList(), violatedEvent.getTransactionsList());
                return;
            }

            List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedDataList, false);
            if (anomalyEventList.isEmpty()) {
                return;
            }

            log.info("Processed Violated Event proto into Anomaly Event proto successfully.Sending it to queue for further processing.");

        } catch (Exception e) {
            log.error("Exception occurred while processing the violation event, Dropping data point {}", violatedEvent, e);
            metrics.updateViolatedEventProcessingErrors();
        } finally {
            metrics.updateSlowViolatedEvents(System.currentTimeMillis() - violatedEventProcessStartTime);
        }
    }

    public void validateAndProcessInputNorThresholdEvents(ThresholdProtos.Threshold threshold) {

        long norThresholdProcessStartTime = System.currentTimeMillis();
        try {
            GenericValidationObject<ThresholdProtos.Threshold> validateObject = thresholdDataProcess.validateNorThreshold(threshold);
            if (!validateObject.isValid()) {
                return;
            }
            threshold = validateObject.getProto();

            log.debug("Threshold proto verified successfully. Sending it to queue for further processing.");
            thresholdDataProcess.processAndSinkThresholdData(threshold);
        } catch (Exception e) {
            log.error("Exception occurred while processing the NOR threshold event, Dropping data point {}", threshold, e);
            metrics.updateNorThresholdProcessingErrors();
        } finally {
            metrics.updateSlowNorThresholdEvents(System.currentTimeMillis() - norThresholdProcessStartTime);
        }
    }

    public void validateAndProcessInputNorClosedThresholdEvents(ThresholdsClosedProtos.NORThresholdsClosed norThresholdsClosed) {
        long norClosedThresholdProcessStartTime = System.currentTimeMillis();
        try {
            GenericValidationObject<ThresholdsClosedProtos.NORThresholdsClosed> validateObject = closedNorThresholdDataProcess.validateNorClosedThreshold(norThresholdsClosed);
            if (!validateObject.isValid()) {
                return;
            }
            norThresholdsClosed = validateObject.getProto();

            log.debug("Nor Closed Threshold proto verified successfully. Sending it to queue for further processing.");
            closedNorThresholdDataProcess.processClosedThresholdData(norThresholdsClosed);
        } catch (Exception e) {
            log.error("Exception occurred while processing the NOR closed threshold event, Dropping data point {}", norThresholdsClosed, e);
            metrics.updateNorThresholdProcessingErrors();
        } finally {
            metrics.updateSlowNorClosedThresholdEvents(System.currentTimeMillis() - norClosedThresholdProcessStartTime);
        }
    }

    public void validateAndProcessInputAnomalyCloseEvents(AnomalySummaryProtos.AnomalySummary anomalySummary) {
        long anomalyCloseProcessStartTime = System.currentTimeMillis();
        try {
            GenericValidationObject<AnomalySummaryProtos.AnomalySummary> validateObject = anomalyCloseDataProcess.validateAnomalyCloseData(anomalySummary);
            if (!validateObject.isValid()) {
                return;
            }
            anomalySummary = validateObject.getProto();

            log.debug("Anomaly close data validated successfully. Processing anomaly closure for anomalyId: {}, account: {}",
                    anomalySummary.getAnomalyId(), anomalySummary.getAccountIdentifier());

            // Process the anomaly closure using the dedicated process class
            anomalyCloseDataProcess.processAndSinkAnomalyCloseData(anomalySummary);
        } catch (Exception e) {
            log.error("Exception occurred while processing the anomaly close event, Dropping data point {}", anomalySummary, e);
            metrics.updateAnomalyCloseProcessingErrors();
        } finally {
            metrics.updateSlowAnomalyCloseEvents(System.currentTimeMillis() - anomalyCloseProcessStartTime);
        }
    }
}