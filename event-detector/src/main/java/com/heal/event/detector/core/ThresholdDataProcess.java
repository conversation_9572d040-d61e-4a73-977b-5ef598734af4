package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.ThresholdProtos;
import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.heal.event.detector.exception.EventDetectorException;
import com.heal.event.detector.pojos.GenericValidationObject;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.StringUtils;
import com.heal.event.detector.utility.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ThresholdDataProcess {

    @Autowired
    OpenSearchRepo openSearchRepo;

    @Autowired
    HealthMetrics metrics;

    @Autowired
    ProcessInstanceKpiThresholdsData processInstanceKpiThresholdsData;

    @Autowired
    ProcessTransactionKpiThresholdsData processTransactionKpiThreshold;

    public void processAndSinkThresholdData(ThresholdProtos.Threshold threshold) {
        String timeInGMT = threshold.getTimeInGMT();
        long violationEpochTime;

        try {
            violationEpochTime = Utils.dateStrToLocalDateTime(timeInGMT).toInstant(ZoneOffset.UTC).toEpochMilli();
        } catch (EventDetectorException e) {
            log.error("""
                    Received incorrect data format, required date format[{}].\
                     Hence ignoring below NOR Threshold. {}
                    
                    """, Constants.DATE_FORMAT, threshold);
            return;
        }

        log.info("Processing instance kpi threshold list of size {} from threshold proto data.", threshold.getKpiThresholdList().size());
        metrics.updateInstanceKpiThresholdsReceived(threshold.getKpiThresholdList().size());
        threshold.getKpiThresholdList().forEach(kpiThreshold -> processInstanceKpiThresholdsData.processInstanceKpiThreshold(threshold.getAccountId(), threshold.getApplicationId(),
                threshold.getThresholdType(), kpiThreshold, violationEpochTime));

        log.info("Processing transaction kpi threshold list of size {} from threshold proto data.", threshold.getTransactionThresholdList().size());
        metrics.updateTransactionKpiThresholdsReceived(threshold.getTransactionThresholdList().size());
        threshold.getTransactionThresholdList().forEach(txnThreshold -> processTransactionKpiThreshold.processTransactionKpiThreshold(threshold.getAccountId(),
                threshold.getApplicationId(), threshold.getThresholdType(), txnThreshold, violationEpochTime));
    }

    public GenericValidationObject<ThresholdProtos.Threshold> validateNorThreshold(ThresholdProtos.Threshold threshold) {
        GenericValidationObject<ThresholdProtos.Threshold> result = GenericValidationObject.<ThresholdProtos.Threshold>builder()
                .isValid(false)
                .proto(threshold)
                .build();

        if (threshold == null) {
            log.error("Validation of threshold data failed. Reason: Data received is null." +
                    "Hence it will not be processed further.");
            metrics.updateNorThresholdProcessingErrors();
            return result;
        }

        if (threshold.getSerializedSize() == 0) {
            log.error("Validation of threshold data failed. Reason: Data received is either invalid or undefined." +
                    "Hence it will not be processed further.");
            metrics.updateNorThresholdProcessingErrors();
            return result;
        }

        if (StringUtils.isEmpty(threshold.getAccountId())) {
            log.error("Validation of threshold data failed. Reason: Account identifier is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateNorThresholdProcessingErrors();
            return result;
        }

        if (StringUtils.isEmpty(threshold.getThresholdType())) {
            log.error("Validation of threshold data failed. Reason: Threshold type is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateNorThresholdProcessingErrors();
            return result;
        }

        if (StringUtils.isEmpty(threshold.getApplicationId())) {
            log.error("Validation of threshold data failed. Reason: Application identifier is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateNorThresholdProcessingErrors();
            return result;
        }

        if (threshold.getKpiThresholdList().isEmpty() && threshold.getTransactionThresholdList().isEmpty()) {
            log.error("Validation of threshold data failed. Reason: Kpi Threshold list and Transaction " +
                    "threshold list are empty. Hence it will not be be processed further.");
            metrics.updateNorThresholdProcessingErrors();
            return result;
        }

        List<ViolatedEventProtos.KpiInfo> kpiList = new ArrayList<>();
        for (ViolatedEventProtos.KpiInfo kpi : threshold.getKpiThresholdList()) {

            if (StringUtils.isEmpty(kpi.getKpiId())) {
                log.error("Validation of threshold data failed. Reason: Kpi id in kpi info is " +
                        "either invalid or undefined. Hence it will not be be processed further.");
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }

            String kpiId = kpi.getKpiId();

            if (StringUtils.isEmpty(kpi.getInstanceId())) {
                log.error("Validation of threshold data failed. Reason: Instance id for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(kpi.getKpiAttribute())) {
                log.error("Validation of threshold data failed. Reason: Kpi Attribute for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(kpi.getOperationType())) {
                log.error("Validation of threshold data failed. Reason: Operation type in kpi info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }

            if (kpi.getSvcIdList().isEmpty()) {
                log.error("Validation of threshold data data failed. Reason: Service identifier list for kpi [{}] is " +
                        "empty. Hence it will not be be processed further.", kpiId);
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }

            if (kpi.getThresholdsMap().isEmpty()) {
                log.error("Validation of threshold data failed. Reason: Threshold map in kpi info for kpi [{}] is " +
                        "empty. Hence it will not be be processed further.", kpiId);
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }

            List<String> nonEmptyServiceIdList = kpi.getSvcIdList().stream()
                    .filter(c -> !StringUtils.isEmpty(c)).collect(Collectors.toList());
            if (nonEmptyServiceIdList.isEmpty()) {
                log.error("Validation of threshold data failed. Reason: Service identifier for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }

            if (nonEmptyServiceIdList.size() <= kpi.getSvcIdList().size()) {
                kpiList.add(kpi.toBuilder().clearSvcId().addAllSvcId(nonEmptyServiceIdList).build());
            }

        }
        threshold = threshold.toBuilder().clearKpiThreshold().addAllKpiThreshold(kpiList).build();
        result.setProto(threshold);

        for (ViolatedEventProtos.TransactionInfo txn : threshold.getTransactionThresholdList()) {

            if (StringUtils.isEmpty(txn.getKpiId())) {
                log.error("Validation of threshold data failed. Reason: Kpi id in transaction info is " +
                        "either invalid or undefined. Hence it will not be be processed further.");
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }

            String kpiId = txn.getKpiId();

            if (StringUtils.isEmpty(txn.getTransactionId())) {
                log.error("Validation of threshold data failed. Reason: Transaction id for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getGroupId())) {
                log.error("Validation of threshold data failed. Reason: Group id for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getOperationType())) {
                log.error("Validation of threshold data failed. Reason: Operation type in transaction info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getSvcId())) {
                log.error("Validation of threshold data failed. Reason: Service identifier in transaction info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }

            if (txn.getThresholdsMap().isEmpty()) {
                log.error("Validation of threshold data failed. Reason: Threshold map in transaction info for kpi [{}] is " +
                        "empty. Hence it will not be be processed further.", kpiId);
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getResponseTimeType().name())) {
                log.error("Validation of threshold data failed. Reason: Response time type in transaction info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateNorThresholdProcessingErrors();
                return result;
            }
        }
        result.setValid(true);
        return result;
    }
}
