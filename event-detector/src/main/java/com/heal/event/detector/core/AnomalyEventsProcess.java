package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.heal.configuration.pojos.ApplicationSettings;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.ComponentKpiEntity;
import com.heal.configuration.pojos.ViewTypes;
import com.heal.configuration.pojos.opensearch.Alerts;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.pojos.GenericValidationObject;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.service.EventForwarderToQueue;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.StringUtils;
import com.heal.event.detector.utility.Utils;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class AnomalyEventsProcess {

    @Autowired
    OpenSearchRepo openSearchRepo;

    @Autowired
    PrepareAnomalyData prepareAnomalyData;

    @Autowired
    PersistenceSuppression persistenceSuppression;

    @Autowired
    PerSuppProcessWatcherKPI perSuppProcessWatcherKPI;

    @Autowired
    PerSuppBatchJob perSuppBatchJob;

    @Autowired
    HealthMetrics metrics;

    @Autowired
    EventForwarderToQueue forwarder;

    @Value("${forward.raw.anomalies:false}")
    boolean pushRawAnomalies;

    @Value("${kpi.data.outoforder.mins:10}")
    int outOfOrderValue;

    @Value("${entity.type.instance:INSTANCE}")
    String entityTypeInstance;

    @Value("${entity.type.transaction:TRANSACTION}")
    String entityTypeTransaction;

    @Autowired
    CacheWrapper cacheWrapper;

    @Value(("${heal.global.account.identifier:e573f852-5057-11e9-8fd2-b37b61e52317}"))
    private String globalAccountIdentifier;
    @Value("${heal.transaction.component.identifier:Transaction}")
    private String transactionComponentIdentifier;
    @Value("${heal.batch.component.identifier:BatchProcess}")
    private String batchComponentIdentifier;

    /**
     * Processes the violated KPI data and returns a list of AnomalyEventProtos.AnomalyEvent.
     * It performs persistence and suppression on the violated data, processes watcher KPIs,
     * processes batch job KPIs, and inserts anomalies and alerts into OpenSearch and RMQ.
     *
     * @param violatedDataList List of ViolatedData containing violation details.
     * @param isAggregatedKpi  Flag indicating if the KPI is aggregated.
     * @return List of AnomalyEventProtos.AnomalyEvent containing processed anomaly events.
     */
    public List<AnomalyEventProtos.AnomalyEvent> processViolatedKpiData(List<ViolatedData> violatedDataList, boolean isAggregatedKpi) {
        sinkRawViolationToOS(violatedDataList);

        List<AnomalyAccountPojo> anomalyKpisList = persistenceAndSuppressionOnViolatedData(violatedDataList);
        List<AnomalyAccountPojo> anomalyWatcherKpisList = processWatcherKpis(violatedDataList);
        List<AnomalyAccountPojo> anomalyBatchJobKpisList = processBatchJobKpis(violatedDataList);

        List<AnomalyAccountPojo> allAnomalyAccountPojos = Stream.of(anomalyKpisList, anomalyWatcherKpisList, anomalyBatchJobKpisList)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());

        return insertAnomaliesAndAlertsOpenSearchRMQ(allAnomalyAccountPojos, isAggregatedKpi);
    }

    /**
     * Inserts anomalies and alerts into OpenSearch and sends them to RMQ.
     * @param anomalyAccountPojosList   List of AnomalyAccountPojo containing anomaly details.
     * @param isAggregatedKpi           Flag indicating if the KPI is aggregated.
     * @return
     */
    public List<AnomalyEventProtos.AnomalyEvent> insertAnomaliesAndAlertsOpenSearchRMQ(List<AnomalyAccountPojo> anomalyAccountPojosList, boolean isAggregatedKpi) {
        // Convert AnomalyAccountPojo to AnomalyEventProtos.AnomalyEvent for backward compatibility
        List<AnomalyEventProtos.AnomalyEvent> anomalyKpisProtosList = prepareAnomalyData.getAllAnomalies(anomalyAccountPojosList);

        if (anomalyAccountPojosList.isEmpty()) {
            log.debug("No anomalies to process. Returning empty list.");
            return null;
        }

        openSearchRepo.insertAnomalies(anomalyAccountPojosList);
        anomalyAccountPojosList
            .forEach(anomalyAccountPojo -> {
                Alerts alerts = getAlerts(anomalyAccountPojo.getAnomalyDetails());
                openSearchRepo.insertAlertData(alerts, anomalyAccountPojo.getAccountIdentifier(), anomalyAccountPojo.getAnomalyDetails().getMetadata().get("alertId"));
            });

        // Anomaly metrics are now tracked at individual operation level (create/update/close)
        // in AnomalyManagementService, so no need to track total count here
        forwarder.sendAnomalyOutputToActionQueue(anomalyKpisProtosList);
        List<ViewTypes> mstSubType = cacheWrapper.getMstTypes();
        Map<String, Integer> subTypeMap = mstSubType.parallelStream().filter(c -> c.getTypeName().equalsIgnoreCase(
                "AnomalySignalType")).collect(Collectors.toMap(ViewTypes::getSubTypeName, ViewTypes::getSubTypeId));
        log.debug("Total anomaly event created is {}", anomalyKpisProtosList.size());

        anomalyKpisProtosList.forEach(c -> {
            if(!c.getAppIdList().isEmpty()) {
                ApplicationSettings applicationSettings = cacheWrapper.getApplicationSettings(c.getAccountId(),
                        c.getAppId(0));
                log.debug("processing anomaly for appid {}, accountId {}", c.getAppId(0), c.getAccountId());

                if (Objects.isNull(applicationSettings)) {
                    forwarder.sendAnomalyOutputToSignalQueue(c);
                    if (pushRawAnomalies && isAggregatedKpi) {
                        log.debug("The consul key 'anomaly.itsm.forward' is set to true. Anomaly details will be forwarded to Notification-Processor. Details: Anomaly Id {}, AppId {}, Account Id {}, Trigger Time(in GMT) {}", c.getAnomalyId(), c.getAppId(0), c.getAccountId(), c.getAnomalyTriggerTimeGMT());
                        forwarder.sendAnomalyMessagesQueue(c);
                    }
                } else {
                    int subTypeId = subTypeMap.getOrDefault(applicationSettings.getTypeName(), 413);
                    if (subTypeId == 413 || applicationSettings.getTypeName().equalsIgnoreCase("signal-detector")) {
                        forwarder.sendAnomalyOutputToSignalQueue(c);
                        if (pushRawAnomalies && isAggregatedKpi) {
                            log.info("The consul key 'anomaly.itsm.forward' is set to true. Anomaly details will be forwarded to Notification-Processor. Details: Anomaly Id {}, AppId {}, Account Id {}, Trigger Time(in GMT) {}", c.getAnomalyId(), c.getAppId(0), c.getAccountId(), c.getAnomalyTriggerTimeGMT());
                            forwarder.sendAnomalyMessagesQueue(c);
                        }
                    }

                    if (subTypeId == 413 || applicationSettings.getTypeName().equalsIgnoreCase("event-correlation")) {
                        forwarder.sendAnomalyOutputToMLESignalQueue(c);
                    }
                }
            }
        });
        return anomalyKpisProtosList;
    }

    private Alerts getAlerts(Anomalies anomalies) {
        Alerts alerts = new Alerts();
        alerts.setAnomalyId(anomalies.getAnomalyId());
        alerts.setAlertStatus(anomalies.getMetadata().get("alertStatus"));
        alerts.setAlertTime(anomalies.getAnomalyCreatedTime());
        alerts.setIdentifiedTime(anomalies.getIdentifiedTime());
        alerts.setEntityType(anomalies.getEntityType());
        alerts.setEntityId(anomalies.getEntityId());
        alerts.setSeverityId(String.valueOf(anomalies.getLastSeverityId()));
        alerts.setCategoryId(anomalies.getCategoryId());
        alerts.setKpiAttribute(anomalies.getKpiAttribute());
        alerts.setKpiId(anomalies.getKpiId());
        alerts.setKpiIdentifier(anomalies.getKpiIdentifier());
        alerts.setServiceId(anomalies.getServiceId());

        Map<String, String> metadata = new HashMap<>();
        metadata.put("isMaintenanceExcluded", anomalies.getMetadata().get("isMaintenanceExcluded"));
        metadata.put("violationLevel", anomalies.getMetadata().get("violationLevel"));
        metadata.put("isInformatic", anomalies.getMetadata().get("isInformatic"));
        metadata.put("kpiType", anomalies.getMetadata().get("kpiType"));
        metadata.put("attributeName", anomalies.getMetadata().get("attributeName"));
        metadata.put("alertCount", anomalies.getMetadata().get("alertOccurrenceCount"));
        alerts.setMetadata(metadata);

        alerts.setOperationType(anomalies.getOperationType());
        alerts.setThresholdType(anomalies.getThresholdType());
        alerts.setPersistence(anomalies.getMetadata().get("persistence"));
        alerts.setSuppression(anomalies.getMetadata().get("suppression"));
        alerts.setAlertType(anomalies.getMetadata().get("alertType"));

        Map<String, Double> thresholds = new HashMap<>();
        thresholds.put("thresholdsUpper", Double.valueOf(anomalies.getMetadata().get("thresholdsUpper")));
        thresholds.put("thresholdsLower", Double.valueOf(anomalies.getMetadata().get("thresholdsLower")));
        alerts.setThresholds(thresholds);

        alerts.setAnomalyScore(anomalies.getMetadata().get("anomalyScore"));
        alerts.setValue(anomalies.getMetadata().get("value"));

        return alerts;
    }

    private void sinkRawViolationToOS(List<ViolatedData> violatedDataList) {

        try {
            if (violatedDataList.isEmpty()) {
                return;
            }
            Map<ViolationEventType, List<ViolatedData>> violationEventTypeListMap = violatedDataList.parallelStream()
                    .filter(c -> !Objects.isNull(c.getEventType()))
                    .collect(Collectors.groupingBy(ViolatedData::getEventType));

            if (violationEventTypeListMap.isEmpty()) {
                return;
            }

            openSearchRepo.insertRawKpiViolations(violationEventTypeListMap
                    .getOrDefault(ViolationEventType.KPI_VIOLATION, new ArrayList<>()));

            openSearchRepo.insertRawTxnViolations(violationEventTypeListMap
                    .getOrDefault(ViolationEventType.TXN_VIOLATION, new ArrayList<>()));

            openSearchRepo.insertRawBatchJobViolations(violationEventTypeListMap
                    .getOrDefault(ViolationEventType.BATCH_JOB_VIOLATION, new ArrayList<>()));
        } catch (Exception e) {
            log.error("Exception while pushing data into OS. Details: ", e);
        }
    }

    private List<AnomalyAccountPojo> persistenceAndSuppressionOnViolatedData(List<ViolatedData> violatedDataList) {
        return violatedDataList.parallelStream()
                .filter(c -> c.getEventType() != ViolationEventType.BATCH_JOB_VIOLATION)
                .filter(c -> !c.getAppIds().isEmpty())
                .filter(c -> !((c.getEventType() == ViolationEventType.KPI_VIOLATION)
                        && (c.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.FileWatch
                        || c.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.ConfigWatch)))
                .map(value -> persistenceSuppression.applyPersistenceSuppressionCreateUpdateAnomaly(value))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<AnomalyAccountPojo> processBatchJobKpis(List<ViolatedData> violatedDataList) {
        return violatedDataList.parallelStream()
                .filter(c -> c.getEventType() == ViolationEventType.BATCH_JOB_VIOLATION)
                .filter(c -> !c.getAppIds().isEmpty())
                .map(value -> {
                    AnomalyEventProtos.AnomalyEvent anomalyEvent = perSuppBatchJob.applyPersistenceSupp(value);
                    if (anomalyEvent != null) {
                        // Convert AnomalyEvent to AnomalyAccountPojo
                        return convertAnomalyEventToAccountPojo(anomalyEvent);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<AnomalyAccountPojo> processWatcherKpis(List<ViolatedData> violatedDataList) {
        return violatedDataList.parallelStream()
                .filter(c -> c.getEventType() != ViolationEventType.BATCH_JOB_VIOLATION)
                .filter(c -> c.getKpiType() != null &&
                        (c.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.FileWatch
                                || c.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.ConfigWatch))
                .map(value -> {
                    AnomalyEventProtos.AnomalyEvent anomalyEvent = perSuppProcessWatcherKPI.applyPersistenceSuppressionBasedOnType(value,
                            0, 0, false, value.getServiceList().get(0), 1);
                    if (anomalyEvent != null) {
                        // Convert AnomalyEvent to AnomalyAccountPojo
                        return convertAnomalyEventToAccountPojo(anomalyEvent);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public GenericValidationObject<ViolatedEventProtos.ViolatedEvent> validateAnomalyEvent(ViolatedEventProtos.ViolatedEvent violatedEvent) {
        GenericValidationObject<ViolatedEventProtos.ViolatedEvent> result =
                GenericValidationObject.<ViolatedEventProtos.ViolatedEvent>builder()
                        .isValid(false)
                        .proto(violatedEvent)
                        .build();

        if (violatedEvent == null) {
            log.error("Validation of violated event data failed. Reason: Data received is null." +
                    "Hence it will not be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        if (violatedEvent.getSerializedSize() == 0) {
            log.error("Validation of violated event data failed. Reason: Data received is either invalid or undefined." +
                    "Hence it will not be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        if (Utils.isOutOfOrder(violatedEvent.getViolationTmeInGMT(), outOfOrderValue)) {
            log.error("Validation of violated event data failed. Reason: Data received is OutOfOrder for KPIs:{}, Time:{}, OutOfOrderValue:{}." +
                    "Hence it will not be processed further.", violatedEvent.getKpisList().size(), violatedEvent.getViolationTmeInGMT(), outOfOrderValue);
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        if (StringUtils.isEmpty(violatedEvent.getAccountId())) {
            log.error("Validation of violated event data failed. Reason: Account identifier is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        if (violatedEvent.getAppIdList().isEmpty()) {
            log.error("Validation of violated event data failed. Reason: Application identifier list is " +
                    "empty. Hence it will not be be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        List<String> nonEmptyAppIdList = violatedEvent.getAppIdList().stream()
                .filter(c -> !StringUtils.isEmpty(c)).collect(Collectors.toList());
        if (nonEmptyAppIdList.isEmpty()) {
            log.error("Validation of violated event data failed. Reason: Application identifier is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        if (nonEmptyAppIdList.size() < violatedEvent.getAppIdList().size()) {
            violatedEvent = violatedEvent.toBuilder().clearAppId().addAllAppId(nonEmptyAppIdList).build();
            result.setProto(violatedEvent);
        }

        if (StringUtils.isEmpty(violatedEvent.getThresholdType())) {
            log.error("Validation of violated event data failed. Reason: Threshold type is " +
                    "either invalid or undefined. Hence it will not be be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        if (violatedEvent.getKpisList().isEmpty() && violatedEvent.getBatchJobList().isEmpty() &&
                violatedEvent.getTransactionsList().isEmpty()) {
            log.error("Validation of violated event data failed. Reason: Kpi list, Transaction list and" +
                    " BatchJob list are empty. Hence it will not be be processed further.");
            metrics.updateViolatedEventProcessingErrors();
            return result;
        }

        List<ViolatedEventProtos.Kpi> kpiList = new ArrayList<>();
        for (ViolatedEventProtos.Kpi kpi : violatedEvent.getKpisList()) {

            if (StringUtils.isEmpty(kpi.getKpiInfo().getKpiId())) {
                log.error("Validation of violated event data failed. Reason: Kpi id in kpi info is " +
                        "either invalid or undefined. Hence it will not be be processed further.");
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            String kpiId = kpi.getKpiInfo().getKpiId();

            if (StringUtils.isEmpty(kpi.getKpiInfo().getInstanceId())) {
                log.error("Validation of violated event data failed. Reason: Instance id for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(kpi.getKpiInfo().getKpiAttribute())) {
                log.error("Validation of violated event data failed. Reason: Kpi Attribute for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(kpi.getKpiInfo().getOperationType())) {
                log.error("Validation of violated event data failed. Reason: Operation type in kpi info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (kpi.getKpiInfo().getSvcIdList().isEmpty()) {
                log.error("Validation of violated event data failed. Reason: Service identifier list for kpi [{}] is " +
                        "empty. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (kpi.getKpiInfo().getThresholdsMap().isEmpty()) {
                log.error("Validation of violated event data failed. Reason: Threshold map in kpi info for kpi [{}] is " +
                        "empty. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(kpi.getValue())) {
                log.error("Validation of violated event data failed. Reason: Kpi value in kpi info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            List<String> nonEmptyServiceIdList = kpi.getKpiInfo().getSvcIdList().stream()
                    .filter(c -> !StringUtils.isEmpty(c)).collect(Collectors.toList());
            if (nonEmptyServiceIdList.isEmpty()) {
                log.error("Validation of violated event data failed. Reason: Service identifier for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (nonEmptyServiceIdList.size() <= kpi.getKpiInfo().getSvcIdList().size()) {
                kpiList.add(kpi.toBuilder().clearKpiInfo().setKpiInfo(kpi.getKpiInfo().toBuilder().clearSvcId().addAllSvcId(nonEmptyServiceIdList).build()).build());
            }

        }
        violatedEvent = violatedEvent.toBuilder().clearKpis().addAllKpis(kpiList).build();
        result.setProto(violatedEvent);

        for (ViolatedEventProtos.Transaction txn : violatedEvent.getTransactionsList()) {

            if (StringUtils.isEmpty(txn.getTxnInfo().getKpiId())) {
                log.error("Validation of violated event data failed. Reason: Kpi id in transaction info is " +
                        "either invalid or undefined. Hence it will not be be processed further.");
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            String kpiId = txn.getTxnInfo().getKpiId();

            if (StringUtils.isEmpty(txn.getTxnInfo().getTransactionId())) {
                log.error("Validation of violated event data failed. Reason: Transaction id for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getTxnInfo().getGroupId())) {
                log.error("Validation of violated event data failed. Reason: Group id for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getTxnInfo().getOperationType())) {
                log.error("Validation of violated event data failed. Reason: Operation type in transaction info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getTxnInfo().getSvcId())) {
                log.error("Validation of violated event data failed. Reason: Service identifier in transaction info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (txn.getTxnInfo().getThresholdsMap().isEmpty()) {
                log.error("Validation of violated event data failed. Reason: Threshold map in transaction info for kpi [{}] is " +
                        "empty. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getValue())) {
                log.error("Validation of violated event data failed. Reason: Kpi value in transaction info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(txn.getTxnInfo().getResponseTimeType().name())) {
                log.error("Validation of violated event data failed. Reason: Response time type in transaction info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

        }

        for (ViolatedEventProtos.BatchJob batchJob : violatedEvent.getBatchJobList()) {

            if (StringUtils.isEmpty(batchJob.getKpiId())) {
                log.error("Validation of violated event data failed. Reason: Kpi id in batchjob info is " +
                        "either invalid or undefined. Hence it will not be be processed further.");
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            String kpiId = batchJob.getKpiId();

            if (StringUtils.isEmpty(batchJob.getBatchJob())) {
                log.error("Validation of violated event data failed. Reason: Batchjob id for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(batchJob.getOperationType())) {
                log.error("Validation of violated event data failed. Reason: Operation type in batchjob info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (batchJob.getThresholdsMap().isEmpty()) {
                log.error("Validation of violated event data failed. Reason: Threshold map in batchjob info for kpi [{}] is " +
                        "empty. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }

            if (StringUtils.isEmpty(batchJob.getValue())) {
                log.error("Validation of violated event data failed. Reason: Kpi value in batchjob info for kpi [{}] is " +
                        "either invalid or undefined. Hence it will not be be processed further.", kpiId);
                metrics.updateViolatedEventProcessingErrors();
                return result;
            }
        }

        log.info("Violated Event validated successfully.");
        result.setValid(true);
        return result;
    }

    private AnomalyAccountPojo convertAnomalyEventToAccountPojo(AnomalyEventProtos.AnomalyEvent anomalyEvent) {
        if (anomalyEvent == null) {
            return null;
        }
        
        // Initialize with default values for when KPI info is not available
        String entityId = "";
        String entityType = entityTypeInstance; // default to instance
        long kpiId = 0;
        String kpiAttribute = "";
        Set<String> serviceIds = new HashSet<>();
        Map<String, String> metadata = new HashMap<>();
        String categoryId = "";
        String kpiIdentifier = "";
        
        // Only populate KPI info fields if available
        if (anomalyEvent.hasKpis()) {
            AnomalyEventProtos.KpiInfo kpiInfo = anomalyEvent.getKpis();
            
            // Set entityId if available
            if (kpiInfo.getInstanceId() != null && !kpiInfo.getInstanceId().isEmpty()) {
                entityId = kpiInfo.getInstanceId();
            }
            
            // Set entityType based on workload flag
            entityType = kpiInfo.getIsWorkload() ? entityTypeTransaction : entityTypeInstance;
            
            // Set kpiId if available and valid
            if (kpiInfo.getKpiId() != null && !kpiInfo.getKpiId().isEmpty()) {
                try {
                    kpiId = Long.parseLong(kpiInfo.getKpiId());
                } catch (NumberFormatException e) {
                    log.warn("AnomalyEvent {} has invalid kpiId format: {}, using default value 0", 
                            anomalyEvent.getAnomalyId(), kpiInfo.getKpiId());
                }
            }
            
            // Set kpiAttribute if available
            if (kpiInfo.getKpiAttribute() != null) {
                kpiAttribute = kpiInfo.getKpiAttribute();
            }

            // Set serviceIds if available
            if (kpiInfo.getSvcIdList() != null && !kpiInfo.getSvcIdList().isEmpty()) {
                serviceIds = new HashSet<>(kpiInfo.getSvcIdList());
            }
            
            // Set metadata if available
            if (kpiInfo.getMetadataMap() != null) {
                metadata = new HashMap<>(kpiInfo.getMetadataMap());
            }
        } else {
            log.debug("AnomalyEvent {} does not have KPI info, using default values", anomalyEvent.getAnomalyId());
        }

        AnomalyEventProtos.BatchInfo batchInfo = anomalyEvent.getBatchInfo();

        // Get KPI details for additional information
        CompInstKpiEntity compInstKpiEntity;
        ComponentKpiEntity componentKpiEntity;
        AnomalyEventProtos.KpiInfo kpiInfo = anomalyEvent.getKpis();
        if (kpiInfo.getIsWorkload()) {
            ComponentKpiEntity basicKpiEntity = cacheWrapper.getComponentKPIDetails(globalAccountIdentifier, transactionComponentIdentifier, String.valueOf(kpiId));
            compInstKpiEntity = CompInstKpiEntity.builder()
                    .categoryDetails(basicKpiEntity.getCategoryDetails())
                    .identifier(basicKpiEntity.getIdentifier())
                    .type(basicKpiEntity.getType())
                    .build();
        } else {
            compInstKpiEntity = cacheWrapper.getInstanceKPIDetails(anomalyEvent.getAccountId(), kpiInfo.getInstanceId(), Math.toIntExact(kpiId));
        }
        if (compInstKpiEntity != null) {
            categoryId = compInstKpiEntity.getCategoryDetails().getIdentifier();
            kpiIdentifier = compInstKpiEntity.getIdentifier();
        }

        if (anomalyEvent.getBatchInfo().getSerializedSize() > 0) {
            componentKpiEntity = cacheWrapper.getComponentKPIDetails(globalAccountIdentifier, batchComponentIdentifier, String.valueOf(kpiId));
            categoryId = componentKpiEntity.getCategoryDetails().getIdentifier();
            kpiIdentifier = componentKpiEntity.getIdentifier();
        }

        Anomalies anomalies = Anomalies.builder()
                .anomalyId(anomalyEvent.getAnomalyId())
                .categoryId(categoryId)
                .kpiIdentifier(kpiIdentifier)
                .anomalyStartTime(anomalyEvent.getStartTimeGMT())
                .anomalyEndTime(anomalyEvent.getEndTimeGMT())
                .anomalyCreatedTime(anomalyEvent.getAnomalyTriggerTimeGMT())
                .identifiedTime(anomalyEvent.getAnomalyTriggerTimeGMT())
                .entityId(entityId)
                .entityType(entityType)
                .kpiId(kpiId)
                .kpiAttribute(kpiAttribute)
                .serviceId(serviceIds)
                .thresholdType(anomalyEvent.getThresholdType())
                .operationType(anomalyEvent.getOperationType())
                .metadata(metadata)
                .anomalyStatus(Constants.ANOMALY_STATUS_OPEN)
                .build();
        
        return AnomalyAccountPojo.builder()
                .accountIdentifier(anomalyEvent.getAccountId())
                .anomalyDetails(anomalies)
                .build();
    }
}
