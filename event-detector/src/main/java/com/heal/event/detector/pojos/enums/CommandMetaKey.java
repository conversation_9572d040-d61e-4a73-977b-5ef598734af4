package com.heal.event.detector.pojos.enums;

public enum CommandMetaKey {
    ACCOUNT_ID("AccountId"),
    INSTANCE_ID("InstanceId"),
    CATEGORY_ID("CategoryId"),
    IS_WORKLOAD("IsWorkloadKpi"),
    <PERSON><PERSON>_ID("KPIId"),
    KPI_ATTRIBUTE("KPIAttribute"),
    ACTION_ID("ActionId"),
    ACTION_TRIGGER_TIME("ActionTriggerTime"),
    PRODUCER_TYPE("ProducerType"),
    SUPERVISOR_MODE("SupervisorMode"),
    SIGNAL_STATUS("Signal_Status"),
    ANOMALY_EVENT_ID("Anomaly_Event_ID"),
    FORENSIC_NAME("Forensic_Name"),
    KPI_IDENTIFIER("KPIIdentifier"),
    EVENT_DETECTED_TIME("Event_Detected_Time"),
    SEVERITY("Severity"),
    OPERATION_TYPE("Operation"),
    IMPACTED_APPLICATIONS("App_Names"),
    IMPACTED_SERVICES("Affected_ServiceNames"),
    KPI_NAME("KPIName"),
    CATEGORY_NAME("CategoryName"),
    INSTANCE_NAME("InstanceName"),
    KPI_VALUE("KPIValue"),
    INSTANCE_ROW_ID("InstanceRowId"),
    KPI_VIOLATION_TIME("KPI_VIOLATION_TIME");

    private String key;

    public String getKey() {
        return key;
    }

    CommandMetaKey(String key) {
        this.key = key;
    }
}
