package com.heal.event.detector.utility;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class TestClass {

    // ----------------------------------------------------------------------------------
    // 1. Supporting Enums and Data Classes
    // ----------------------------------------------------------------------------------

    @Getter
    public enum Severity {
        LOW("Low"),
        MEDIUM("Medium"),
        HIGH("High"),
        CRITICAL("Critical");

        private final String displayName;

        Severity(String displayName) {
            this.displayName = displayName;
        }
    }

    public enum Operation {
        GREATER_THAN, LESS_THAN, BETWEEN, OUTSIDE_RANGE
    }

    public enum ViolationLevel {
        SERVICE, INSTANCE
    }

    @Data
    @NoArgsConstructor // Needed for JSON deserialization
    public static class Threshold {
        private String name;
        private Operation operation;
        private Severity severity;
        private double min;
        private double max;
        private ViolationLevel violationLevel;
        private int persistenceThreshold;
        private int suppressionThreshold;

        public Threshold(String name, Operation operation, Severity severity, double min, double max, ViolationLevel level, int persistenceThreshold, int suppressionThreshold) {
            this.name = name;
            this.operation = operation;
            this.severity = severity;
            this.min = min;
            this.max = max;
            this.violationLevel = level;
            this.persistenceThreshold = persistenceThreshold;
            this.suppressionThreshold = suppressionThreshold;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Threshold threshold = (Threshold) o;
            return Double.compare(threshold.min, min) == 0 &&
                    Double.compare(threshold.max, max) == 0 &&
                    persistenceThreshold == threshold.persistenceThreshold &&
                    suppressionThreshold == threshold.suppressionThreshold &&
                    Objects.equals(name, threshold.name) &&
                    operation == threshold.operation &&
                    severity == threshold.severity &&
                    violationLevel == threshold.violationLevel;
        }

        @Override
        public int hashCode() {
            return Objects.hash(name, operation, severity, min, max, violationLevel, persistenceThreshold, suppressionThreshold);
        }
    }

    // ----------------------------------------------------------------------------------
    // 2. Data Models for State (JSON-friendly DTOs)
    // ----------------------------------------------------------------------------------

    @Data
    @NoArgsConstructor // Needed for JSON deserialization
    public static class AnomalySummary {
        public enum AnomalyState { OPEN, ONGOING, CLOSED }
        private String anomalyId;
        private String entityType;
        private String entityId;
        private AnomalyState status;
        private Severity firstSeverity;
        private Severity currentSeverity;
        private long firstAlertTime;
        private long lastAlertTime;
        private int numberOfAlerts;
        private String closureReason;

        public AnomalySummary(String entityType, String entityId) {
            this.anomalyId = UUID.randomUUID().toString();
            this.entityType = entityType;
            this.entityId = entityId;
            this.status = AnomalyState.OPEN;
            this.numberOfAlerts = 0;
        }

        public void recordOpeningAlert(Severity severity, long timestampMillis) {
            this.firstSeverity = severity;
            this.currentSeverity = severity;
            this.firstAlertTime = timestampMillis;
            this.lastAlertTime = timestampMillis;
            this.numberOfAlerts = 1;
        }

        public void recordUpdateAlert(Severity severity, long timestampMillis) {
            this.status = AnomalyState.ONGOING;
            this.currentSeverity = severity;
            this.lastAlertTime = timestampMillis;
            this.numberOfAlerts++;
        }
    }

    @Data
    @NoArgsConstructor // Needed for JSON deserialization
    public static class ViolationThresholdState {
        private Threshold thresholdRule;
        private long violationCount = 0;
        private long firstViolationTime;
        private long lastViolationTime;
        private long noViolationCount = 0;
        private boolean currentlyViolating = false;

        public ViolationThresholdState(Threshold thresholdRule) {
            if (thresholdRule.getPersistenceThreshold() < 1) throw new IllegalArgumentException("Persistence threshold must be at least 1.");
            if (thresholdRule.getSuppressionThreshold() < 0) throw new IllegalArgumentException("Suppression threshold cannot be negative.");
            this.thresholdRule = thresholdRule;
        }

        public void recordViolation(long timestampMillis, boolean isContinuous) {
            if (!isContinuous || !this.currentlyViolating) {
                this.firstViolationTime = timestampMillis;
                this.violationCount = 1;
            } else {
                this.violationCount++;
            }
            this.lastViolationTime = timestampMillis;
            this.currentlyViolating = true;
            this.noViolationCount = 0;
        }

        public void recordNoViolation() {
            if (this.currentlyViolating) {
                this.violationCount = 0;
                this.firstViolationTime = 0;
                this.lastViolationTime = 0;
                this.currentlyViolating = false;
            }
            this.noViolationCount++;
        }
    }

    @Data
    @NoArgsConstructor // Needed for JSON deserialization
    public static class ViolationState {
        private String entityType;
        private String entityId;
        private int collectionIntervalInSeconds;
        private int maxNoViolationCount;
        private int maxAllowedDataBreaks;
        private ViolationLevel currentViolationLevel;
        private AnomalySummary currentAnomaly;
        private Map<String, ViolationThresholdState> detailsByThresholdName = new HashMap<>();
        private long lastViolationSequenceTime;
        private int maxDataBreaks = 0;
        private long dataBreakStartTime;
        private long dataBreakLastTime;
        private long noViolationCount = 0;
        private long noViolationStartTime;
        private long noViolationLastTime;

        public ViolationState(String entityType, String entityId, int collectionIntervalInSeconds,
                              int maxNoViolationCount, int maxAllowedDataBreaks,
                              List<Threshold> initialThresholds) {
            this.entityType = entityType;
            this.entityId = entityId;
            this.collectionIntervalInSeconds = collectionIntervalInSeconds;
            this.maxNoViolationCount = maxNoViolationCount;
            this.maxAllowedDataBreaks = maxAllowedDataBreaks;

            for (Threshold threshold : initialThresholds) {
                detailsByThresholdName.put(threshold.getName(), new ViolationThresholdState(threshold));
            }
        }
    }

    @Data
    @NoArgsConstructor // Needed for JSON deserialization
    public static class Alert {
        private String anomalyId;
        private AnomalySummary.AnomalyState status;
        private ViolationThresholdState triggeringState;
        private String reason;
        private long alertTime;

        public Alert(String anomalyId, AnomalySummary.AnomalyState status, ViolationThresholdState triggeringState, String reason, long alertTime) {
            this.anomalyId = anomalyId;
            this.status = status;
            this.triggeringState = triggeringState;
            this.reason = reason;
            this.alertTime = alertTime;
        }
    }

    @Data
    @NoArgsConstructor // Needed for JSON deserialization
    public static class AnomalyProcessingResult {
        private ViolationState finalState;
        private Alert alertDetails;
        private ViolationState closedState;

        public AnomalyProcessingResult(ViolationState finalState, Alert alertDetails, ViolationState closedState) {
            this.finalState = finalState;
            this.alertDetails = alertDetails;
            this.closedState = closedState;
        }
    }

    // ----------------------------------------------------------------------------------
    // 3. Logic Processors (Stateless Services)
    // ----------------------------------------------------------------------------------
    @Slf4j
    public static class AnomalyProcessor {

        private static final ObjectMapper objectMapper = new ObjectMapper()
                .registerModule(new JavaTimeModule())
                .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        private static <T> T deepCopy(T object, Class<T> clazz) {
            if (object == null) {
                return null;
            }
            try {
                String json = objectMapper.writeValueAsString(object);
                return objectMapper.readValue(json, clazz);
            } catch (JsonProcessingException e) {
                log.error("Failed to deep copy object of type {}: {}", clazz.getSimpleName(), e.getMessage());
                throw new RuntimeException("Failed to deep copy object", e);
            }
        }

        public AnomalyProcessingResult process(ViolationState state, List<Threshold> allThresholds, double kpiValue, LocalDateTime timestamp, boolean isDataPresent) {
            long timestampMillis = timestamp.atZone(ZoneOffset.UTC).toInstant().toEpochMilli();

            // Determine applicable thresholds and new level
            Map<ViolationLevel, List<Threshold>> thresholdsByLevel = allThresholds.stream()
                    .collect(Collectors.groupingBy(Threshold::getViolationLevel));
            List<Threshold> applicableThresholds;
            ViolationLevel newLevel;
            if (thresholdsByLevel.containsKey(ViolationLevel.INSTANCE) && !thresholdsByLevel.get(ViolationLevel.INSTANCE).isEmpty()) {
                applicableThresholds = thresholdsByLevel.get(ViolationLevel.INSTANCE);
                newLevel = ViolationLevel.INSTANCE;
            } else if (thresholdsByLevel.containsKey(ViolationLevel.SERVICE) && !thresholdsByLevel.get(ViolationLevel.SERVICE).isEmpty()) {
                applicableThresholds = thresholdsByLevel.get(ViolationLevel.SERVICE);
                newLevel = ViolationLevel.SERVICE;
            } else {
                applicableThresholds = Collections.emptyList();
                newLevel = null;
            }

            // Check for config or level change, which requires a state reset
            boolean configChanged = isConfigChanged(state, allThresholds);
            boolean levelChanged = !Objects.equals(newLevel, state.getCurrentViolationLevel()) && state.getCurrentViolationLevel() != null;

            if (configChanged || levelChanged) {
                String reason = configChanged ? "Configuration change detected" : "Violation level changed";
                log.warn("{} for entity [{}:{}]. Resetting state.", reason, state.getEntityType(), state.getEntityId());

                // 1. Create a snapshot of the old state to be closed.
                ViolationState stateToClose = deepCopy(state, ViolationState.class);
                Alert closureAlert = null;
                if (isAnomalyOpen(stateToClose)) {
                    closureAlert = closeAnomaly(stateToClose, timestampMillis, reason);
                }

                // 2. Create a brand new state for the new configuration.
                ViolationState newState = new ViolationState(
                        state.getEntityType(), state.getEntityId(), state.getCollectionIntervalInSeconds(),
                        state.getMaxNoViolationCount(), state.getMaxAllowedDataBreaks(), allThresholds
                );
                newState.setCurrentViolationLevel(newLevel);

                // 3. Process the current KPI against this *new* state.
                List<Threshold> newApplicableThresholds = findBreachedThresholds(kpiValue, allThresholds).stream()
                        .filter(t -> t.getViolationLevel() == newLevel).collect(Collectors.toList());
                Optional<Alert> newOpenAlert = processKpiStatusInternal(newState, newApplicableThresholds, timestampMillis, isDataPresent);

                // 4. The final alert is the new open alert if it exists, otherwise it's the closure alert.
                Alert finalAlert = newOpenAlert.orElse(closureAlert);
                return new AnomalyProcessingResult(newState, finalAlert, stateToClose);
            }

            // --- If no config/level change, process normally ---
            ViolationState originalStateCopy = deepCopy(state, ViolationState.class);
            state.setCurrentViolationLevel(newLevel); // Ensure level is set if it was null
            List<Threshold> breachedThresholds = findBreachedThresholds(kpiValue, applicableThresholds);
            Optional<Alert> alert = processKpiStatusInternal(state, breachedThresholds, timestampMillis, isDataPresent);

            ViolationState closedState = null;
            if (state.getCurrentAnomaly() != null && state.getCurrentAnomaly().getStatus() == AnomalySummary.AnomalyState.CLOSED) {
                closedState = originalStateCopy;
            }

            return new AnomalyProcessingResult(state, alert.orElse(null), closedState);
        }

        private boolean isConfigChanged(ViolationState state, List<Threshold> incomingThresholds) {
            Map<String, Threshold> currentThresholdsMap = state.getDetailsByThresholdName().values().stream()
                    .map(ViolationThresholdState::getThresholdRule)
                    .collect(Collectors.toMap(Threshold::getName, Function.identity()));

            Map<String, Threshold> incomingThresholdsMap = incomingThresholds.stream()
                    .collect(Collectors.toMap(Threshold::getName, Function.identity()));

            if (currentThresholdsMap.size() != incomingThresholdsMap.size()) {
                return true;
            }

            for (Map.Entry<String, Threshold> entry : incomingThresholdsMap.entrySet()) {
                Threshold currentThreshold = currentThresholdsMap.get(entry.getKey());
                if (currentThreshold == null || !currentThreshold.equals(entry.getValue())) {
                    return true;
                }
            }
            return false;
        }

        private Optional<Alert> processKpiStatusInternal(ViolationState state, List<Threshold> breachedThresholds, long timestampMillis, boolean isDataPresent) {
            if (!isDataPresent) {
                return Optional.ofNullable(handleMissingData(state, timestampMillis));
            }

            state.setMaxDataBreaks(0);
            if (state.getDataBreakStartTime() != 0) {
                state.setDataBreakStartTime(0);
                state.setDataBreakLastTime(0);
            }

            if (breachedThresholds == null || breachedThresholds.isEmpty()) {
                return Optional.ofNullable(handleNoViolation(state, timestampMillis));
            } else {
                return Optional.ofNullable(handleBreaches(state, breachedThresholds, timestampMillis));
            }
        }

        private Alert handleMissingData(ViolationState state, long timestampMillis) {
            state.setMaxDataBreaks(state.getMaxDataBreaks() + 1);
            log.warn("KPI data missing for entity [{}:{}] at {}. maxDataBreaks incremented to {}.",
                    state.getEntityType(), state.getEntityId(), LocalDateTime.ofEpochSecond(timestampMillis / 1000, (int) (timestampMillis % 1000 * 1_000_000), ZoneOffset.UTC), state.getMaxDataBreaks());

            if (state.getDataBreakStartTime() == 0) {
                state.setDataBreakStartTime(timestampMillis);
            }
            state.setDataBreakLastTime(timestampMillis);

            state.setNoViolationCount(0);
            state.setNoViolationStartTime(0);
            state.setNoViolationLastTime(0);

            if (isAnomalyOpen(state) && state.getMaxDataBreaks() > state.getMaxAllowedDataBreaks()) {
                return closeAnomaly(state, timestampMillis, "Max data breaks exceeded (" + state.getMaxDataBreaks() + ")");
            }
            return null;
        }

        private Alert handleNoViolation(ViolationState state, long timestampMillis) {
            if (state.getNoViolationStartTime() == 0) {
                state.setNoViolationStartTime(timestampMillis);
            }
            state.setNoViolationLastTime(timestampMillis);
            state.setNoViolationCount(state.getNoViolationCount() + 1);

            state.getDetailsByThresholdName().values().forEach(ViolationThresholdState::recordNoViolation);

            if (isAnomalyOpen(state)) {
                boolean allSeveritiesNormal = state.getDetailsByThresholdName().values().stream()
                        .allMatch(s -> s.getNoViolationCount() >= state.getMaxNoViolationCount());

                if (allSeveritiesNormal) {
                    return closeAnomaly(state, timestampMillis, "All severities reached max no-violation count (" + state.getMaxNoViolationCount() + ")");
                }
            }
            return null;
        }

        private Alert handleBreaches(ViolationState state, List<Threshold> breachedThresholds, long timestampMillis) {
            state.setNoViolationCount(0);
            state.setNoViolationStartTime(0);
            state.setNoViolationLastTime(0);

            boolean isContinuous = isViolationContinuous(state, timestampMillis);
            state.setLastViolationSequenceTime(timestampMillis);

            for (Threshold breachedThreshold : breachedThresholds) {
                ViolationThresholdState thresholdState = state.getDetailsByThresholdName().get(breachedThreshold.getName());
                if (thresholdState != null) {
                    thresholdState.recordViolation(timestampMillis, isContinuous);
                }
            }

            state.getDetailsByThresholdName().values().stream()
                    .filter(vts -> breachedThresholds.stream().noneMatch(bt -> bt.getName().equals(vts.getThresholdRule().getName())))
                    .forEach(ViolationThresholdState::recordNoViolation);

            Optional<ViolationThresholdState> highestAlertingState = findHighestPriorityAlertingState(state);

            if (highestAlertingState.isPresent()) {
                ViolationThresholdState triggeringState = highestAlertingState.get();
                if (!isAnomalyOpen(state)) {
                    return openAnomaly(state, triggeringState, timestampMillis);
                } else {
                    return updateAnomaly(state, triggeringState, timestampMillis);
                }
            }
            return null;
        }

        public List<Threshold> findBreachedThresholds(double kpiValue, List<Threshold> thresholds) {
            if (thresholds == null) return Collections.emptyList();
            return thresholds.stream().filter(threshold -> {
                switch (threshold.getOperation()) {
                    case GREATER_THAN: return kpiValue > threshold.getMax();
                    case LESS_THAN: return kpiValue < threshold.getMin();
                    case BETWEEN: return kpiValue >= threshold.getMin() && kpiValue <= threshold.getMax();
                    case OUTSIDE_RANGE: return kpiValue < threshold.getMin() || kpiValue > threshold.getMax();
                    default: return false;
                }
            }).collect(Collectors.toList());
        }

        private Optional<ViolationThresholdState> findHighestPriorityAlertingState(ViolationState state) {
            return state.getDetailsByThresholdName().values().stream()
                    .filter(summary -> shouldTriggerAlert(state, summary))
                    .max(Comparator.comparing(summary -> summary.getThresholdRule().getSeverity()));
        }

        private boolean shouldTriggerAlert(ViolationState state, ViolationThresholdState summary) {
            if (!summary.isCurrentlyViolating()) return false;

            int persistenceThreshold = summary.getThresholdRule().getPersistenceThreshold();
            int suppressionThreshold = summary.getThresholdRule().getSuppressionThreshold();
            Severity currentRuleSeverity = summary.getThresholdRule().getSeverity();

            if (!isAnomalyOpen(state)) {
                return summary.getViolationCount() >= persistenceThreshold;
            }

            if (currentRuleSeverity.ordinal() > state.getCurrentAnomaly().getCurrentSeverity().ordinal()) {
                return summary.getViolationCount() >= persistenceThreshold;
            } else {
                long countSincePersistence = summary.getViolationCount() - persistenceThreshold;
                if (countSincePersistence < 0) return false;
                if (suppressionThreshold == 0) return true;
                return countSincePersistence == 0 || (countSincePersistence % suppressionThreshold == 0);
            }
        }

        private boolean isViolationContinuous(ViolationState state, long currentTimestampMillis) {
            if (state.getLastViolationSequenceTime() == 0) return true;
            if (state.getMaxDataBreaks() > 0 && state.getMaxDataBreaks() <= state.getMaxAllowedDataBreaks()) return true;

            long secondsSinceLast = (currentTimestampMillis - state.getLastViolationSequenceTime()) / 1000;
            return secondsSinceLast <= (state.getCollectionIntervalInSeconds() * 1.1);
        }

        private boolean isAnomalyOpen(ViolationState state) {
            return state.getCurrentAnomaly() != null && state.getCurrentAnomaly().getStatus() != AnomalySummary.AnomalyState.CLOSED;
        }

        private Alert openAnomaly(ViolationState state, ViolationThresholdState triggeringState, long timestampMillis) {
            Severity severity = triggeringState.getThresholdRule().getSeverity();
            String thresholdName = triggeringState.getThresholdRule().getName();

            AnomalySummary anomaly = new AnomalySummary(state.getEntityType(), state.getEntityId());
            anomaly.recordOpeningAlert(severity, timestampMillis);
            state.setCurrentAnomaly(anomaly);
            log.warn("ANOMALY OPENED [ID: {}] for entity [{}:{}] at {} due to persistent [{}] violation (threshold: {}).",
                    anomaly.getAnomalyId(), state.getEntityType(), state.getEntityId(), LocalDateTime.ofEpochSecond(timestampMillis / 1000, (int) (timestampMillis % 1000 * 1_000_000), ZoneOffset.UTC), severity, thresholdName);
            return new Alert(anomaly.getAnomalyId(), AnomalySummary.AnomalyState.OPEN, triggeringState, "Persistence met", timestampMillis);
        }

        private Alert updateAnomaly(ViolationState state, ViolationThresholdState triggeringState, long timestampMillis) {
            Severity severity = triggeringState.getThresholdRule().getSeverity();
            String thresholdName = triggeringState.getThresholdRule().getName();

            state.getCurrentAnomaly().recordUpdateAlert(severity, timestampMillis);
            log.warn("ANOMALY {} [ID: {}] for entity [{}:{}] at {} with severity [{}]. Total alerts: {} (threshold: {}).",
                    state.getCurrentAnomaly().getStatus(), state.getCurrentAnomaly().getAnomalyId(), state.getEntityType(), state.getEntityId(), LocalDateTime.ofEpochSecond(timestampMillis / 1000, (int) (timestampMillis % 1000 * 1_000_000), ZoneOffset.UTC), severity, state.getCurrentAnomaly().getNumberOfAlerts(), thresholdName);
            return new Alert(state.getCurrentAnomaly().getAnomalyId(), state.getCurrentAnomaly().getStatus(), triggeringState, "Suppression met / Escalation", timestampMillis);
        }

        private Alert closeAnomaly(ViolationState state, long timestampMillis, String reason) {
            if (!isAnomalyOpen(state)) {
                return null;
            }
            AnomalySummary closedAnomalySummary = state.getCurrentAnomaly();
            closedAnomalySummary.setStatus(AnomalySummary.AnomalyState.CLOSED);
            closedAnomalySummary.setClosureReason(reason);
            log.warn("ANOMALY CLOSED [ID: {}] for entity [{}:{}] at {} due to: {}.",
                    closedAnomalySummary.getAnomalyId(), state.getEntityType(), state.getEntityId(), LocalDateTime.ofEpochSecond(timestampMillis / 1000, (int) (timestampMillis % 1000 * 1_000_000), ZoneOffset.UTC), reason);
            return new Alert(closedAnomalySummary.getAnomalyId(), AnomalySummary.AnomalyState.CLOSED, null, reason, timestampMillis);
        }
    }
}