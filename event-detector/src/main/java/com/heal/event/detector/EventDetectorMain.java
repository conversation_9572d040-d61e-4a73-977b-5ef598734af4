package com.heal.event.detector;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@PropertySource(value = "classpath:conf.properties")
@ComponentScan
@EnableScheduling
@EnableAsync
@EnableCaching
@Slf4j
public class EventDetectorMain {

    public static void main(String[] args) {
        System.setProperty("logging.config", "classpath:logback.xml");
        log.info("Event Detector is starting........");
        SpringApplication.run(EventDetectorMain.class, args);
        log.info("Event Detector started successfully.......");
    }

}
