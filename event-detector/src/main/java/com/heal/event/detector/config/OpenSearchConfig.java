package com.heal.event.detector.config;

import com.appnomic.appsone.common.util.StringUtils;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.OSIndexZoneDetails;
import com.heal.configuration.pojos.TenantDetails;
import com.heal.configuration.pojos.TenantOpenSearchDetails;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.auth.AuthScope;
import org.apache.hc.client5.http.auth.UsernamePasswordCredentials;
import org.apache.hc.client5.http.impl.auth.BasicCredentialsProvider;
import org.apache.hc.client5.http.impl.nio.PoolingAsyncClientConnectionManager;
import org.apache.hc.client5.http.impl.nio.PoolingAsyncClientConnectionManagerBuilder;
import org.apache.hc.core5.http.HttpHost;
import org.apache.hc.core5.reactor.IOReactorConfig;
import org.apache.hc.core5.util.TimeValue;
import org.opensearch.client.json.jackson.JacksonJsonpMapper;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.transport.OpenSearchTransport;
import org.opensearch.client.transport.httpclient5.ApacheHttpClient5TransportBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class OpenSearchConfig {
    @Autowired
    CacheWrapper cacheWrapper;

    @Autowired
    private HealthMetrics metrics;

    @Value("${opensearch.connection.io.reactor.size:2}")
    private int osIOReactor;

    private final List<OpenSearchClient> openSearchClients = new ArrayList<>();
    public final Map<String, OpenSearchClient> accountZoneOpenSearchConfigMap = new ConcurrentHashMap<>();

    public OpenSearchClient getOpenSearchClient(String accountIdentifier, String indexName) {
        String zone;
        List<OSIndexZoneDetails> osIndexZoneDetails = cacheWrapper.getHealIndexZones();
        if (osIndexZoneDetails == null || osIndexZoneDetails.isEmpty()) {
            log.warn("No OS Index to zone mappings found in redis. Defaulting to 'MISC' zone. Account:{}, index:{}", accountIdentifier, indexName);
            zone = "MISC";
        } else {
            OSIndexZoneDetails zoneDetails = osIndexZoneDetails.stream().filter(c -> c.getIndexName().equals(indexName)).findAny().orElse(null);
            if (zoneDetails == null) {
                log.warn("No zone found for index:{}, account:{}. Defaulting to 'MISC' zone.", indexName, accountIdentifier);
                zone = "MISC";
            } else {
                zone = zoneDetails.getZoneName();
            }
        }

        String key = accountIdentifier + "_" + zone;

        return accountZoneOpenSearchConfigMap.compute(key, (k, existingClient) -> {
            if (existingClient == null) {
                log.warn("OpenSearch Client is null! Need to re-initialize, key:{}", key);
            } else {
                try {
                    if (existingClient.ping() == null || !existingClient.ping().value()) {
                        log.warn("Ping failed! Need to re-initialize OpenSearch Client, key:{}", key);
                        existingClient.shutdown();
                    } else {
                        log.debug("OpenSearch Client is initialized for account {} and zone {}", accountIdentifier, zone);
                        return existingClient;
                    }
                } catch (Exception eox) {
                    try {
                        existingClient.shutdown();
                    } catch (Exception e) {
                        log.error("Exception when closing OpenSearch client, key:{}", key, e);
                    }
                }
            }

            Account account = cacheWrapper.getAccountDetails(accountIdentifier);
            if (account == null) {
                log.error("Account details not found for accountIdentifier: {}, index:{}", accountIdentifier, indexName);
                return null;
            }

            TenantDetails tenantDetails = account.getTenantDetails();
            if (tenantDetails == null) {
                log.error("Tenant details not found for accountIdentifier: {}, index:{}", accountIdentifier, indexName);
                return null;
            }

            List<TenantOpenSearchDetails> tenantOpenSearchDetailsList = cacheWrapper.getTenantOpensearchDetails(tenantDetails.getTenantIdentifier());
            if (tenantOpenSearchDetailsList.isEmpty()) {
                log.error("OpenSearch mapping not found for tenantId: {}, account:{}, index:{}", tenantDetails.getTenantId(), accountIdentifier, indexName);
                return null;
            }

            TenantOpenSearchDetails tenantOpenSearchDetails = tenantOpenSearchDetailsList.stream()
                    .filter(c -> c.getZone().equals(zone)).findAny().orElse(null);
            if (tenantOpenSearchDetails == null) {
                log.error("No OpenSearch config found for account:{}, zone: {} and tenantId: {}", accountIdentifier, zone, tenantDetails.getTenantId());
                return null;
            }

            return createOpenSearchClient(tenantOpenSearchDetails, accountIdentifier, indexName);
        });
    }

    private OpenSearchClient createOpenSearchClient(TenantOpenSearchDetails openSearchDetails, String accountIdentifier, String indexName) {
        // Custom thread factory for naming threads for both IO reactor and connection pool
        ThreadFactory customThreadFactory = new OpenSearchClientThreadFactory(accountIdentifier + "-io-thread-");

        OpenSearchClient client;
        try {
            List<HttpHost> httpHosts = getHttpHostList(openSearchDetails);
            if (httpHosts.isEmpty()) {
                log.error("Http hosts list is empty. OpenSearch:{}, account:{}, index:{}", openSearchDetails, accountIdentifier, indexName);
                return null;
            }
            final OpenSearchTransport transport = ApacheHttpClient5TransportBuilder.builder(httpHosts.toArray(new HttpHost[0]))
                    .setMapper(new JacksonJsonpMapper())
                    .setHttpClientConfigCallback(httpClientBuilder -> {

                        final PoolingAsyncClientConnectionManager connectionManager = PoolingAsyncClientConnectionManagerBuilder
                                .create()
                                .setMaxConnPerRoute(openSearchDetails.getPerRouteConnections())
                                .setMaxConnTotal(openSearchDetails.getMaxConnections())
                                .setConnectionTimeToLive(TimeValue.of(openSearchDetails.getConnectionTimeout(), TimeUnit.MILLISECONDS))
                                .build();

                        if (!com.appnomic.appsone.common.util.StringUtils.isEmpty(openSearchDetails.getUsername()) && !StringUtils.isEmpty(openSearchDetails.getEncryptedPassword())) {
                            final BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                            String decryptedPwd = new String(Base64.getDecoder().decode(openSearchDetails.getEncryptedPassword()), StandardCharsets.UTF_8);
                            httpHosts.forEach(httpHost ->
                                    credentialsProvider.setCredentials(new AuthScope(httpHost), new UsernamePasswordCredentials(openSearchDetails.getUsername(), decryptedPwd.toCharArray())));

                            return httpClientBuilder
                                    .setDefaultCredentialsProvider(credentialsProvider)
                                    .setConnectionManager(connectionManager)
                                    .setThreadFactory(customThreadFactory)
                                    .setIOReactorConfig(IOReactorConfig.custom().setIoThreadCount(osIOReactor).build())
                                    .setKeepAliveStrategy((r, c) -> TimeValue.ofSeconds(openSearchDetails.getKeepAliveSecs()));
                        } else {
                            return httpClientBuilder
                                    .setConnectionManager(connectionManager)
                                    .setThreadFactory(customThreadFactory)
                                    .setIOReactorConfig(IOReactorConfig.custom().setIoThreadCount(osIOReactor).build())
                                    .setKeepAliveStrategy((r, c) -> TimeValue.ofSeconds(openSearchDetails.getKeepAliveSecs()));
                        }
                    }).build();

            client = new OpenSearchClient(transport);
            if (client.ping().value()) {
                log.info("Established connection with OpenSearch nodes [{}] with protocol:{}, username:{}, account:{}, index:{} ",
                        openSearchDetails.getNodeAddresses(), openSearchDetails.getProtocol(), openSearchDetails.getUsername(), accountIdentifier, indexName);
            } else {
                throw new Exception(String.format("Unable to establish connection to OpenSearch nodes [%s]", openSearchDetails.getNodeAddresses()));
            }
        } catch (Throwable e) {
            metrics.updateOpenSearchErrors(1);
            client = null;
            log.error("Error while establishing connection to OpenSearch nodes [{}], Protocol:{}, username:{}, account:{}, index:{}",
                    openSearchDetails.getNodeAddresses(), openSearchDetails.getProtocol(), openSearchDetails.getUsername(), accountIdentifier, indexName, e);
        }

        openSearchClients.add(client);
        return client;
    }

    private List<HttpHost> getHttpHostList(TenantOpenSearchDetails openSearchDetails) {
        List<String> nodesList = Arrays.asList(openSearchDetails.getNodeAddresses().split(","));
        List<HttpHost> httpHosts = new ArrayList<>();
        nodesList.forEach(node -> {
            try {
                String[] hostAndPort = node.split(":");
                HttpHost httpHost = new HttpHost(openSearchDetails.getProtocol(), hostAndPort[0], Integer.parseInt(hostAndPort[1]));
                httpHosts.add(httpHost);
            } catch (Exception e) {
                metrics.updateOpenSearchErrors(1);
                log.error("Invalid OpenSearch node details, Host: {}", node, e);
            }
        });
        return httpHosts;
    }

    @PreDestroy
    public void shutdown() {
        log.info("Opensearch connection shutdown method called.");
        openSearchClients.stream().filter(Objects::nonNull).forEach(c -> {
            try {
                c.shutdown();
            } catch (Exception e) {
                log.error("Exception while closing OpenSearch client", e);
            }
        });
    }
}

