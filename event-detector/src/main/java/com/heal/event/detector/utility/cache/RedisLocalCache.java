package com.heal.event.detector.utility.cache;

import com.heal.configuration.entities.BasicInstanceBean;
import com.heal.configuration.pojos.*;
import com.heal.event.detector.repo.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class RedisLocalCache {

    @Autowired
    RedisUtilities redisUtilities;

    @Cacheable(value = CacheConstants.ACCOUNT, key = "#accountIdentifier", unless = "#result == null")
    public Account getAccountDetails(String accountIdentifier) {
        log.debug("Fetching account details for the accountIdentifier : {} for the first time from redis.", accountIdentifier);
        return redisUtilities.getAccountDetails(accountIdentifier);
    }

    @Cacheable(value = CacheConstants.INSTANCE, key = "#accountIdentifier + ':' + #instanceIdentifier", unless = "#result == null")
    public CompInstClusterDetails getInstanceDetails(String accountIdentifier, String instanceIdentifier) {
        log.debug("Fetching instance details for the accountIdentifier : {}, instance identifier : {} for the first time from redis", accountIdentifier, instanceIdentifier);
        return redisUtilities.getInstanceDetails(accountIdentifier, instanceIdentifier);
    }

    @Cacheable(value = CacheConstants.INSTANCE_KPI, key = "#accountIdentifier + ':' + #instanceIdentifier + ':' + #kpiId", unless = "#result == null")
    public CompInstKpiEntity getInstanceKPIDetails(String accountIdentifier, String instanceIdentifier, int kpiId) {
        log.debug("Fetching instance kpi details for the accountIdentifier : {}, instanceIdentifier : {}, kpiId : {} for the first time from redis", accountIdentifier, instanceIdentifier, kpiId);
        return redisUtilities.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId);
    }

    @Cacheable(value = CacheConstants.COMPONENT_KPI, key = "#accountIdentifier + ':' + #componentIdentifier + ':' + #kpiId", unless = "#result == null")
    public ComponentKpiEntity getComponentKPIDetails(String accountIdentifier, String componentIdentifier, String kpiId) {
        log.debug("Fetching components kpi details for the accountIdentifier : {}, componentIdentifier : {}, kpiId : {} for the first time from redis", accountIdentifier, componentIdentifier, kpiId);
        return redisUtilities.getComponentKPIDetails(accountIdentifier, componentIdentifier, kpiId);
    }

    @Cacheable(value = CacheConstants.HEAL_TYPES, unless = "#result == null || #result.isEmpty()")
    public List<ViewTypes> getMstTypes() {
        log.debug("Fetching heal types details for the first time from redis");
        return redisUtilities.getMstTypes();
    }

    @Cacheable(value = CacheConstants.SERVICE, key = "#accountIdentifier + ':' + #serviceIdentifier", unless = "#result == null")
    public Service getServiceDetails(String accountIdentifier, String serviceIdentifier) {
        log.debug("Fetching service details for the accountIdentifier : {}, service identifier : {} for the first time from redis", accountIdentifier, serviceIdentifier);
        return redisUtilities.getServiceDetails(accountIdentifier, serviceIdentifier);
    }

    @Cacheable(value = CacheConstants.SERVICE_KPI, key = "#accountIdentifier + ':' + #serviceIdentifier + ':' + #kpiId", unless = "#result == null")
    public KpiDetails getServiceKPIDetails(String accountIdentifier, String serviceIdentifier, int kpiId) {
        log.debug("Fetching service kpi details for the accountIdentifier : {}, serviceIdentifier : {}, kpiId : {} for the first time from redis", accountIdentifier, serviceIdentifier, kpiId);
        return redisUtilities.getServiceKPIDetails(accountIdentifier, serviceIdentifier, kpiId);
    }

    @Cacheable(value = CacheConstants.TRANSACTIONS, key = "#accountIdentifier + ':' + #transaction", unless = "#result == null")
    public Transaction getTransactionDetails(String accountIdentifier, String transaction) {
        log.debug("Fetching transaction details for the accountIdentifier : {}, transaction : {} for the first time from redis", accountIdentifier, transaction);
        return redisUtilities.getTransactionDetails(accountIdentifier, transaction);
    }

    @Cacheable(value = CacheConstants.TRANSACTIONS_VIOLATION, key = "#accountIdentifier + ':' + #transaction", unless = "#result == null || #result.isEmpty()")
    public List<TxnKPIViolationConfig> getTransactionViolationConfigDetails(String accountIdentifier, String transaction) {
        log.debug("Fetching transaction violation config details for the accountIdentifier : {}, transaction : {} for the first time from redis", accountIdentifier, transaction);
        return redisUtilities.getTransactionViolationConfigDetails(accountIdentifier, transaction);
    }
    @Cacheable(value = CacheConstants.INSTANCE_KPIS, key = "#accIdentifier + ':' + #instanceIdentifier", unless = "#result == null || #result.isEmpty()")
    public List<CompInstKpiEntity> getInstanceKPIDetails(String accIdentifier, String instanceIdentifier) {
        log.debug("Fetching transaction violation config details for the accountIdentifier : {}, transaction : {} for the first time from redis", accIdentifier, instanceIdentifier);
        return redisUtilities.getInstanceKPIDetails(accIdentifier, instanceIdentifier);
    }

    @Cacheable(value = CacheConstants.COMPONENT_KPIS, key = "#accountIdentifier + ':' + #componentName", unless = "#result == null || #result.isEmpty()")
    public List<ComponentKpiEntity> getComponentKPIs(String accountIdentifier, String componentName) {
        log.debug("Fetching component kpi details for the accountIdentifier : {}, component : {} for the first time from redis", accountIdentifier, componentName);
        return redisUtilities.getComponentKPIs(accountIdentifier, componentName);
    }

    @Cacheable(value = CacheConstants.APPLICATIONS, key = "#accountIdentifier + ':' + #applicationIdentifier", unless = "#result == null")
    public Application getApplicationByIdentifier(String accountIdentifier, String applicationIdentifier){
        log.debug("Fetching application details for the accountIdentifier : {}, application identifier : {} for the first time from redis", accountIdentifier, applicationIdentifier);
        return redisUtilities.getApplicationByIdentifier(accountIdentifier, applicationIdentifier);
    }

    @Cacheable(value = CacheConstants.APPLICATION_SERVICES, key = "#accountIdentifier + ':' + #applicationIdentifier", unless = "#result == null || #result.isEmpty()")
    public List<BasicEntity> getServicesMappedToApplication(String accountIdentifier, String applicationIdentifier){
        log.debug("Fetching service details mapped to application for the accountIdentifier : {}, application : {} for the first time from redis", accountIdentifier, applicationIdentifier);
        return redisUtilities.getServicesMappedToApplication(accountIdentifier, applicationIdentifier);
    }

    @Cacheable(value = CacheConstants.SERVICE_INSTANCES, key = "#accountIdentifier + ':' + #serviceIdentifier", unless = "#result == null || #result.isEmpty()")
    public List<BasicInstanceBean> getServiceInstances(String accountIdentifier, String serviceIdentifier) {
        log.debug("Fetching service wise instances detail for the accountIdentifier : {}, service : {} for the first time from redis", accountIdentifier, serviceIdentifier);
        return redisUtilities.getServiceInstances(accountIdentifier, serviceIdentifier, true);
    }

    @Cacheable(value = CacheConstants.SERVICE_TRANSACTIONS, key = "#accountIdentifier + ':' + #serviceIdentifier", unless = "#result == null || #result.isEmpty()")
    public List<BasicTransactionEntity> getServiceWiseTransaction(String accountIdentifier, String serviceIdentifier) {
        log.debug("Fetching service wise transactions for the accountIdentifier : {}, service : {} for the first time from redis", accountIdentifier, serviceIdentifier);
        return redisUtilities.getServiceWiseTransaction(accountIdentifier, serviceIdentifier);
    }

    @Cacheable(value = CacheConstants.SERVICE, key = "#accountIdentifier + ':' + #instanceIdentifier", unless = "#result == null || #result.isEmpty()")
    public List<BasicEntity> getServicesMappedToInstance(String accountIdentifier, String instanceIdentifier) {
        log.debug("Fetching services mapped to instance for the accountIdentifier : {}, instance : {} for the first time from redis", accountIdentifier, instanceIdentifier);
        return redisUtilities.getServicesMappedToInstance(accountIdentifier, instanceIdentifier);
    }

    @Cacheable(value = CacheConstants.APPLICATIONS, key = "#accountIdentifier + ':' + #serviceIdentifier", unless = "#result == null || #result.isEmpty()")
    public List<BasicEntity> getApplicationsMappedToService(String accountIdentifier, String serviceIdentifier) {
        log.debug("Fetching applications mapped to service for the accountIdentifier : {}, service : {} for the first time from redis", accountIdentifier, serviceIdentifier);
        return redisUtilities.getApplicationsMappedToService(accountIdentifier, serviceIdentifier);
    }

    @Cacheable(value = CacheConstants.SERVICE, key = "#accountIdentifier + ':' + #serviceId", unless = "#result == null")
    public BasicEntity getServiceDetailsFromServiceId(String accountIdentifier, int serviceId) {
        log.debug("Fetching service details from service id for the accountIdentifier : {}, serviceId : {} for the first time from redis", accountIdentifier, serviceId);
        return redisUtilities.getServiceDetailsFromServiceId(accountIdentifier, serviceId);
    }

    @Cacheable(value = CacheConstants.HEAL_TYPES, key = "#tenantIdentifier", unless = "#result == null || #result.isEmpty()")
    public List<TenantOpenSearchDetails> getTenantOpenSearchDetails(String tenantIdentifier) {
        log.debug("Fetching tenant opensearch mapping details for tenantIdentifier {} for the first time from redis", tenantIdentifier);
        return redisUtilities.getTenantOpenSearchDetails(tenantIdentifier);
    }

    @Cacheable(value = CacheConstants.HEAL_TYPES, key = "'heal_index_zones'", unless = "#result == null || #result.isEmpty()")
    public List<OSIndexZoneDetails> getHealIndexZones() {
        log.debug("Fetching heal opensearch index to zone mapping details for the first time from redis");
        return redisUtilities.getHealIndexZones();
    }

    @Cacheable(value = CacheConstants.ANOMALY_SIGNAL, key = "#accountIdentifier + ':' + #applicationIdentifier", unless = "#result == null")
    public ApplicationSettings getApplicationSettings(String accountIdentifier, String applicationIdentifier) {
        log.debug("Fetching application settings for the accountIdentifier : {}, application identifier : {} for the first time from redis", accountIdentifier, applicationIdentifier);
        return redisUtilities.getApplicationSettings(accountIdentifier, applicationIdentifier);
    }
}
