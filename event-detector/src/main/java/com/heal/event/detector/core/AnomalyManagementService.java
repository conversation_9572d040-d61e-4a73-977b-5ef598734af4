package com.heal.event.detector.core;

import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.Alerts;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.protbuf.AnomalySummaryProtos;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.utility.DateTimeUtil;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.StringUtils;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.heal.event.detector.utility.Constants.*;

/**
 * Service for managing anomaly lifecycle operations
 * Provides methods to create, update, and close anomalies across RabbitMQ, Redis, and OpenSearch
 */
@Slf4j
@Service
public class AnomalyManagementService {

    @Autowired
    private RedisUtilities redisUtilities;

    @Autowired
    private OpenSearchRepo openSearchRepo;

    @Autowired
    private HealthMetrics metrics;

    @Autowired
    CacheWrapper cacheWrapper;

    @Value("${entity.type.instance:INSTANCE}")
    String entityTypeInstance;

    @Value("${entity.type.transaction:TRANSACTION}")
    String entityTypeTransaction;

    private static final String AE_PREFIX = "AE";
    private static final String AE_SPLITTER = "-";

    /**
     * Creates a new anomaly and publishes to RabbitMQ, stores in Redis, and indexes in OpenSearch
     *
     * @param anomalyAccountPojo The anomaly data to create
     * @return AnomalyResponse indicating success/failure and processing status
     */
    public AnomalyAccountPojo createAnomaly(AnomalyAccountPojo anomalyAccountPojo, Alerts alerts, List<String> appIds, ViolationDetails violationDetails) {
        Anomalies anomalies = anomalyAccountPojo.getAnomalyDetails();
        String accountId = anomalyAccountPojo.getAccountIdentifier();
        String entityId = anomalies.getEntityId();
        String kpiId = String.valueOf(anomalies.getKpiId());
        String kpiAttribute = anomalies.getKpiAttribute();

        // Generate anomaly ID if not provided
        if (anomalies.getAnomalyId() == null || anomalies.getAnomalyId().isEmpty()) {
            String anomalyEvent = getAnomalyIdentifier(accountId, anomalies);
            anomalies.setAnomalyId(anomalyEvent);
            log.debug("Generated anomaly ID: {} for account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                    anomalyEvent, accountId, entityId, kpiId, kpiAttribute);
        }
        log.info("Creating anomaly for account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}, anomalyId: {}",
                accountId, entityId, kpiId, kpiAttribute, anomalies.getAnomalyId());

        anomalies.setAnomalyStatus(ANOMALY_STATUS_OPEN);
        anomalies.setAnomalyCreatedTime(DateTimeUtil.getCurrentGMTTimeMillisUsingCalendar()); // Explicit GMT time using Instant.now()

        String alertId = null;
        int alertOccurrenceCount = 1;
        if (anomalies.getAnomalyId() != null) {
            // Generate AlertId using anomaly ID + alert occurrence count (starting with 1)
            alertId = generateAlertId(anomalies.getAnomalyId(), alertOccurrenceCount);
            log.debug("Generated AlertId: {} for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                    alertId, anomalies.getAnomalyId(), accountId, entityId, kpiId, kpiAttribute);
        }
        alerts.setAlertStatus(ALERT_STATUS_OPEN);
        Map<String, String> metadata = new HashMap<>();
        metadata.put("alertId", alertId);
        metadata.put("alertStatus", alerts.getAlertStatus());
        metadata.put("persistence", alerts.getPersistence());
        metadata.put("suppression", alerts.getSuppression());
        metadata.put("alertType", alerts.getAlertType());
        metadata.put("appIds", String.join(",", appIds));
        for (Map.Entry<String, Double> entry : alerts.getThresholds().entrySet()) {
            String key = "thresholds" + entry.getKey();
            Double value = entry.getValue();
            metadata.put(key, String.valueOf(value));
        }
        metadata.put("anomalyScore", alerts.getAnomalyScore());
        metadata.put("value", alerts.getValue());
        metadata.put("alertOccurrenceCount", String.valueOf(alertOccurrenceCount));
        anomalies.setMetadata(metadata);
        anomalies.setLastAlertTime(DateTimeUtil.getCurrentGMTTimeMillisUsingCalendar()); // Explicit GMT time
        anomalyAccountPojo.setAnomalyDetails(anomalies);

        try {
            String entityType = anomalies.getEntityType();
            if (violationDetails != null) {
                AnomalyEventStatus anomalyEventStatus = violationDetails.getAnomalyEventStatus();
                if (anomalyEventStatus == null) {
                    anomalyEventStatus = new AnomalyEventStatus();
                    anomalyEventStatus.setAnomalyEventId(anomalies.getAnomalyId());
                    anomalyEventStatus.setAnomalyStatus(anomalies.getAnomalyStatus());
                    anomalyEventStatus.setAnomalyStartTime(anomalies.getAnomalyStartTime());
                    anomalyEventStatus.setLastAnomalySeverity(String.valueOf(anomalies.getLastSeverityId()));
                    log.debug("Created new AnomalyEventStatus for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                            anomalies.getAnomalyId(), accountId, entityId, kpiId, kpiAttribute);
                }
                violationDetails.setAnomalyEventStatus(anomalyEventStatus);
                redisUtilities.putViolationDetails(accountId, entityId, entityType,
                        kpiId, kpiAttribute, anomalies.getThresholdType().equalsIgnoreCase("Static") ? "SOR" : "NOR", violationDetails);
                log.debug("Anomaly stored in Redis successfully for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                        anomalies.getAnomalyId(), accountId, entityId, kpiId, kpiAttribute);
            } else {
                log.warn("ViolationDetails is null for account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}, thresholdType: {}, anomalyId: {}",
                        accountId, entityId, kpiId, kpiAttribute, anomalies.getThresholdType(), anomalies.getAnomalyId());
            }

            // Update metrics for anomaly creation
            metrics.updateAnomalyCreateCount(1);
            metrics.updateAlertCount(1); // Alert is created with the anomaly

            log.info("Anomaly created successfully - anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                    anomalies.getAnomalyId(), accountId, entityId, kpiId, kpiAttribute);
            return anomalyAccountPojo;
        } catch (Exception e) {
            log.error("Error creating anomaly - anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                    anomalies.getAnomalyId(), accountId, entityId, kpiId, kpiAttribute, e);
        }
        return null;
    }

    private String getAnomalyIdentifier(String accountIdentifier, Anomalies anomalies) {
        String instId = anomalies.getEntityId();

        Account accountData = cacheWrapper.getAccountDetails(accountIdentifier);

        String anomalyEvent = null;

        if (anomalies.getEntityType().equalsIgnoreCase(entityTypeTransaction)) {
            Transaction txnData = cacheWrapper.getTransactionDetails(accountIdentifier, instId);
            if (Objects.nonNull(accountData) && Objects.nonNull(txnData)) {
                anomalyEvent = AE_PREFIX + AE_SPLITTER + accountData.getId() +
                        AE_SPLITTER +
                        txnData.getId() +
                        AE_SPLITTER +
                        anomalies.getKpiId() +
                        AE_SPLITTER +
                        "T" +
                        AE_SPLITTER +
                        (anomalies.getThresholdType().equals("SOR") ? "S" : "N") +
                        AE_SPLITTER +
                        TimeUnit.MILLISECONDS.toMinutes(anomalies.getAnomalyStartTime());
            }
        } else {
            boolean isGroupKpi = anomalies.getMetadata() != null && Boolean.parseBoolean(anomalies.getMetadata().getOrDefault("isGroupKpi", (StringUtils.isEmpty(anomalies.getKpiAttribute())
                    && !anomalies.getKpiAttribute().trim().equals(COMMON_ATTRIBUTE)) ? "true" : "false"));

            CompInstClusterDetails instData = cacheWrapper.getInstanceDetails(accountIdentifier, instId);
            if (Objects.nonNull(accountData) && Objects.nonNull(instData)) {
                anomalyEvent = AE_PREFIX + AE_SPLITTER + accountData.getId() +
                        AE_SPLITTER +
                        instData.getId() +
                        AE_SPLITTER +
                        anomalies.getKpiId() +
                        AE_SPLITTER +
                        "C" +
                        AE_SPLITTER +
                        (anomalies.getThresholdType().equals("SOR") ? "S" : "N") +
                        AE_SPLITTER +
                        (!StringUtils.isEmpty(anomalies.getKpiAttribute()) ? (isGroupKpi ? String.valueOf(anomalies.getKpiAttribute().trim().hashCode()) : anomalies.getKpiAttribute().trim()).concat(AE_SPLITTER) : "") +
                        TimeUnit.MILLISECONDS.toMinutes(anomalies.getAnomalyStartTime());
            }
        }
        if (anomalyEvent == null) {
            String anomalyIdPrefix = anomalies.getThresholdType().equals("SOR") ? "AE-S-" : "AE-N-";
            anomalyEvent = anomalyIdPrefix + UUID.randomUUID();
        }
        return anomalyEvent;
    }

    /**
     * Updates an existing anomaly across RabbitMQ, Redis, and OpenSearch
     *
     * @param anomalyAccountPojo The updated anomaly data
     * @return AnomalyResponse indicating success/failure and processing status
     */
    public AnomalyAccountPojo updateAnomaly(AnomalyAccountPojo anomalyAccountPojo, Alerts alerts, List<String> appIds, ViolationDetails violationDetails) {
        Anomalies anomalies = anomalyAccountPojo.getAnomalyDetails();
        String accountId = anomalyAccountPojo.getAccountIdentifier();
        String entityId = anomalies.getEntityId();
        String kpiId = String.valueOf(anomalies.getKpiId());
        String kpiAttribute = anomalies.getKpiAttribute();
        if (violationDetails != null && violationDetails.getAnomalyEventStatus() != null) {
            anomalies.setAnomalyId(violationDetails.getAnomalyEventStatus().getAnomalyEventId());
        }
        String anomalyId = anomalies.getAnomalyId();

        log.info("Updating anomaly for account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}, anomalyId: {}",
                accountId, entityId, kpiId, kpiAttribute, anomalyId);

        if (anomalyId == null || anomalyId.isEmpty()) {
            log.error("AnomalyId is null for account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}. Hence it will not be processed further.",
                    accountId, entityId, kpiId, kpiAttribute);
            metrics.updateViolatedEventProcessingErrors();
            return null;
        }

        anomalies.setAnomalyStatus(ANOMALY_STATUS_ONGOING);
        try {
            String entityType = anomalies.getEntityType();
            // 2. Update in Redis
            if (violationDetails != null) {
                AnomalyEventStatus anomalyEventStatus = violationDetails.getAnomalyEventStatus();
                if (anomalyEventStatus != null) {
                    anomalyEventStatus.setAnomalyStatus(anomalies.getAnomalyStatus());
                    anomalyEventStatus.setLastAnomalySeverity(String.valueOf(anomalies.getLastSeverityId()));
                    violationDetails.setAnomalyEventStatus(anomalyEventStatus);
                    redisUtilities.putViolationDetails(accountId, entityId, entityType,
                            kpiId, kpiAttribute, anomalies.getThresholdType().equalsIgnoreCase("Static") ? "SOR" : "NOR", violationDetails);
                    log.debug("Anomaly updated in Redis successfully for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                            anomalyId, accountId, entityId, kpiId, kpiAttribute);
                } else {
                    log.error("AnomalyEventStatus is null in ViolationDetails for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}. Hence it will not be processed further.",
                            anomalyId, accountId, entityId, kpiId, kpiAttribute);
                    metrics.updateViolatedEventProcessingErrors();
                    return null;
                }
            } else {
                log.error("ViolationDetails is null for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}. Hence it will not be processed further.",
                        anomalyId, accountId, entityId, kpiId, kpiAttribute);
                metrics.updateViolatedEventProcessingErrors();
                return null;
            }

            // 3 Read from OpenSearch heal_anomalies_* index as fallback
            AnomalyAccountPojo existingAnomalyAccountPojo = openSearchRepo.getAnomalyFromOpenSearch(accountId, anomalyId);

            if (existingAnomalyAccountPojo == null) {
                log.error("AnomalyAccountPojo is null from OpenSearch for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}. Hence it will not be processed further.",
                        anomalyId, accountId, entityId, kpiId, kpiAttribute);
                metrics.updateViolatedEventProcessingErrors();
                return null;
            }

            String alertId;
            int alertOccurrenceCount = 1;
            // Get the current alert occurrence count and increment it
            alertOccurrenceCount += Integer.parseInt(existingAnomalyAccountPojo.getAnomalyDetails().getMetadata().get("alertOccurrenceCount"));
            alertId = generateAlertId(anomalyId, alertOccurrenceCount);
            log.debug("Generated AlertId: {} for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}, occurrence: {}",
                    alertId, anomalyId, accountId, entityId, kpiId, kpiAttribute, alertOccurrenceCount);
            alerts.setAlertStatus(ALERT_STATUS_UPDATE);

            Map<String, String> metadata = new HashMap<>();
            metadata.put("alertId", alertId);
            metadata.put("alertStatus", alerts.getAlertStatus());
            metadata.put("persistence", alerts.getPersistence());
            metadata.put("suppression", alerts.getSuppression());
            metadata.put("alertType", alerts.getAlertType());
            metadata.put("appIds", String.join(",", appIds));
            for (Map.Entry<String, Double> entry : alerts.getThresholds().entrySet()) {
                String key = "thresholds" + entry.getKey();
                Double value = entry.getValue();
                metadata.put(key, String.valueOf(value));
            }
            metadata.put("anomalyScore", alerts.getAnomalyScore());
            metadata.put("value", alerts.getValue());
            metadata.put("alertOccurrenceCount", String.valueOf(alertOccurrenceCount));
            anomalies.setMetadata(metadata);
            anomalies.setLastAlertTime(DateTimeUtil.getCurrentGMTTimeMillisUsingCalendar()); // Explicit GMT time
            anomalyAccountPojo.setAnomalyDetails(anomalies);

            // 4. Merge updates with existing data
            AnomalyAccountPojo updatedAnomalyAccountPojo = mergeAnomalyData(existingAnomalyAccountPojo, anomalyAccountPojo);

            // Update metrics for anomaly update
            metrics.updateAnomalyUpdateCount(1);
            metrics.updateAlertCount(1); // Alert is created with the update

            log.info("Anomaly updated successfully - anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                    anomalyId, accountId, entityId, kpiId, kpiAttribute);
            return updatedAnomalyAccountPojo;

        } catch (Exception e) {
            log.error("Error updating anomaly - anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                    anomalyId, accountId, entityId, kpiId, kpiAttribute, e);
        }
        return null;
    }

    /**
     * Closes an existing anomaly across RabbitMQ, Redis, and OpenSearch
     *
     * @return AnomalyResponse indicating success/failure and processing status
     */
    public AnomalyAccountPojo closeAnomaly(AnomalySummaryProtos.AnomalySummary anomalySummary) {
        String accountId = anomalySummary.getAccountIdentifier();
        String entityId = anomalySummary.getEntityIdentifier();
        String entityType = anomalySummary.getEntityType();
        String kpiId = anomalySummary.getKpiId();
        String kpiAttribute = anomalySummary.getKpiAttribute();
        String anomalyId = anomalySummary.getAnomalyId();

        log.info("Closing anomaly for account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}, anomalyId: {}",
                accountId, entityId, kpiId, kpiAttribute, anomalyId);

        if (anomalyId.isEmpty()) {
            log.error("AnomalyId is null for accountId: {}, entityId: {}, kpiId: {}, kpiAttribute: {}. Hence it will not be processed further.",
                    accountId, entityId, kpiId, kpiAttribute);
            metrics.updateViolatedEventProcessingErrors();
            return null;
        }

        Alerts alerts = Alerts.builder()
                .anomalyId(anomalyId)
                .alertStatus(anomalySummary.getAnomalyStatus())
                .build();

        try {
            // 1. Read existing anomaly from Redis and delete
            ViolationDetails violationDetails = redisUtilities.getViolationDetails(accountId, entityId, entityType, kpiId, kpiAttribute,
                    anomalySummary.getViolationFor());
            if (violationDetails != null) {
                redisUtilities.deleteViolationDetails(accountId, entityId, entityType, kpiId, kpiAttribute,
                        anomalySummary.getViolationFor());
                log.debug("Deleted ViolationDetails from Redis for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                        anomalyId, accountId, entityId, kpiId, kpiAttribute);
            } else {
                log.warn("ViolationDetails not found in Redis for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                        anomalyId, accountId, entityId, kpiId, kpiAttribute);
            }

            // Read from OpenSearch heal_anomalies_* index as fallback
            AnomalyAccountPojo existingAnomalyAccountPojo = openSearchRepo.getAnomalyFromOpenSearch(accountId, anomalyId);

            if (existingAnomalyAccountPojo == null) {
                log.error("AnomalyAccountPojo is null from OpenSearch for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}. Hence it will not be processed further.",
                        anomalyId, accountId, entityId, kpiId, kpiAttribute);
                metrics.updateViolatedEventProcessingErrors();
                return null;
            }

            String alertId = null;
            int alertOccurrenceCount = 1;
            // Get the final alert occurrence count for closing
            alertOccurrenceCount += Integer.parseInt(existingAnomalyAccountPojo.getAnomalyDetails().getMetadata().get("alertOccurrenceCount"));
            alertId = generateAlertId(anomalyId, alertOccurrenceCount);
            log.debug("Generated closing AlertId: {} for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}, occurrence: {}",
                    alertId, anomalyId, accountId, entityId, kpiId, kpiAttribute, alertOccurrenceCount);
            alerts.setAlertStatus(ALERT_STATUS_CLOSE);

            // 2. Update anomaly status to CLOSED
            Anomalies existingAnomaly = existingAnomalyAccountPojo.getAnomalyDetails();
            existingAnomaly.setAnomalyStatus(ANOMALY_STATUS_CLOSED);
            existingAnomaly.setClosingReason(anomalySummary.getClosingReason());
            log.debug("Updated anomaly status to CLOSED with reason: {} for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                    anomalySummary.getClosingReason(), anomalyId, accountId, entityId, kpiId, kpiAttribute);

            Map<String, String> metadata = new HashMap<>(existingAnomaly.getMetadata());
            metadata.put("alertId", alertId);
            metadata.put("alertStatus", alerts.getAlertStatus());

            int persistence = Optional.ofNullable(existingAnomaly.getLow())
                    .map(ViolationStatus::getPersistence)
                    .orElseGet(() -> Optional.ofNullable(existingAnomaly.getMedium())
                            .map(ViolationStatus::getPersistence)
                            .orElseGet(() -> Optional.ofNullable(existingAnomaly.getHigh())
                                    .map(ViolationStatus::getPersistence)
                                    .orElse(0)));

            int suppression = Optional.ofNullable(existingAnomaly.getLow())
                    .map(ViolationStatus::getSuppression)
                    .orElseGet(() -> Optional.ofNullable(existingAnomaly.getMedium())
                            .map(ViolationStatus::getSuppression)
                            .orElseGet(() -> Optional.ofNullable(existingAnomaly.getHigh())
                                    .map(ViolationStatus::getSuppression)
                                    .orElse(0)));

            metadata.put("persistence", String.valueOf(persistence));
            metadata.put("suppression", String.valueOf(suppression));
            metadata.put("alertType", persistence != 0 ? "Persistence" : "Suppression");
            if (anomalySummary.getAppIdCount() > 0 && !anomalySummary.getAppId(0).isEmpty()) {
                metadata.put("appIds", anomalySummary.getAppId(0));
            }
            metadata.put("alertOccurrenceCount", String.valueOf(alertOccurrenceCount));
            existingAnomaly.setMetadata(metadata);
            existingAnomaly.setLastAlertTime(DateTimeUtil.getCurrentGMTTimeMillisUsingCalendar()); // Explicit GMT time
            existingAnomalyAccountPojo.setAnomalyDetails(existingAnomaly);

            // Update metrics for anomaly closure
            metrics.updateAnomalyCloseCount(1);
            metrics.updateAlertCount(1); // Alert is created for closure

            log.info("Anomaly closed successfully - anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}, closingReason: {}",
                    anomalyId, accountId, entityId, kpiId, kpiAttribute, anomalySummary.getClosingReason());
            return existingAnomalyAccountPojo;
        } catch (Exception e) {
            log.error("Error closing anomaly - anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                    anomalyId, accountId, entityId, kpiId, kpiAttribute, e);
        }
        return null;
    }

    /**
     * Merges update data with existing anomaly data
     */
    private AnomalyAccountPojo mergeAnomalyData(AnomalyAccountPojo existing, AnomalyAccountPojo updated) {
        Anomalies existingAnomalies = existing.getAnomalyDetails();
        Anomalies updatedAnomalies = updated.getAnomalyDetails();

        existingAnomalies.setAnomalyEndTime(updatedAnomalies.getAnomalyEndTime());

        if (updatedAnomalies.getAnomalyCreatedTime() != 0) {
            existingAnomalies.setAnomalyCreatedTime(updatedAnomalies.getAnomalyCreatedTime());
        }

        if (updatedAnomalies.getLastAlertTime() != 0) {
            existingAnomalies.setLastAlertTime(updatedAnomalies.getLastAlertTime());
        }

        existingAnomalies.setAnomalyStatus(updatedAnomalies.getAnomalyStatus());

        if (updatedAnomalies.getLastSeverityId() != 0) {
            existingAnomalies.setLastSeverityId(updatedAnomalies.getLastSeverityId());
        }
        existingAnomalies.setMetadata(updatedAnomalies.getMetadata());
        existingAnomalies.setLow(updatedAnomalies.getLow());
        existingAnomalies.setMedium(updatedAnomalies.getMedium());
        existingAnomalies.setHigh(updatedAnomalies.getHigh());
        existingAnomalies.setSeveritiesEnabled(updatedAnomalies.getSeveritiesEnabled());
        existingAnomalies.setViolationCountsReset(updatedAnomalies.getViolationCountsReset());

        if (updatedAnomalies.getThresholdType() != null) {
            existingAnomalies.setThresholdType(updatedAnomalies.getThresholdType());
        }

        if (updatedAnomalies.getClosingReason() != null) {
            existingAnomalies.setClosingReason(updatedAnomalies.getClosingReason());
        }

        log.debug("Merged anomaly data in-place for anomalyId: {}", existingAnomalies.getAnomalyId());

        return existing;
    }

    /**
     * Generates AlertId using anomaly ID + alert occurrence count.
     * Format: {anomalyId}-ALT-{occurrenceCount}
     *
     * @param anomalyId the anomaly ID
     * @param occurrenceCount the alert occurrence count (starts from 1)
     * @return generated alert ID
     */
    private String generateAlertId(String anomalyId, int occurrenceCount) {
        if (anomalyId == null || anomalyId.isEmpty()) {
            log.warn("Cannot generate AlertId: anomalyId is null or empty");
            return null;
        }

        String alertId = anomalyId + "-" + String.format("%03d", occurrenceCount);
        log.debug("Generated AlertId: {} from anomalyId: {} and occurrence: {}", alertId, anomalyId, occurrenceCount);
        return alertId;
    }
}