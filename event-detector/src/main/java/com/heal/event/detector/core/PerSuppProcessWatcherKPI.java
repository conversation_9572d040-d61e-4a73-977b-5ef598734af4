package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.pojos.enums.CommandMetaKey;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import com.heal.event.detector.utility.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PerSuppProcessWatcherKPI {

    @Autowired
    PersistenceSuppression persistenceSuppression;

    public AnomalyEventProtos.AnomalyEvent applyPersistenceSuppressionBasedOnType(ViolatedData value, int persistenceVal, int suppressionVal,
                                                                                  boolean isTxn, String serviceId, int isInformatic) {

        log.debug("Processing Watcher KPI for anomaly creation. Violated data : [{}]", value);

        long anomalyTime = TimeUnit.MINUTES.toMillis(TimeUnit.MILLISECONDS.toMinutes(System.currentTimeMillis()));

        String anomalyEvent = persistenceSuppression.getAnomalyIdentifier(value, isTxn);
        value.setPersistence(persistenceVal);
        value.setSuppression(suppressionVal);

        AnomalyEventProtos.AnomalyEvent.Builder anomalyEventBuilder = AnomalyEventProtos.AnomalyEvent.newBuilder();
        anomalyEventBuilder.setAccountId(value.getAccountId());
        anomalyEventBuilder.addAllAppId(new ArrayList<>(value.getAppIds()));
        anomalyEventBuilder.setAnomalyId(anomalyEvent);
        anomalyEventBuilder.setThresholdType(value.getThresholdType());
        anomalyEventBuilder.setOperationType(value.getOperationType());
        //violation time in ViolatedData is in milliseconds.
        anomalyEventBuilder.setStartTimeGMT(value.getViolationTime());
        anomalyEventBuilder.setEndTimeGMT(value.getViolationTime());
        anomalyEventBuilder.setAnomalyTriggerTimeGMT(anomalyTime);

        AnomalyEventProtos.KpiInfo.Builder kpiInfo = AnomalyEventProtos.KpiInfo.newBuilder();
        kpiInfo.setKpiId(value.getKpiId());
        kpiInfo.addSvcId(serviceId);
        Map<String, Double> thresholds = new HashMap<>();
        thresholds.put("isInformatic", (double) isInformatic);
        kpiInfo.putAllThresholds(thresholds);
        kpiInfo.setValue(value.getValue());

        if (null != value.getThresholdSeverity()) {
            kpiInfo.setThresholdSeverity(value.getThresholdSeverity());
        }

        if (value.getEventType().toString().equals(ViolationEventType.KPI_VIOLATION.toString())) {
            kpiInfo.setInstanceId(value.getInstanceId());
            kpiInfo.setIsWorkload(false);
            kpiInfo.setKpiAttribute(value.getKpiAttribute());
        } else if (value.getEventType().toString().equals(ViolationEventType.TXN_VIOLATION.toString())) {
            kpiInfo.setInstanceId(value.getTransactionId());
            kpiInfo.setIsWorkload(true);
            kpiInfo.setKpiAttribute(value.getResponseTimeType());
        }

        kpiInfo.putMetadata(CommandMetaKey.KPI_VIOLATION_TIME.getKey(), value.getKpiViolationTime());
        kpiInfo.putAllMetadata(value.fetchMetaData());
        Map<String, String> metaData = new HashMap<>();
        metaData.put("persistence", String.valueOf(value.getPersistence()));
        metaData.put("suppression", String.valueOf(value.getSuppression()));
        metaData.put("starttime", String.valueOf(value.getViolationTime()));
        if (!kpiInfo.getMetadataMap().containsKey("serviceIdentifier"))
            metaData.put("serviceIdentifier", serviceId);
        metaData.put("thresholdseverity", kpiInfo.getThresholdSeverity());
        metaData.put("isMaintenanceExcluded", value.isMaintenanceExcluded() ? "1" : "0");

        String[] strings = kpiInfo.getValue().split(Constants.DATA_SPLITTER_DEFAULT);
        if (strings.length > 2) {
            metaData.put("oldValue", strings[2].trim());
            metaData.put("newValue", strings[1].trim());
            metaData.put("operation", strings[0].trim());
        }
        metaData.putIfAbsent("kpiType", value.getKpiType().name());
        kpiInfo.putAllMetadata(metaData);

        anomalyEventBuilder.setKpis(kpiInfo.build());
        AnomalyEventProtos.AnomalyEvent event = anomalyEventBuilder.build();

        log.info("Anomaly created: {}", event);

        return event;
    }
}
