package com.heal.event.detector.exception;

public class EventDetectorException extends Exception {

    private final String errorMessage;
    private Object errorObject;

    public EventDetectorException(Throwable cause, String errorMessage) {
        super(errorMessage, cause);
        this.errorMessage = errorMessage;
    }

    public EventDetectorException(String errorMessage) {
        super("EventDetectorException : " + errorMessage);
        this.errorMessage = errorMessage;
    }

    public String getSimpleMessage() {
        return "EventDetectorException :: " + this.errorMessage;
    }

}
