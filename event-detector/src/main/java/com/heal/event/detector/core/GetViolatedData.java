package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.heal.configuration.enums.AnomalyClosureReason;
import com.heal.configuration.enums.OperationType;
import com.heal.configuration.pojos.*;
import com.heal.configuration.protbuf.AnomalySummaryProtos;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.pojos.BasicKPIStateInfo;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.pojos.enums.Intervals;
import com.heal.event.detector.pojos.enums.PersistenceSuppressionStatus;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.StringUtils;
import com.heal.event.detector.utility.Utils;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GetViolatedData {

    @Autowired
    RedisUtilities redisUtilities;
    @Autowired
    CacheWrapper cacheWrapper;
    @Autowired
    Utils utils;
    @Autowired
    PersistenceSuppression persistenceSuppression;
    @Autowired
    AnomalyManagementService anomalyManagementService;
    @Autowired
    AnomalyEventsProcess anomalyEventsProcess;

    @Value("${signal.severity.low:Default}")
    String lowSeveritySignal;

    @Value("${signal.severity.id.high:433}")
    String highSeverityIdSignal;

    @Value("${signal.severity.id.medium:432}")
    String mediumSeverityIdSignal;

    @Value("${signal.severity.id.low:431}")
    String lowSeverityIdSignal;

    @Value("${entity.type.instance:INSTANCE}")
    String entityTypeInstance;

    @Value("${entity.type.transaction:TRANSACTION}")
    String entityTypeTransaction;

    @Value("${entity.type.service:SERVICE}")
    String entityTypeService;

    @Value("${heal.transaction.component.identifier:Transaction}")
    private String transactionComponentIdentifier;

    @Value("${heal.batch.component.identifier:BatchProcess}")
    private String batchComponentIdentifier;

    @Value(("${heal.global.account.identifier:e573f852-5057-11e9-8fd2-b37b61e52317}"))
    private String globalAccountIdentifier;

    public List<ViolatedData> getViolatedDataObject(ViolatedEventProtos.ViolatedEvent violatedEventProto) {
        log.trace("ViolatedEvent in GetViolatedData: {}", violatedEventProto);

        List<ViolatedData> violatedDataList = new ArrayList<>();

        try {
            if (cacheWrapper.getAccountDetails(violatedEventProto.getAccountId()) == null) {
                log.error("Invalid account identifier [{}]", violatedEventProto.getAccountId());
                return Collections.emptyList();
            }

            if (!violatedEventProto.getThresholdType().equalsIgnoreCase("Static") && !violatedEventProto.getThresholdType().equalsIgnoreCase("RealTime")) {
                log.error("Received violated event has unsupported threshold type [{}]. Hence dropping the data point.",
                        violatedEventProto.getThresholdType());
                return null;
            }

            String timeInGMT = Utils.getKey(violatedEventProto.getViolationTmeInGMT(), Intervals.Minutely); //remove seconds
            long violatedTimeInEpochSec = Utils.dateStrToLocalDateTime(timeInGMT).toEpochSecond(ZoneOffset.UTC);

            log.debug("violatedEventProto.getViolationTmeInGMT(): {}, timeInGMT: {}, violatedTimeInEpochSec: {}",
                    violatedEventProto.getViolationTmeInGMT(), timeInGMT, violatedTimeInEpochSec);

            violatedDataList.addAll(getKpiViolatedData(violatedEventProto, timeInGMT, violatedTimeInEpochSec));
            violatedDataList.addAll(getTxnViolatedData(violatedEventProto, timeInGMT, violatedTimeInEpochSec));
            violatedDataList.addAll(getBatchJobViolatedData(violatedEventProto, timeInGMT, violatedTimeInEpochSec));
        } catch (Exception e) {
            log.error("Received incorrect date format, required date format[{}]. Hence ignoring below Violated Event\n{}\n", Constants.DATE_FORMAT, violatedEventProto, e);
        }
        return violatedDataList;
    }

    /**
     * Creates or Updates ViolationDetails for an event and handles closing of anomalies if required.
     *
     * @param accountIdentifier   String representing the account identifier.
     * @param entityIdentifier    String representing the entity identifier (instance or transaction).
     * @param kpiId               String representing the KPI identifier.
     * @param serviceIds          List of service identifiers associated with the KPI.
     * @param kv                  KpiViolationConfig object containing the violation configuration details.
     * @param thresholds          Map of thresholds for the KPI, containing lower and upper bounds.
     * @param serviceIdentifier   String representing the specific service identifier, if applicable.
     * @param violationLevel      String representing the violation level (e.g., INSTANCE, SERVICE).
     * @param opType              OperationType representing the operation type (e.g., GREATER_THAN, LESS_THAN).
     * @param kpiAttributeName    String representing the KPI attribute name (e.g., CPU, Memory).
     * @param kpiAttributeNameUsed String representing the actual KPI attribute name used in the violation.
     * @param eventTimeInEpochSec long representing the event time in epoch seconds.
     * @param violationFor        String representing the context of the violation (e.g., "KPI", "Transaction").
     * @param appIds              List of application identifiers associated with the KPI violation.
     * @param entityType          String representing the entity type (INSTANCE, TRANSACTION, etc).
     * @param violationDetails     ViolationDetails object to be updated or created.
     * @return Updated ViolationDetails object.
     */
    public ViolationDetails handleViolationDetails(String accountIdentifier, String entityIdentifier, String kpiId,
                                       List<String> serviceIds, KpiViolationConfig kv, Map<String, Double> thresholds, String serviceIdentifier,
                                       String violationLevel, OperationType opType, String kpiAttributeName, String kpiAttributeNameUsed, long eventTimeInEpochSec,
                                       String violationFor, List<String> appIds, String entityType, ViolationDetails violationDetails) {

        String severityId = String.valueOf(kv.getThresholdSeverityId());

        if (violationDetails != null) {
            String reason;

            // Ensure ViolationStatus exists for this severity
            if (!violationDetails.getViolationStatusMap().containsKey(severityId)) {
                violationDetails.getViolationStatusMap().put(severityId, buildViolationStatus(kv, thresholds));
            }

            boolean attributeNameChanged = violationDetails.checkAttributeNameMatchs(kpiAttributeNameUsed);
            // Check thresholds/operation name
            boolean thresholdsChanged = violationDetails.checkThresholdsOperationNameMatchForSeverity(severityId, kv.getOperation(), thresholds);
            // Check persistence/suppression config
            Map<String, Object> persSuppResult = isPersistenceSuppressionValid(violationDetails, kv, entityType, accountIdentifier, entityIdentifier, kpiId, serviceIds, serviceIdentifier, severityId);
            boolean persSuppChanged = (boolean) persSuppResult.get("isValid");
            int persistence = (int) persSuppResult.get("persistence");
            int suppression = (int) persSuppResult.get("suppression");
            String persSuppLevel = (String) persSuppResult.get("persSuppLevel");

            reason = !attributeNameChanged ? AnomalyClosureReason.MISSING_MISMATCHED_ATTRIBUTE.format("Attribute: {}", kpiAttributeNameUsed)
                    : (!thresholdsChanged ? AnomalyClosureReason.MISMATCHED_OPERATION_NAME_THRESHOLDS.format("OperationName: {}, Thresholds: {}", kv.getOperation(), thresholds)
                    : (!persSuppChanged ? AnomalyClosureReason.MISSING_MISMATCHED_PERSISTENCE_SUPPRESSION.format("Persistence/Suppression Level: {}, Persistence : {}, Suppression :{}", persSuppLevel, persistence, suppression) : null));

            // At the end, handle reset and anomaly close if needed
            if (reason != null) {
                log.warn("Threshold or persistence-suppression configuration mismatch for severity: {}, attribute: {}, kpiId: {}, entityType: {}, entityIdentifier: {}, serviceIds: {}, accountIdentifier: {}, anomalyClosureReason: {}",
                        severityId, kpiAttributeName, kpiId, entityType, entityIdentifier, serviceIds, accountIdentifier, reason);
                violationDetails = handleResetAndCloseAnomaly(violationDetails, accountIdentifier, entityIdentifier, kpiId, kpiAttributeName, violationFor, entityType, reason, appIds);
            }

            if (violationDetails != null) {
                // If the operation type is null, it means the value does not violate any thresholds. Also check if reset flag is set in case of any validation issues.
                if (Objects.isNull(opType)) {
                    log.info("No configuration change detected and kpi value is not breaching the thresholds. Resetting the violation counter for attribute: {}, " +
                                    "kpiId: {}, entityType: {}, entityIdentifier: {}, serviceIds: {}, accountIdentifier: {}, violationConfig: {}",
                            kpiAttributeName, kpiId, entityType, entityIdentifier, serviceIds, accountIdentifier, kv);
                    violationDetails.resetViolationStatusBySeverity(severityId);
                    return violationDetails;
                }

                violationDetails.updateViolationCountBySeverity(severityId, eventTimeInEpochSec);
                return violationDetails;
            }
        }
        // If violationDetails is null, create new
        int persistence = kv.getPersistence();
        int suppression = kv.getSuppression();
        int collectionInterval = 60;

        if (persistence <= 0 || suppression <= 0) {
            List<String> serviceIdList = serviceIdentifier == null ? serviceIds : List.of(serviceIdentifier);

            //TODO: Discuss with Prasad
            for (String serviceId : serviceIdList) {
                Map<String, Integer> persSupp = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(entityType, accountIdentifier, entityIdentifier,
                        kpiId, serviceId, severityId);

                if (persSupp == null || persSupp.isEmpty() || !persSupp.containsKey("collectionInterval") || !persSupp.containsKey("persistence") || !persSupp.containsKey("suppression")) {
                    log.warn("Persistence suppression configuration not found for entityType: {}, accountIdentifier: {}, instanceIdentifier: {}, kpiId: {}, serviceId: {}, severityId: {}",
                            entityType, accountIdentifier, entityIdentifier, kpiId, serviceId, severityId);
                    continue;
                }

                collectionInterval = persSupp.getOrDefault("collectionInterval", 60);
                persistence = persSupp.get("persistence");
                suppression = persSupp.get("suppression");

                break;
            }
        }

        if (persistence <= 0 || suppression <= 0) {
            log.warn("violationDetails not created as persistence suppression configuration not found for entityType: {}," +
                            " accountIdentifier: {}, instanceIdentifier: {}, kpiId: {}, serviceIds: {}, severityId: {}",
                    entityType, accountIdentifier, entityIdentifier, kpiId, serviceIds, severityId);
            return null;
        }

        ViolationStatus violationStatus = ViolationStatus.builder()
                .persistence(persistence)
                .suppression(suppression)
                .thresholds(thresholds)
                .operationName(kv.getOperation())
                .build();
        Map<String, ViolationStatus> violationStatusMap = new HashMap<>();
        violationStatusMap.put(severityId, violationStatus);

        violationDetails = ViolationDetails.builder()
                .violationStatusMap(violationStatusMap)
                .attributeName(kpiAttributeNameUsed)
                .violationLevel(violationLevel)
                .serviceList(serviceIdentifier == null ? serviceIds : Collections.singletonList(serviceIdentifier))
                .collectionInterval(collectionInterval)
                .build();

        violationDetails.updateViolationCountBySeverity(severityId, eventTimeInEpochSec);
        return violationDetails;
    }

    // Helper: Build ViolationStatus
    private ViolationStatus buildViolationStatus(KpiViolationConfig kv, Map<String, Double> thresholds) {
        return ViolationStatus.builder()
                .persistence(kv.getPersistence())
                .suppression(kv.getSuppression())
                .thresholds(thresholds)
                .operationName(kv.getOperation())
                .build();
    }

    // Helper: Validate persistence/suppression config
    private Map<String, Object> isPersistenceSuppressionValid(ViolationDetails violationDetails, KpiViolationConfig kv, String entityType, String accountIdentifier,
                                                  String instanceIdentifier, String kpiId, List<String> serviceIds, String serviceIdentifier, String severityId) {
        int persistence = kv.getPersistence();
        int suppression = kv.getSuppression();
        String persSuppLevel = entityTypeInstance;
        boolean isValid = false;
        if (persistence > 0 && suppression > 0) {
            log.debug("Persistence suppression configuration available at the KPI level for kpi: {}, instance: {}, account: {}", kpiId, instanceIdentifier, accountIdentifier);
            isValid = violationDetails.checkPersistenceSuppressionMatchForSeverity(severityId, persistence, suppression);
            Map<String, Object> result = new HashMap<>();
            result.put("isValid", isValid);
            result.put("persistence", persistence);
            result.put("suppression", suppression);
            result.put("persSuppLevel", persSuppLevel);
            return result;
        }

        persSuppLevel = entityTypeService;
        String serviceId = serviceIdentifier == null ? serviceIds.get(0) : serviceIdentifier;
        Map<String, Integer> persSupp = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                entityType,
                accountIdentifier,
                instanceIdentifier,
                kpiId,
                serviceId,
                violationDetails.getHighestViolatedSeverity());
        if (persSupp == null) {
            Map<String, Object> result = new HashMap<>();
            result.put("isValid", false);
            result.put("persistence", 0);
            result.put("suppression", 0);
            result.put("persSuppLevel", persSuppLevel);
            return result;
        }

        if (!persSupp.containsKey("persistence") || !persSupp.containsKey("suppression")) {
            Map<String, Object> result = new HashMap<>();
            result.put("isValid", false);
            result.put("persistence", 0);
            result.put("suppression", 0);
            result.put("persSuppLevel", persSuppLevel);
            return result;
        }

        persistence = persSupp.get("persistence");
        suppression = persSupp.get("suppression");
        isValid = violationDetails.checkPersistenceSuppressionMatchForSeverity(severityId, persistence, suppression);
        Map<String, Object> result = new HashMap<>();
        result.put("isValid", isValid);
        result.put("persistence", persistence);
        result.put("suppression", suppression);
        result.put("persSuppLevel", persSuppLevel);
        return result;
    }

    // Helper: Handle reset and anomaly close
    private ViolationDetails handleResetAndCloseAnomaly(ViolationDetails violationDetails, String accountIdentifier, String entityIdentifier, String kpiId, String kpiAttributeName,
                                            String violationFor, String entityType, String reason, List<String> appIds) {
        if (violationDetails.getAnomalyEventStatus() != null && violationDetails.getAnomalyEventStatus().getAnomalyEventId() != null) {
            redisUtilities.deleteViolationDetails(accountIdentifier, entityIdentifier, entityType, kpiId, kpiAttributeName, violationFor);
            AnomalySummaryProtos.AnomalySummary anomalySummary = AnomalySummaryProtos.AnomalySummary.newBuilder()
                    .setAnomalyId(violationDetails.getAnomalyEventStatus().getAnomalyEventId())
                    .setAccountIdentifier(accountIdentifier)
                    .setAnomalyStatus(violationDetails.getAnomalyEventStatus().getAnomalyStatus())
                    .setClosingReason(reason)
                    .setKpiId(kpiId)
                    .setViolationFor(violationFor)
                    .setEntityType(entityType)
                    .setEntityIdentifier(entityIdentifier)
                    .setKpiAttribute(kpiAttributeName)
                    .addAllAppId(appIds)
                    .build();

            AnomalyAccountPojo anomalyAccountPojo = anomalyManagementService.closeAnomaly(anomalySummary);
            anomalyEventsProcess.insertAnomaliesAndAlertsOpenSearchRMQ(List.of(anomalyAccountPojo), false);
            return null; // Return null to indicate anomaly has been closed and violation details reset
        } else {
            violationDetails.resetViolationCounts();
            redisUtilities.putViolationDetails(accountIdentifier, entityIdentifier, entityType, kpiId, kpiAttributeName,
                    violationFor, violationDetails);
            return violationDetails; // Return updated violationDetails after reset
        }
    }

    /**
     * Filters the violated data list to return only those with the highest
     * severity.
     * If no violations are found, it checks if an anomaly event exists and updates
     * the closing window count.
     * If the closing window count matches the configured closing window, it closes
     * the anomaly.
     *
     * @param accountIdentifier   String representing the account identifier.
     * @param entityIdentifier    String representing the entity identifier (instance or transaction).
     * @param kpiId               String representing the KPI identifier.
     * @param serviceIds          List of service identifiers associated with the KPI.
     * @param violatedDataList    List of ViolatedData objects to filter for highest severity.
     * @param kpiAttributeName    String representing the KPI attribute name (e.g., CPU, Memory).
     * @param serviceIdentifier   String representing the specific service identifier, if applicable.
     * @param eventTimeInEpochSec long representing the event time in epoch seconds.
     * @param violationFor        String representing the context of the violation (e.g., "KPI", "Transaction").
     * @param appIds              List of application identifiers associated with the KPI violation.
     * @param entityType          String representing the entity type (INSTANCE, TRANSACTION, etc).
     * @return List of ViolatedData with the highest severity or empty if no violations
     * found.
     */
    public ViolatedData getViolatedDataForHighestSeverity(String accountIdentifier, String entityIdentifier,
                                                                String kpiId, List<String> serviceIds, List<ViolatedData> violatedDataList, String kpiAttributeName,
                                                                String serviceIdentifier, long eventTimeInEpochSec, String violationFor, List<String> appIds, String entityType,
                                                                ViolationDetails violationDetails) {

        if (violationDetails == null) {
            return null;
        }

        if (!violatedDataList.isEmpty()) {
            violationDetails.setHighestViolatedSeverity(null);
            violationDetails.setSeveritiesEnabled(new HashSet<>());

            // Set highest violated severity based on available violation counts
            List<String> severityLevels = Arrays.asList(lowSeverityIdSignal, mediumSeverityIdSignal, highSeverityIdSignal);
            for (String severityId : severityLevels) {
                if (hasSeverityViolation(violationDetails, severityId)) {
                    PersistenceSuppressionStatus status = persistenceSuppression.persistenceSuppressionMetForSeverity(violationDetails, severityId);
                    switch (status) {
                        case PERSISTENCE:
                            violationDetails.updatePersistenceMeetCountForSeverity(severityId);
                            break;
                        case SUPPRESSION:
                            violationDetails.updateSuppressionMeetCountForSeverity(severityId);
                            break;
                        case CONTINUE:
                            violationDetails.getSeveritiesEnabled().add(severityId);
                            // Only set if not already set to a higher severity
                            if (violationDetails.getHighestViolatedSeverity() == null ||
                                    severityLevels.indexOf(severityId) > severityLevels.indexOf(violationDetails.getHighestViolatedSeverity())) {
                                violationDetails.setHighestViolatedSeverity(severityId);
                            }
                            break;
                        default:
                            // Do nothing for ERROR or other statuses
                            break;
                    }
                }
            }
            if (violationDetails.getHighestViolatedSeverity() == null) {
                log.error("No valid violation severity found for KPI {} mapped to instance {} and account {}. Hence dropping the data point.", kpiId, entityIdentifier, accountIdentifier);
            }
            // Filter to only highest severity
            ViolatedData highestViolatedData =  violatedDataList.stream()
                    .filter(vd -> vd.getThresholdSeverity().equals(violationDetails.getHighestViolatedSeverity()))
                    .peek(vd -> vd.setViolationDetails(violationDetails))
                    .findFirst().orElse(null);
            if (highestViolatedData == null) {
                redisUtilities.putViolationDetails(accountIdentifier, entityIdentifier, entityType, kpiId, kpiAttributeName,
                        violationFor, violationDetails);
            }
            return highestViolatedData;
        }

        // No violations, but violationDetails exists
        if (violationDetails.getAnomalyEventStatus() != null
                && violationDetails.getAnomalyEventStatus().getAnomalyEventId() != null) {
            violationDetails.updateClosingWindowCount(eventTimeInEpochSec);
            String serviceId = serviceIdentifier == null ? serviceIds.get(0) : serviceIdentifier;
            ServiceConfiguration serviceConfiguration = persistenceSuppression.getServiceConfiguration(accountIdentifier, serviceId);
            if (serviceConfiguration == null) {
                log.warn("Unable to update ClosingWindowCount as Persistence suppression conf unavailable for account {}, instance {}, " +
                                "kpiId {}, and services {}", accountIdentifier, entityIdentifier, kpiId, serviceId);
                return null;
            }

            if (violationDetails.getClosingWindowCount() == serviceConfiguration.getClosingWindow()) {
                handleResetAndCloseAnomaly(violationDetails, accountIdentifier, entityIdentifier, kpiId, kpiAttributeName, violationFor, entityType,
                        AnomalyClosureReason.CLOSING_WINDOW_COUNT_REACHED.getDescription(), appIds);
            } else {
                redisUtilities.putViolationDetails(accountIdentifier, entityIdentifier, entityType, kpiId, kpiAttributeName,
                        violationFor, violationDetails);
            }
        } else {
            redisUtilities.deleteViolationDetails(accountIdentifier, entityIdentifier, entityType, kpiId,
                    kpiAttributeName, violationFor);
        }
        return null;
    }

    // Helper: Check if a severity has a violation count > 0
    private boolean hasSeverityViolation(ViolationDetails violationDetails, String severityId) {
        return violationDetails.getViolationStatusMap().containsKey(severityId)
                && violationDetails.getViolationStatusMap().get(severityId).getViolationCount() > 0;
    }

    /**
     * This method checks the violations for the given violated data and returns a list of ViolatedData
     * with the highest severity based on the configured thresholds.
     *
     * @param violatedData The violated data to check for violations.
     * @param violationDetails The violation details to use and update.
     * @param entityType The type of entity (e.g., INSTANCE, TRANSACTION, SERVICE).
     * @return List of ViolatedData with the highest severity.
     */
    public ViolatedData checkViolationsGetHighestSeverity(ViolatedData violatedData, ViolationDetails violationDetails, String entityType) {
        // Fetch violation configs based on event type and violation level
        Map.Entry<String, List<KpiViolationConfig>> configEntry = getViolationConfigsForViolatedData(violatedData);
        if (configEntry == null) return null;
        String kpiAttributeNameUsed = configEntry.getKey();
        List<KpiViolationConfig> violationConfigList = configEntry.getValue();
        if (violationConfigList == null) return null;
        String entityIdentifier = entityType.equalsIgnoreCase(entityTypeInstance) ? violatedData.getInstanceId() : violatedData.getTransactionId();

        final ViolationDetails[] latestViolationDetails = {violationDetails};
        List<ViolatedData> violatedDataList = violationConfigList.stream()
                .filter(kv -> isValidViolationConfig(kv, violatedData))
                .map(kv -> {
                    Map<String, Double> thresholds = new HashMap<>();
                    thresholds.put(Constants.LOWER_THRESHOLD, kv.getMinThreshold());
                    thresholds.put(Constants.UPPER_THRESHOLD, kv.getMaxThreshold());

                    ViolatedData violatedDataNew = mapKpiViolationConfigToViolatedData(kv, thresholds, violatedData, entityType, latestViolationDetails[0]);
                    if (violatedDataNew != null) {

                        latestViolationDetails[0] = handleViolationDetails(violatedDataNew.getAccountId(),
                                entityIdentifier, violatedDataNew.getKpiId(),
                                violatedDataNew.getServiceList(), kv, thresholds,
                                violatedDataNew.getMetaData().get("serviceIdentifier"),
                                violatedDataNew.getMetaData().get("violationLevel"), OperationType.fromString(violatedDataNew.getOperationType()),
                                violatedDataNew.getKpiAttribute(), kpiAttributeNameUsed, violatedDataNew.getViolationTime(),
                                violatedData.getViolationFor(), violatedData.getAppIds(), entityType, latestViolationDetails[0]);
                        return violatedDataNew;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        violationDetails = latestViolationDetails[0];

        // Only keep highest severity
        return getViolatedDataForHighestSeverity(
                violatedData.getAccountId(),
                entityIdentifier,
                violatedData.getKpiId(),
                violatedData.getServiceList(),
                violatedDataList,
                violatedData.getKpiAttribute(),
                violatedData.getMetaData().get("serviceIdentifier"),
                violatedData.getViolationTime(),
                violatedData.getViolationFor(),
                violatedData.getAppIds(),
                entityType,
                violationDetails
        );
    }

    // Helper: Fetch violation configs for ViolatedData
    private Map.Entry<String, List<KpiViolationConfig>> getViolationConfigsForViolatedData(ViolatedData violatedData) {
        String kpiAttributeNameUsed = Constants.COMMON_ATTRIBUTE;
        try {
            if (violatedData.getEventType().equals(ViolationEventType.KPI_VIOLATION)) {
                if (violatedData.getMetaData().get("violationLevel").equals(entityTypeInstance)) {
                    CompInstKpiEntity kpiDetails = cacheWrapper.getInstanceKPIDetails(violatedData.getAccountId(),
                            violatedData.getInstanceId(), Integer.parseInt(violatedData.getKpiId()));
                    if (kpiDetails == null) {
                        log.error("Instance KPI details not found for accountId: {}, instanceId: {}, kpiId: {}",
                                violatedData.getAccountId(), violatedData.getInstanceId(), violatedData.getKpiId());
                        return null;
                    }
                    if (kpiDetails.getKpiViolationConfig().containsKey(violatedData.getKpiAttribute())) {
                        kpiAttributeNameUsed = violatedData.getKpiAttribute();
                    }
                    List<KpiViolationConfig> configs = kpiDetails.getKpiViolationConfig().get(kpiAttributeNameUsed);
                    return new AbstractMap.SimpleEntry<>(kpiAttributeNameUsed, configs);
                } else if ((violatedData.getMetaData().get("violationLevel").equals(entityTypeService))
                        && violatedData.getMetaData().get("serviceIdentifier") != null) {
                    KpiDetails serviceKpiDetails = cacheWrapper.getServiceKPIDetails(violatedData.getAccountId(),
                            violatedData.getMetaData().get("serviceIdentifier"),
                            Integer.parseInt(violatedData.getKpiId()));
                    if (serviceKpiDetails == null) {
                        log.error("Service KPI details not found for accountId: {}, serviceIdentifier: {}, kpiId: {}",
                                violatedData.getAccountId(), violatedData.getMetaData().get("serviceIdentifier"),
                                violatedData.getKpiId());
                        return null;
                    }
                    if (serviceKpiDetails.getKpiViolationConfig().containsKey(violatedData.getKpiAttribute())) {
                        kpiAttributeNameUsed = violatedData.getKpiAttribute();
                    }
                    List<KpiViolationConfig> configs = serviceKpiDetails.getKpiViolationConfig().get(kpiAttributeNameUsed);
                    return new AbstractMap.SimpleEntry<>(kpiAttributeNameUsed, configs);
                } else {
                    log.debug("KPI violation level is not INSTANCE or SERVICE. Hence dropping the data point for accountId: {}, instanceId: {}, kpiId: {}",
                            violatedData.getAccountId(), violatedData.getInstanceId(), violatedData.getKpiId());
                    return null;
                }
            } else if (violatedData.getEventType().equals(ViolationEventType.TXN_VIOLATION)) {
                ComponentKpiEntity componentKPIDetails = cacheWrapper.getComponentKPIDetails(
                        violatedData.getAccountId(), transactionComponentIdentifier, violatedData.getKpiId());
                if (componentKPIDetails == null) {
                    log.error("Transaction KPI details not found for accountId: {}, kpiId: {}",
                            violatedData.getAccountId(), violatedData.getKpiId());
                    return null;
                }
                List<TxnKPIViolationConfig> txnKPIViolationConfigList = cacheWrapper
                        .getTransactionViolationConfigDetails(violatedData.getAccountId(),
                                violatedData.getTransactionId());
                List<KpiViolationConfig> configs = txnKPIViolationConfigList.stream()
                        .map(TxnKPIViolationConfig::getKpiViolationConfig)
                        .flatMap(List::stream)
                        .filter(c -> c.getKpiId() == Integer.parseInt(violatedData.getKpiId()))
                        .collect(Collectors.toList());
                return new AbstractMap.SimpleEntry<>(kpiAttributeNameUsed, configs);
            }
        } catch (Exception e) {
            log.error("Error while finding kpi details : Account:{}, Instance:{}, kpiId:{}",
                    violatedData.getAccountId(), violatedData.getInstanceId(), violatedData.getKpiId(), e);
            return null;
        }
        return null;
    }

    // Helper: Validate a violation config
    private boolean isValidViolationConfig(KpiViolationConfig kv, ViolatedData violatedData) {
        if (kv.getStatus() == 0) {
            log.info("Static violation configuration is disabled for attribute {} of Kpi {} mapped to instance {} and account {}. Dropping the data point.",
                    violatedData.getKpiAttribute(), violatedData.getKpiId(),
                    violatedData.getInstanceId(), violatedData.getAccountId());
            return false;
        }
        if (kv.getMaxThreshold() == null || kv.getMinThreshold() == null || kv.getOperation() == null) {
            log.error("Threshold data not found for attribute {} of Kpi {} mapped to instance {} and account {}. Dropping the data point.",
                    violatedData.getKpiAttribute(), violatedData.getKpiId(),
                    violatedData.getInstanceId(), violatedData.getAccountId());
            return false;
        }
        return true;
    }

    // Helper: Map KpiViolationConfig to ViolatedData (with handleViolationDetails call)
    private ViolatedData mapKpiViolationConfigToViolatedData(KpiViolationConfig kv, Map<String, Double> thresholds, ViolatedData violatedData, String entityType, ViolationDetails violationDetails) {
        ViolatedData violatedDataNew = new ViolatedData(violatedData);

        if (violatedDataNew.getValue() == null) {
            log.error("ViolatedData value is null for Kpi {} - skipping config.",
                    violatedDataNew.getKpiId());
            return null;
        }
        OperationType opType = Utils.applyThreshold(Double.valueOf(violatedDataNew.getValue()),
                kv.getOperation(), thresholds);

        log.debug("Valid violation configuration found for attribute {} of KpiId {}, opType:{} mapped to instance {} and accountId {}",
                violatedDataNew.getKpiAttribute(), violatedDataNew.getKpiId(), opType,
                violatedDataNew.getInstanceId(), violatedDataNew.getAccountId());

        if (Objects.isNull(opType)) {
            // If the operation type is null, it means the value does not violate any thresholds.
            return null;
        }

        violatedDataNew.setThresholdSeverity(String.valueOf(kv.getThresholdSeverityId()));
        violatedDataNew.setOperationType(opType.getOperationType());
        Map<String, Double> orderedThreshold;
        // If the KPI type is Core or Availability, we need to order the thresholds based on the operation type.
        if (violatedDataNew.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core
                || violatedDataNew.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Availability) {
            orderedThreshold = Utils.getThresholdBasedOnOperationType(thresholds, opType.getOperationType());
            if (orderedThreshold == null || orderedThreshold.isEmpty()) {
                violatedDataNew.setThresholds(thresholds);
            } else {
                violatedDataNew.setThresholds(orderedThreshold);
            }
        } else {
            violatedDataNew.setThresholds(thresholds);
        }

        Map<String, String> allMetaData = violatedDataNew.getMetaData();
        if (violatedDataNew.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core) {
            allMetaData.put("anomalyScore", Utils.getAnomalyScore(thresholds, Double.parseDouble(violatedDataNew.getValue())));
        } else if (violatedDataNew.getKpiType() == KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Availability) {
            // TODO: Need values for anomalyScore
            if (violatedDataNew.getThresholdSeverity().equals(highSeverityIdSignal)) {
                allMetaData.put("anomalyScore", "1");
            } else if (violatedDataNew.getThresholdSeverity().equals(mediumSeverityIdSignal)) {
                allMetaData.put("anomalyScore", "0.7");
            } else if (violatedDataNew.getThresholdSeverity().equals(lowSeverityIdSignal)) {
                allMetaData.put("anomalyScore", "0.5");
            }
        }
        violatedDataNew.setMetaData(allMetaData);

        return violatedDataNew;
    }

    private List<ViolatedData> getKpiViolatedData(ViolatedEventProtos.ViolatedEvent violatedEventProto, String timeInGMT, long violatedTimeInEpochSec) {
        String accId = violatedEventProto.getAccountId();

        return violatedEventProto.getKpisList().parallelStream()
                .filter(c -> {
                    if (!instanceValidation(accId, c.getKpiInfo().getInstanceId())
                            || !serviceValidation(accId, c.getKpiInfo().getSvcIdList())) {
                        log.error("Either instance or/and service related details unavailable for violated event proto [{}]",
                                violatedEventProto);
                        return false;
                    }
                    return true;
                })
                .map(kpi -> {
                    BasicKPIStateInfo kpiStateInfo = getKpiBasicInfo(violatedEventProto, kpi);
                    if (kpiStateInfo == null) {
                        log.info("Kpi violation is ignored. Reason: Invalid KpiInfo. KPI details: [{}]", kpi);
                        return null;
                    }
                    if (!kpiStateInfo.getGenerateAnomaly()) {
                        log.info("Kpi violation is ignored. Reason: Generate Anomaly is set to false. KPI details: [{}]", kpi);
                        return null;
                    }

                    List<String> validServices = kpi.getKpiInfo().getSvcIdList();
                    if (!kpiStateInfo.getIsMaintenanceExcluded()) {
                        if (isInstanceMaintenanceWindowOn(kpi.getKpiInfo().getInstanceId(), violatedEventProto.getAccountId(), violatedEventProto.getViolationTmeInGMT())) {
                            log.info("Kpi violation is ignored. Reason: Instance is under maintenance. KPI details: [{}]", kpi);
                            return null;
                        }

                        validServices = getSvsNotUnderMaintenanceWindow(kpi.getKpiInfo().getSvcIdList(), violatedEventProto.getAccountId(), violatedEventProto.getViolationTmeInGMT());
                        if (validServices.isEmpty()) {
                            log.warn("Kpi violation is ignored. Reason: Service(s) is under maintenance. Time:{}, KPI details:[{}]", violatedEventProto.getViolationTmeInGMT(), kpi);
                            return null;
                        }
                    }

                    ViolatedEventProtos.KpiInfo kpiInfo = kpi.getKpiInfo();

                    ViolatedData violatedData = getViolatedData(violatedEventProto, violatedTimeInEpochSec);
                    violatedData.setServiceList(new ArrayList<>(validServices));
                    violatedData.setValue(kpi.getValue());
                    violatedData.setEventType(ViolationEventType.KPI_VIOLATION);
                    violatedData.setKpiId(kpiInfo.getKpiId());
                    violatedData.setInstanceId(kpiInfo.getInstanceId());
                    violatedData.setKpiAttribute(kpiInfo.getKpiAttribute());
                    violatedData.setOperationType(kpiInfo.getOperationType());
                    violatedData.setThresholds(kpiInfo.getThresholdsMap());
                    violatedData.setThresholdSeverity(StringUtils.isEmpty(kpiInfo.getThresholdSeverity()) ? lowSeverityIdSignal : kpiInfo.getThresholdSeverity());
                    violatedData.setKpiType(kpiStateInfo.getKpiType());
                    violatedData.setDisplayAttributeName(kpiStateInfo.getAttributeDisplayName());
                    violatedData.setIsInfo(kpiStateInfo.getIsInfo());
                    violatedData.setMaintenanceExcluded(kpiStateInfo.getIsMaintenanceExcluded());
                    violatedData.setKpiViolationTime(timeInGMT);

                    //TODO Remove when fixed
                    violatedData.setKpiAttribute(violatedData.getKpiAttribute() != null ? violatedData.getKpiAttribute():"ALL");
                    violatedData.setViolationFor(violatedData.getViolationFor() != null ? violatedData.getViolationFor():"SOR");

                    Map<String, String> metaDataMap = new HashMap<>(kpiInfo.getMetaDataMap());
                    if (!StringUtils.isEmpty(kpiInfo.getAgentId())) {
                        metaDataMap.put("agentUid", kpiInfo.getAgentId());
                    }
                    violatedData.setMetaData(metaDataMap);

                    log.debug("KPI violatedData [{}]", violatedData);

                    // Fetch violationDetails for this KPI
                    ViolationDetails violationDetails = redisUtilities.getViolationDetails(
                        violatedData.getAccountId(),
                        violatedData.getInstanceId(),
                        entityTypeInstance,
                        violatedData.getKpiId(),
                        violatedData.getKpiAttribute(),
                        violatedData.getViolationFor()
                    );

                    return checkViolationsGetHighestSeverity(violatedData, violationDetails, entityTypeInstance);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<ViolatedData> getTxnViolatedData(ViolatedEventProtos.ViolatedEvent violatedEventProto, String timeInGMT, long violatedTimeInEpochSec) {
        String accId = violatedEventProto.getAccountId();

        return violatedEventProto.getTransactionsList().parallelStream().filter(c -> {
                    if (!serviceValidation(accId, Collections.singletonList(c.getTxnInfo().getSvcId()))
                            || cacheWrapper.getTransactionDetails(accId, c.getTxnInfo().getTransactionId()) == null) {
                        log.error("Transaction or/and service related details unavailable for violated event proto [{}]",
                                violatedEventProto);
                        return false;
                    }
                    return true;
                }).map(txn -> {
                    BasicKPIStateInfo txnStateInfo = getTxnBasicInfo(violatedEventProto.getAccountId(), txn);
                    if (txnStateInfo == null) {
                        log.info("Transaction kpi violation is ignored. Reason: Invalid transaction kpi info. Transaction kpi details: [{}]", txn);
                        return null;
                    }
                    if (!txnStateInfo.getGenerateAnomaly()) {
                        log.info("Transaction kpi violation is ignored. Reason: Generate Anomaly is set to false. Transaction kpi details: [{}]", txn);
                        return null;
                    }
                    if (!txnStateInfo.getIsMaintenanceExcluded()) {
                        List<String> validServices = getSvsNotUnderMaintenanceWindow(Collections.singletonList(txn.getTxnInfo().getSvcId()),
                                violatedEventProto.getAccountId(), violatedEventProto.getViolationTmeInGMT());
                        if (validServices.isEmpty()) {
                            log.info("Transaction kpi violation is ignored. Reason: Service is under maintenance. Transaction kpi details: [{}]", txn);
                            return null;
                        }
                    }

                    ViolatedEventProtos.TransactionInfo txnInfo = txn.getTxnInfo();

                    ViolatedData violatedData = getViolatedData(violatedEventProto, violatedTimeInEpochSec);
                    violatedData.setKpiViolationTime(timeInGMT);
                    violatedData.setValue(txn.getValue());
                    violatedData.setEventType(ViolationEventType.TXN_VIOLATION);
                    violatedData.setServiceList(Collections.singletonList(txnInfo.getSvcId()));
                    violatedData.setTransactionId(txnInfo.getTransactionId());
                    violatedData.setResponseTimeType(txnInfo.getResponseTimeType().name());
                    violatedData.setKpiId(txnInfo.getKpiId());
                    violatedData.setOperationType(txnInfo.getOperationType());
                    violatedData.setInstanceId(txnInfo.getTransactionId());
                    violatedData.setThresholds(txnInfo.getThresholdsMap());
                    violatedData.setThresholdSeverity(StringUtils.isEmpty(txnInfo.getThresholdSeverity()) ? lowSeverityIdSignal : txnInfo.getThresholdSeverity());
                    violatedData.setKpiType(txnStateInfo.getKpiType());
                    violatedData.setIsInfo(txnStateInfo.getIsInfo());

                    //TODO Remove when fixed
                    violatedData.setKpiAttribute(violatedData.getKpiAttribute() != null ? violatedData.getKpiAttribute():"ALL");
                    violatedData.setViolationFor(violatedData.getViolationFor() != null ? violatedData.getViolationFor():"SOR");

                    Map<String, String> metaDataMap = new HashMap<>(txnInfo.getMetaDataMap());
                    if (!StringUtils.isEmpty(txnInfo.getAgentId())) {
                        metaDataMap.put("agentUid", txnInfo.getAgentId());
                    }
                    violatedData.setMetaData(metaDataMap);

                    log.debug("Transaction violatedData [{}]", violatedData);

                    // Fetch violationDetails for this transaction
                    ViolationDetails violationDetails = redisUtilities.getViolationDetails(
                        violatedData.getAccountId(),
                        violatedData.getTransactionId(),
                        entityTypeTransaction,
                        violatedData.getKpiId(),
                        violatedData.getKpiAttribute(),
                        violatedData.getViolationFor()
                    );

                    return checkViolationsGetHighestSeverity(violatedData, violationDetails, entityTypeTransaction);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<ViolatedData> getBatchJobViolatedData(ViolatedEventProtos.ViolatedEvent violatedEventProto, String timeInGMT, long violatedTimeInEpochSec) {
        List<ViolatedEventProtos.BatchJob> batchJobs = violatedEventProto.getBatchJobList();

        return batchJobs.parallelStream().map(batchJob -> {

                    ComponentKpiEntity basicKpiEntity = redisUtilities.getComponentKPIDetails(globalAccountIdentifier, batchComponentIdentifier, batchJob.getKpiId());
                    if (basicKpiEntity == null) {
                        log.error("Batch job violated will be ignored because the kpi doesn't exists. BatchJob:{}, kpiId:{}", batchJob.getBatchJob(), batchJob.getKpiId());
                        return null;
                    }

                    ViolatedData violatedData = getViolatedData(violatedEventProto, violatedTimeInEpochSec);
                    violatedData.setBatchJob(batchJob.getBatchJob());
                    violatedData.setKpiViolationTime(timeInGMT);
                    violatedData.setKpiId(batchJob.getKpiId());
                    violatedData.setEventType(ViolationEventType.BATCH_JOB_VIOLATION);
                    violatedData.setThresholds(batchJob.getThresholdsMap());
                    violatedData.setValue(batchJob.getValue());
                    violatedData.setOperationType(batchJob.getOperationType());
                    violatedData.setKpiAttribute(Constants.COMMON_ATTRIBUTE);
                    violatedData.setThresholdSeverity(StringUtils.isEmpty(batchJob.getThresholdSeverity()) ? lowSeverityIdSignal : batchJob.getThresholdSeverity());

                    Map<String, String> metaDataMap = new HashMap<>(batchJob.getMetaDataMap());
                    if (!StringUtils.isEmpty(batchJob.getAgentId())) {
                        metaDataMap.put("agentUid", batchJob.getAgentId());
                    }
                    violatedData.setMetaData(metaDataMap);
                    violatedData.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.valueOf(basicKpiEntity.getType()));
                    log.debug("Batch job violatedData [{}]", violatedData);

                    return violatedData;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private boolean instanceValidation(String accId, String instanceId) {
        CompInstClusterDetails compInstClusterDetails = cacheWrapper.getInstanceDetails(accId, instanceId);
        return compInstClusterDetails != null;
    }

    private boolean serviceValidation(String accId, List<String> serviceList) {
        for (String c : serviceList) {
            com.heal.configuration.pojos.Service service = cacheWrapper.getServiceDetails(accId, c);
            if (service == null) {
                log.error("Service details unavailable for service identifier [{}] mapped to account [{}]", c, accId);
                return false;
            }
        }

        return true;
    }

    private BasicKPIStateInfo getKpiBasicInfo(ViolatedEventProtos.ViolatedEvent violatedEventProto, ViolatedEventProtos.Kpi kpi) {

        BasicKPIStateInfo basicKPIStateInfo = new BasicKPIStateInfo();
        String accountIdentifier = violatedEventProto.getAccountId();

        try {
            String instanceIdentifier = kpi.getKpiInfo().getInstanceId();
            int kpiId = Integer.parseInt(kpi.getKpiInfo().getKpiId());

            CompInstKpiEntity instanceKpiDetails = cacheWrapper.getInstanceKPIDetails(accountIdentifier, instanceIdentifier, kpiId);
            if (instanceKpiDetails == null) {
                return null;
            }
            log.trace("Instance Kpi Details found {}", instanceKpiDetails);

            boolean generateAnomaly = getGenerateAnomalyStatus(violatedEventProto, instanceKpiDetails, kpi);
            log.trace("Generate anomaly is set to {}", generateAnomaly);

            basicKPIStateInfo.setIsMaintenanceExcluded(instanceKpiDetails.getIsMaintenanceExcluded() == 1);
            basicKPIStateInfo.setIsInfo(instanceKpiDetails.getIsInfo());
            basicKPIStateInfo.setGenerateAnomaly(generateAnomaly);

            if (instanceKpiDetails.getIsGroup()) {
                if (instanceKpiDetails.getAttributeValues() != null) {
                    basicKPIStateInfo.setAttributeDisplayName(instanceKpiDetails.getAttributeValues().getOrDefault(kpi.getKpiInfo().getKpiAttribute(),
                            kpi.getKpiInfo().getKpiAttribute()));
                }
            }

            if (instanceKpiDetails.getType() == null || instanceKpiDetails.getType().trim().isEmpty()) {
                log.error("KPI type unavailable for KPI [{}] for instance [{}]", instanceKpiDetails.getId(), instanceIdentifier);
                return null;
            } else {
                basicKPIStateInfo.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.valueOf(instanceKpiDetails.getType()));
            }

        } catch (Exception e) {
            log.error("Error while finding basic kpi state details : ", e);
            return null;
        }

        return basicKPIStateInfo;
    }

    public boolean getGenerateAnomalyStatus(ViolatedEventProtos.ViolatedEvent violatedEventProto,
                                            CompInstKpiEntity instanceKpiDetails,
                                            ViolatedEventProtos.Kpi kpi) {

        if (violatedEventProto.getThresholdType().equalsIgnoreCase("Realtime")) {
            log.trace("Realtime data found. Setting generate anomaly flag to true.");
            return true;
        }

        if (instanceKpiDetails.getKpiViolationConfig() == null || instanceKpiDetails.getKpiViolationConfig().isEmpty()
                || !instanceKpiDetails.getKpiViolationConfig().containsKey(kpi.getKpiInfo().getKpiAttribute()) ||
                instanceKpiDetails.getKpiViolationConfig().get(kpi.getKpiInfo().getKpiAttribute()) == null) {
            log.trace("No instance kpi violation config found. Checking generate anomaly flag at service kpi violation config.");
            for (String svcIdentifier : kpi.getKpiInfo().getSvcIdList()) {
                KpiDetails serviceKPIDetails = cacheWrapper.getServiceKPIDetails(violatedEventProto.getAccountId(), svcIdentifier, Integer.parseInt(kpi.getKpiInfo().getKpiId()));
                if (serviceKPIDetails == null) {
                    log.trace("No Service kpi details found for account {}, service {}, kpi {}", violatedEventProto.getAccountId(), svcIdentifier, kpi.getKpiInfo().getKpiId());
                    continue;
                }
                log.trace("Service kpi details found for account {}, service {}, kpi {} :-  {}",
                        violatedEventProto.getAccountId(), svcIdentifier, kpi.getKpiInfo().getKpiId(), serviceKPIDetails);

                if (serviceKPIDetails.getKpiViolationConfig() != null && !serviceKPIDetails.getKpiViolationConfig().isEmpty()) {

                    KpiViolationConfig kpiViolationConfig = serviceKPIDetails.getKpiViolationConfig().get("ALL")
                            .stream()
                            .findAny().orElse(null);

                    if (kpiViolationConfig != null) {
                        return kpiViolationConfig.getGenerateAnomaly() == 1;
                    }
                }
            }
        } else {
            log.trace("Instance kpi violation config found. Checking for generate anomaly flag.");
            return instanceKpiDetails.getKpiViolationConfig().get(kpi.getKpiInfo().getKpiAttribute()).get(0).getGenerateAnomaly() == 1;
        }

        return false;
    }

    private BasicKPIStateInfo getTxnBasicInfo(String accountIdentifier, ViolatedEventProtos.Transaction txn) {
        BasicKPIStateInfo basicKPIStateInfo = new BasicKPIStateInfo();

        try {
            ComponentKpiEntity kpiDetails = cacheWrapper.getComponentKPIDetails(accountIdentifier, transactionComponentIdentifier, txn.getTxnInfo().getKpiId());
            if (kpiDetails == null) {
                return null;
            }

            basicKPIStateInfo.setIsInfo(kpiDetails.getIsInfo());

            if (kpiDetails.getType() == null || kpiDetails.getType().trim().isEmpty()) {
                log.error("KPI type unavailable for KPI [{}] for transaction [{}]", kpiDetails.getId(), txn.getTxnInfo().getTransactionId());
                return null;
            } else {
                basicKPIStateInfo.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.valueOf(kpiDetails.getType()));
            }

            List<TxnKPIViolationConfig> txnKPIViolationConfigList = cacheWrapper.getTransactionViolationConfigDetails(accountIdentifier, txn.getTxnInfo().getTransactionId());
            Map<Integer, KpiViolationConfig> kpiViolationConfigMap = txnKPIViolationConfigList.parallelStream()
                    .map(TxnKPIViolationConfig::getKpiViolationConfig)
                    .flatMap(Collection::parallelStream)
                    .filter(c -> c.getKpiId() == Integer.parseInt(txn.getTxnInfo().getKpiId()))
                    .collect(Collectors.toMap(KpiViolationConfig::getKpiId, c -> c));

            basicKPIStateInfo.setGenerateAnomaly(kpiViolationConfigMap.isEmpty() || kpiViolationConfigMap.get(Integer.parseInt(txn.getTxnInfo().getKpiId())).getGenerateAnomaly() == 1);
            basicKPIStateInfo.setIsMaintenanceExcluded(!kpiViolationConfigMap.isEmpty() && kpiViolationConfigMap.get(Integer.parseInt(txn.getTxnInfo().getKpiId())).getExcludeMaintenance() == 1);
        } catch (Exception e) {
            log.error("Error while finding basic transaction state details : ", e);
            return null;
        }

        return basicKPIStateInfo;
    }

    public boolean isInstanceMaintenanceWindowOn(String instanceIdentifier, String accountId, String timeInGMT) {
        List<MaintenanceDetails> maintenanceDetailsList = redisUtilities.getInstanceMaintenanceDetails(accountId, instanceIdentifier);

        if (maintenanceDetailsList.isEmpty()) {
            return false;
        }

        if (utils.isUnderMaintenance(timeInGMT, maintenanceDetailsList, 0)) {
            log.warn("Instance [{}] mapped to accountId [{}] is under maintenance.", instanceIdentifier, accountId);
            return true;
        }

        return false;
    }

    public List<String> getSvsNotUnderMaintenanceWindow(List<String> serviceIds, String accountId, String timeInGMT) {
        return serviceIds.stream().map(srv -> {
            List<MaintenanceDetails> maintenanceDetailsList = redisUtilities.getServiceMaintenanceDetails(accountId, srv);

            if (maintenanceDetailsList.isEmpty()) {
                return srv;
            }

            if (utils.isUnderMaintenance(timeInGMT, maintenanceDetailsList, 0)) {
                log.debug("ServiceId:{} mapped to accountId:{} is under maintenance at timeInGMT:{}", srv, accountId, timeInGMT);
                return null;
            }
            return srv;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private ViolatedData getViolatedData(ViolatedEventProtos.ViolatedEvent violatedEventProto,
                                         long violatedTimeInEpochSec) {

        String accountIdentifier = violatedEventProto.getAccountId();
        List<String> appIds = violatedEventProto.getAppIdList();
        int timeZoneOffset = violatedEventProto.getTimezoneOffsetInSeconds();
        String thresholdType = violatedEventProto.getThresholdType();

        ViolatedData violatedData = new ViolatedData(accountIdentifier, appIds);
        violatedData.setThresholdType(thresholdType);
        violatedData.setViolationFor(thresholdType.equalsIgnoreCase("Static") ? "SOR" : "NOR");
        violatedData.setTimezoneOffsetInSeconds(timeZoneOffset);
        violatedData.setViolationTime(violatedTimeInEpochSec * 1000);

        return violatedData;
    }

}
