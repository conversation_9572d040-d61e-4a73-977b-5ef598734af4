package com.heal.event.detector.core;

import com.heal.configuration.protbuf.AnomalySummaryProtos;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.pojos.GenericValidationObject;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.StringUtils;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service class for processing and validating anomaly close data.
 * Follows the same pattern as ThresholdDataProcess for consistency.
 */
@Slf4j
@Service
public class AnomalyCloseDataProcess {

    @Autowired
    private HealthMetrics metrics;

    @Autowired
    private CacheWrapper cacheWrapper;

    @Autowired
    private AnomalyManagementService anomalyManagementService;

    @Autowired
    private AnomalyEventsProcess anomalyEventsProcess;

    /**
     * Processes and sinks anomaly close data after validation.
     * 
     * @param anomalySummary the validated anomaly summary to process
     */
    public void processAndSinkAnomalyCloseData(AnomalySummaryProtos.AnomalySummary anomalySummary) {
        try {
            log.info("Processing anomaly closure for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}, closingReason: {}", 
                    anomalySummary.getAnomalyId(), anomalySummary.getAccountIdentifier(), anomalySummary.getEntityIdentifier(), 
                    anomalySummary.getKpiId(), anomalySummary.getKpiAttribute(), anomalySummary.getClosingReason());

            // Close the anomaly using AnomalyManagementService
            AnomalyAccountPojo anomalyAccountPojo = anomalyManagementService.closeAnomaly(anomalySummary);
            
            if (anomalyAccountPojo != null) {
                // Insert the closed anomaly into OpenSearch and forward to RabbitMQ
                anomalyEventsProcess.insertAnomaliesAndAlertsOpenSearchRMQ(List.of(anomalyAccountPojo), false);
                
                log.info("Successfully processed anomaly closure for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}", 
                        anomalySummary.getAnomalyId(), anomalySummary.getAccountIdentifier(), anomalySummary.getEntityIdentifier(), 
                        anomalySummary.getKpiId(), anomalySummary.getKpiAttribute());
                
                // Update success metrics
                metrics.updateAnomalyCloseProcessingSuccess();
                
            } else {
                log.warn("Anomaly closure returned null result for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}", 
                        anomalySummary.getAnomalyId(), anomalySummary.getAccountIdentifier(), anomalySummary.getEntityIdentifier(), 
                        anomalySummary.getKpiId(), anomalySummary.getKpiAttribute());
                
                // Update metrics for null result
                metrics.updateAnomalyCloseProcessingErrors();
            }

        } catch (Exception e) {
            log.error("Error processing anomaly closure for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}", 
                    anomalySummary.getAnomalyId(), anomalySummary.getAccountIdentifier(), anomalySummary.getEntityIdentifier(), 
                    anomalySummary.getKpiId(), anomalySummary.getKpiAttribute(), e);
            
            // Update error metrics
            metrics.updateAnomalyCloseProcessingErrors();
            throw e; // Re-throw to be handled by the calling method
        }
    }

    /**
     * Validates anomaly close data (AnomalySummary) for required fields and business rules.
     * Follows the same validation pattern as ThresholdDataProcess.validateNorThreshold().
     * 
     * @param anomalySummary the anomaly summary to validate
     * @return GenericValidationObject containing validation result
     */
    public GenericValidationObject<AnomalySummaryProtos.AnomalySummary> validateAnomalyCloseData(AnomalySummaryProtos.AnomalySummary anomalySummary) {
        GenericValidationObject<AnomalySummaryProtos.AnomalySummary> result = GenericValidationObject.<AnomalySummaryProtos.AnomalySummary>builder()
                .isValid(false)
                .proto(anomalySummary)
                .build();

        try {
            // Check if anomalySummary is null
            if (anomalySummary == null) {
                log.error("Validation of anomaly close data failed. Reason: Data received is null. " +
                        "Hence it will not be processed further.");
                metrics.updateAnomalyCloseProcessingErrors();
                return result;
            }

            // Check if data has content
            if (anomalySummary.getSerializedSize() == 0) {
                log.error("Validation of anomaly close data failed. Reason: Data received is either invalid or undefined. " +
                        "Hence it will not be processed further.");
                metrics.updateAnomalyCloseProcessingErrors();
                return result;
            }

            // Validate account identifier
            if (StringUtils.isEmpty(anomalySummary.getAccountIdentifier())) {
                log.error("Validation of anomaly close data failed. Reason: Account identifier is " +
                        "either invalid or undefined. Hence it will not be processed further.");
                metrics.updateAnomalyCloseProcessingErrors();
                return result;
            }

            // Validate account exists in cache
            if (cacheWrapper.getAccountDetails(anomalySummary.getAccountIdentifier()) == null) {
                log.error("Validation of anomaly close data failed. Reason: Invalid account identifier [{}]. " +
                        "Account not found in cache. Hence it will not be processed further.", 
                        anomalySummary.getAccountIdentifier());
                metrics.updateAnomalyCloseProcessingErrors();
                return result;
            }

            // Validate anomaly ID
            if (StringUtils.isEmpty(anomalySummary.getAnomalyId())) {
                log.error("Validation of anomaly close data failed. Reason: Anomaly ID is " +
                        "either invalid or undefined for account: {}. Hence it will not be processed further.", 
                        anomalySummary.getAccountIdentifier());
                metrics.updateAnomalyCloseProcessingErrors();
                return result;
            }

            // Validate entity identifier
            if (StringUtils.isEmpty(anomalySummary.getEntityIdentifier())) {
                log.error("Validation of anomaly close data failed. Reason: Entity identifier is " +
                        "either invalid or undefined for anomalyId: {}, account: {}. Hence it will not be processed further.", 
                        anomalySummary.getAnomalyId(), anomalySummary.getAccountIdentifier());
                metrics.updateAnomalyCloseProcessingErrors();
                return result;
            }

            // Validate KPI ID
            if (StringUtils.isEmpty(anomalySummary.getKpiId())) {
                log.error("Validation of anomaly close data failed. Reason: KPI ID is " +
                        "either invalid or undefined for anomalyId: {}, account: {}, entityId: {}. Hence it will not be processed further.", 
                        anomalySummary.getAnomalyId(), anomalySummary.getAccountIdentifier(), anomalySummary.getEntityIdentifier());
                metrics.updateAnomalyCloseProcessingErrors();
                return result;
            }

            // Validate KPI attribute
            if (StringUtils.isEmpty(anomalySummary.getKpiAttribute())) {
                log.error("Validation of anomaly close data failed. Reason: KPI attribute is " +
                        "either invalid or undefined for anomalyId: {}, account: {}, entityId: {}, kpiId: {}. Hence it will not be processed further.", 
                        anomalySummary.getAnomalyId(), anomalySummary.getAccountIdentifier(), 
                        anomalySummary.getEntityIdentifier(), anomalySummary.getKpiId());
                metrics.updateAnomalyCloseProcessingErrors();
                return result;
            }

            // Validate violation for
            if (StringUtils.isEmpty(anomalySummary.getViolationFor())) {
                log.error("Validation of anomaly close data failed. Reason: Violation for is " +
                        "either invalid or undefined for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}. Hence it will not be processed further.", 
                        anomalySummary.getAnomalyId(), anomalySummary.getAccountIdentifier(), anomalySummary.getEntityIdentifier(), 
                        anomalySummary.getKpiId(), anomalySummary.getKpiAttribute());
                metrics.updateAnomalyCloseProcessingErrors();
                return result;
            }

            // Validate entity type
            if (StringUtils.isEmpty(anomalySummary.getEntityType())) {
                log.error("Validation of anomaly close data failed. Reason: Entity type is " +
                        "either invalid or undefined for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}. Hence it will not be processed further.", 
                        anomalySummary.getAnomalyId(), anomalySummary.getAccountIdentifier(), anomalySummary.getEntityIdentifier(), 
                        anomalySummary.getKpiId(), anomalySummary.getKpiAttribute());
                metrics.updateAnomalyCloseProcessingErrors();
                return result;
            }

            // Validate closing reason
            if (StringUtils.isEmpty(anomalySummary.getClosingReason())) {
                log.error("Validation of anomaly close data failed. Reason: Closing reason is " +
                        "either invalid or undefined for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}. Hence it will not be processed further.", 
                        anomalySummary.getAnomalyId(), anomalySummary.getAccountIdentifier(), anomalySummary.getEntityIdentifier(), 
                        anomalySummary.getKpiId(), anomalySummary.getKpiAttribute());
                metrics.updateAnomalyCloseProcessingErrors();
                return result;
            }

            log.debug("Anomaly close data validation successful for anomalyId: {}, account: {}, entityId: {}, kpiId: {}, kpiAttribute: {}",
                    anomalySummary.getAnomalyId(), anomalySummary.getAccountIdentifier(), anomalySummary.getEntityIdentifier(), 
                    anomalySummary.getKpiId(), anomalySummary.getKpiAttribute());

            // All validations passed
            result = GenericValidationObject.<AnomalySummaryProtos.AnomalySummary>builder()
                    .isValid(true)
                    .proto(anomalySummary)
                    .build();

            return result;

        } catch (Exception e) {
            log.error("Exception during anomaly close data validation for anomalyId: {}, account: {}. " +
                    "Hence it will not be processed further.", 
                    anomalySummary != null ? anomalySummary.getAnomalyId() : "unknown",
                    anomalySummary != null ? anomalySummary.getAccountIdentifier() : "unknown", e);
            metrics.updateAnomalyCloseProcessingErrors();
            return result;
        }
    }
}
