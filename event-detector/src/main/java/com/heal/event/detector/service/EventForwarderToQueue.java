package com.heal.event.detector.service;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.cache.CacheWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class EventForwarderToQueue {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Qualifier("anomalyOutputSignalQueue")
    @Autowired
    private Queue anomalyOutputSignalQueueName;

    @Qualifier("anomalyOutputActionQueue")
    @Autowired
    private Queue anomalyOutputActionQueueName;

    @Qualifier("anomalyOutputMLESignalQueue")
    @Autowired
    private Queue anomalyOutputMLESignalQueueName;

    @Qualifier("anomalyMessagesQueue")
    @Autowired
    private Queue anomalyMessagesQueueName;

    @Autowired
    private HealthMetrics metrics;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    CacheWrapper cacheWrapper;

    public void sendAnomalyOutputToActionQueue(List<AnomalyEventProtos.AnomalyEvent> anomalyEvents) {
        log.trace("Sending Anomaly Event data to queue [{}]. Data:-{}", anomalyOutputActionQueueName.getName(),
                anomalyEvents);

        anomalyEvents.forEach(c -> {
            try {
                if (!c.hasBatchInfo() && !c.getKpis().getIsWorkload()) {
                    CompInstClusterDetails instanceDetail = cacheWrapper.getInstanceDetails(c.getAccountId(), c.getKpis().getInstanceId());

                    if (instanceDetail != null && instanceDetail.getForensicAgentTypeId() == Constants.COMPONENT_AGENT_TYPE_ID) {
                        log.trace("forensic_agent_type_id field in instance details for the instance {} is component-agent type id. Hence skipping this object from being pushed in to action messages queue ", c.getKpis().getInstanceId());
                        return;
                    }

                }
                rabbitTemplate.convertAndSend(anomalyOutputActionQueueName.getName(), c.toByteArray());
                metrics.updateWriteCountAnomalyActionQueue(1);
                metrics.updateSnapshots(anomalyOutputActionQueueName.getName(), 1);
            } catch (Exception e) {
                log.error("Error occurred while adding the anomalies into queue." +
                                " Queue name:{}, size:{}", anomalyOutputActionQueueName.getName(),
                        anomalyEvents.size(), e);
                metrics.updateRmqWriteErrors(1);
            }
        });
    }

    public void sendAnomalyOutputToSignalQueue(AnomalyEventProtos.AnomalyEvent anomalyEvent) {
        log.trace("Sending Anomaly Event data to queue [{}]. Data:-{}", anomalyOutputSignalQueueName.getName(),
                anomalyEvent);
        try {
            rabbitTemplate.convertAndSend(anomalyOutputSignalQueueName.getName(), anomalyEvent.toByteArray());
            metrics.updateWriteCountAnomalySignalQueue(1);
            metrics.updateSnapshots(anomalyOutputSignalQueueName.getName(), 1);
        } catch (Exception e) {
            log.error("Error occurred while adding the anomalies into queue. Queue name:{}, AnomalyId:{}",
                    anomalyOutputSignalQueueName.getName(),
                    anomalyEvent.getAnomalyId(), e);
            metrics.updateRmqWriteErrors(1);
        }
    }

    public void sendAnomalyOutputToMLESignalQueue(AnomalyEventProtos.AnomalyEvent anomalyEvents) {
        log.trace("Sending Anomaly Event data to queue [{}]. Data:-{}", anomalyOutputMLESignalQueueName.getName(), anomalyEvents);


        try {
            rabbitTemplate.convertAndSend(anomalyOutputMLESignalQueueName.getName(), anomalyEvents.toByteArray());
            metrics.updateWriteCountAnomalySignalQueue(1);
            metrics.updateSnapshots(anomalyOutputMLESignalQueueName.getName(), 1);
        } catch (Exception e) {
            log.error("Error occurred while adding the anomalies into queue. Queue name:{}, anomalyId:{}",
                    anomalyOutputMLESignalQueueName.getName(),
                    anomalyEvents.getAnomalyId(), e);
            metrics.updateRmqWriteErrors(1);
        }

    }

    public void sendAnomalyMessagesQueue(AnomalyEventProtos.AnomalyEvent anomalyEvents) {
        log.trace("Sending Event data to queue [{}]. Data:-{}", anomalyMessagesQueueName.getName(), anomalyEvents);

        try {
            rabbitTemplate.convertAndSend(anomalyMessagesQueueName.getName(), anomalyEvents.toByteArray());
            metrics.updateWriteCountAnomalyMessagesQueue(1);
            metrics.updateSnapshots(anomalyMessagesQueueName.getName(), 1);
        } catch (Exception e) {
            log.error("Error occurred while adding the event data into queue. Queue name:{}, anomalyId:{}",
                    anomalyMessagesQueueName.getName(),
                    anomalyEvents.getAnomalyId(), e);
            metrics.updateRmqWriteErrors(1);
        }
    }

}