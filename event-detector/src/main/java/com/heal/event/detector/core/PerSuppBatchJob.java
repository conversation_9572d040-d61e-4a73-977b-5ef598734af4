package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.event.detector.pojos.PersistenceSuppressionPojo;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.utility.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PerSuppBatchJob {

    @Autowired
    RedisUtilities redisUtilities;

    @Value("${batch.job.suppression.value:3}")
    private int suppressionVal;

    @Value("${batch.job.collection.interval:60}")
    private int collectionInterval;

    @Value("${signal.severity.id.high:433}")
    String highSeverityIdSignal;

    @Value("${signal.severity.id.medium:432}")
    String mediumSeverityIdSignal;

    @Value("${signal.severity.id.low:431}")
    String lowSeverityIdSignal;


    public AnomalyEventProtos.AnomalyEvent applyPersistenceSupp(ViolatedData value) {
        StringBuilder keyBuilder = new StringBuilder();

        String key = Utils.stringBuilderHelper(keyBuilder, value.getAccountId(), value.getAppIds().get(0),
                value.getBatchJob(), value.getKpiId());

        long currentTimeCollectionTime = value.getViolationTime();

        //TODO RP-2142 - Purpose of this initialization
        PersistenceSuppressionPojo persistenceSuppressionPojo = redisUtilities.getPersistenceSuppressionDetails(key);
        long previousCollectionTime = 0L;
        if (persistenceSuppressionPojo != null) {
            previousCollectionTime = persistenceSuppressionPojo.getLastViolationTime();
        }

        long timeDiff = (currentTimeCollectionTime - previousCollectionTime) / 1000;

        if (timeDiff <= 0) {
            log.error("Invalid time difference found between last message violation time {} and current message violation time {} for account {}," +
                            "application {}, batch job {}, kpi {}. Ignoring the duplicate violation message.",
                    previousCollectionTime, currentTimeCollectionTime, value.getAccountId(), value.getAppIds().get(0), value.getBatchJob(),
                    value.getKpiId());
            return null;
        }

        if (previousCollectionTime == 0 || timeDiff > collectionInterval) {
            return generateAnomaly(key, currentTimeCollectionTime, value);
        }

        persistenceSuppressionPojo = redisUtilities.syncAndGetPersistenceSuppressionDetails(key, currentTimeCollectionTime);
        if (persistenceSuppressionPojo.getViolationCount() <= suppressionVal) {
            log.info("Anomaly will not be created as number of violated events count is [{}], suppression " +
                            "value is [{}] for account [{}], batch job [{}], kpiId [{}]",
                    persistenceSuppressionPojo.getViolationCount(), suppressionVal, value.getAccountId(), value.getBatchJob(), value.getKpiId());

            return null;
        }

        return generateAnomaly(key, currentTimeCollectionTime, value);
    }

    private AnomalyEventProtos.AnomalyEvent generateAnomaly(String key, long currentTimeCollectionTime,
                                                            ViolatedData value) {

        PersistenceSuppressionPojo persistenceSuppressionPojo = redisUtilities.getPersistenceSuppressionDetails(key);
        long anomalyTime = TimeUnit.MINUTES.toMillis(TimeUnit.MILLISECONDS.toMinutes(System.currentTimeMillis()));
        String anomalyIdentifier = "AE-B-" + value.getBatchJob() + "-" + value.getKpiId() + "-" + TimeUnit.MILLISECONDS.toMinutes(value.getViolationTime());
        value.setSuppression(suppressionVal);

        AnomalyEventProtos.AnomalyEvent.Builder anomalyEventBuilder = AnomalyEventProtos.AnomalyEvent.newBuilder();
        anomalyEventBuilder.setAccountId(value.getAccountId());
        anomalyEventBuilder.addAllAppId(new ArrayList<>(value.getAppIds()));
        anomalyEventBuilder.setAnomalyId(anomalyIdentifier);
        anomalyEventBuilder.setThresholdType(value.getThresholdType());
        anomalyEventBuilder.setOperationType(value.getOperationType());
        anomalyEventBuilder.setStartTimeGMT(persistenceSuppressionPojo == null ? currentTimeCollectionTime :
                persistenceSuppressionPojo.getViolationStartTime());
        anomalyEventBuilder.setEndTimeGMT(value.getViolationTime());
        anomalyEventBuilder.setAnomalyTriggerTimeGMT(anomalyTime);

        AnomalyEventProtos.BatchInfo.Builder builder = AnomalyEventProtos.BatchInfo.newBuilder();
        builder.setBatchJob(value.getBatchJob());
        builder.setKpiId(value.getKpiId());
        Map<String, Double> thresholds = new HashMap<>(value.getThresholds());
        builder.putAllThresholds(thresholds);
        builder.setValue(value.getValue());
        builder.setIsWorkload(false);

        if (null != value.getThresholdSeverity()) {
            builder.setThresholdSeverity(value.getThresholdSeverity());
        }

        Map<String, String> metaData = new HashMap<>(value.getMetaData());
        metaData.put("suppression", String.valueOf(value.getSuppression()));
        metaData.put("starttime", String.valueOf(value.getViolationTime()));
        metaData.put("applicationid", value.getAppIds().get(0));
        metaData.put("thresholdseverity", builder.getThresholdSeverity());
        metaData.put("batch_job", value.getBatchJob());
        metaData.putIfAbsent("kpiType", value.getKpiType().name());
        builder.putAllMetadata(metaData);

        anomalyEventBuilder.setBatchInfo(builder.build());
        AnomalyEventProtos.AnomalyEvent event = anomalyEventBuilder.build();

        log.info("Batch job anomaly created: {}", event);

        persistenceSuppressionPojo = PersistenceSuppressionPojo.builder()
                .violationStartTime(currentTimeCollectionTime)
                .lastViolationTime(currentTimeCollectionTime)
                .violationCount(0)
                .build();

        redisUtilities.putPersistenceSuppressionDetails(key, persistenceSuppressionPojo);

        return event;
    }
}
