package com.heal.event.detector.repo;

import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.heal.configuration.pojos.opensearch.*;
import com.heal.configuration.util.DateHelper;
import com.heal.event.detector.config.OpenSearchConfig;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.pojos.DelayThresholdQueue;
import com.heal.event.detector.pojos.IndexDocIdPojo;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.scheduler.OSDataPushScheduler;
import com.heal.event.detector.utility.*;
import jakarta.json.stream.JsonGenerator;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.json.JsonpMapper;
import org.opensearch.client.json.jackson.JacksonJsonpMapper;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.*;
import org.opensearch.client.opensearch._types.query_dsl.BoolQuery;
import org.opensearch.client.opensearch._types.query_dsl.MatchPhraseQuery;
import org.opensearch.client.opensearch._types.query_dsl.Query;
import org.opensearch.client.opensearch._types.query_dsl.TermQuery;
import org.opensearch.client.opensearch.core.*;
import org.opensearch.client.opensearch.core.search.HitsMetadata;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 21-01-2022
 */
@Slf4j
@Repository
public class OpenSearchRepo {

    @Value("${opensearch.kpi.violations.index:heal_kpi_violations}")
    public String rawKpisViolationsIndex;

    @Value("${opensearch.transaction.violations.index:heal_transaction_violations}")
    public String rawTransactionsViolationsIndex;

    @Value("${opensearch.batchjob.violations.index:heal_batch_job_violations}")
    public String rawBatchJobViolationsIndex;

    @Value("${opensearch.anomalies.index:heal_anomalies}")
    public String anomaliesIndex;

    @Value("${opensearch.alerts.index:heal_events}")
    public String alertsIndex;

    @Value("${opensearch.kpi.thresholds.index:heal_instance_kpi_thresholds}")
    public String kpiThresholdsIndex;

    @Value("${opensearch.transaction.thresholds.index:heal_transaction_kpi_thresholds}")
    public String transactionThresholdsIndex;

    @Value("${opensearch.script.update:true}")
    private boolean updateOSByScript;

    @Value("${local.cache.timeout.minute:60}")
    private int cacheTimeout;

    @Value("${opensearch.anomaly.search.retention.days:30}")
    private int anomalySearchRetentionDays;

    @Autowired
    public OSDataPushScheduler scheduler;
    @Autowired
    public ObjectMapper objectMapper;
    @Autowired
    private Gson gson;
    @Autowired
    public OpenSearchConfig openSearchConfig;
    @Autowired
    LocalCache localCache;
    @Autowired
    private HealthMetrics metrics;

    public void insertRawKpiViolations(List<ViolatedData> violatedDataList) {

        if (violatedDataList.isEmpty()) return;

        AtomicReference<String> accountIdentifier = new AtomicReference<>();

        violatedDataList.parallelStream()
                .map(violatedData -> {
                    accountIdentifier.set(violatedData.getAccountId());

                    Map<String, Double> allThresholdsMap = new HashMap<>(violatedData.getThresholds());
                    Map<String, String> metaDataMap = new HashMap<>(violatedData.getMetaData());
                    Map<String, Double> thresholdsMap = new HashMap<>();
                    allThresholdsMap.forEach((key, value) -> {
                        if (key.equalsIgnoreCase("Upper") || key.equalsIgnoreCase("Lower")) {
                            thresholdsMap.put(key, value);
                        } else {
                            metaDataMap.put(key, String.valueOf(value));
                        }
                    });

                    return RawKpiViolations.builder()
                            .instanceId(violatedData.getInstanceId())
                            .kpiId(Long.parseLong(violatedData.getKpiId()))
                            .kpiAttribute(violatedData.getKpiAttribute())
                            .violationTime(violatedData.getViolationTime())
                            .identifiedTime(System.currentTimeMillis())
                            .metadata(metaDataMap)
                            .operationType(violatedData.getOperationType())
                            .thresholdType(violatedData.getThresholdType())
                            .thresholds(thresholdsMap)
                            .value(violatedData.getValue())
                            .timestamp(DateHelper.getDate(violatedData.getViolationTime()))
                            .kpiType(violatedData.getKpiType().name())
                            .build();
                })
                .filter(Objects::nonNull)
                .forEach(m -> {
                    try {
                        List<String> indexDates = DateHelper.getWeeksAsString(m.getViolationTime(), m.getViolationTime());
                        scheduler.addToIndexQueue(new IndexRequest.Builder<>()
                                        .index(rawKpisViolationsIndex + "_" + accountIdentifier.get() + "_" + indexDates.get(0))
                                        .document(m)
                                        .build(),
                                accountIdentifier.get(), rawKpisViolationsIndex);
                    } catch (Exception e) {
                        metrics.updateOpenSearchErrors(1);
                        log.error("Exception while making Raw Kpi Violations data to push into OpenSearch. ", e);
                    }
                });
    }

    public void insertRawTxnViolations(List<ViolatedData> violatedDataList) {

        if (violatedDataList.isEmpty()) return;

        AtomicReference<String> accountIdentifier = new AtomicReference<>();

        violatedDataList.parallelStream()
                .map(violatedData -> {
                    accountIdentifier.set(violatedData.getAccountId());

                    Map<String, Double> allThresholdsMap = new HashMap<>(violatedData.getThresholds());
                    Map<String, String> metaDataMap = new HashMap<>(violatedData.getMetaData());
                    Map<String, Double> thresholdsMap = new HashMap<>();
                    allThresholdsMap.forEach((key, value) -> {
                        if (key.equalsIgnoreCase("Upper") || key.equalsIgnoreCase("Lower")) {
                            thresholdsMap.put(key, value);
                        } else {
                            metaDataMap.put(key, String.valueOf(value));
                        }
                    });

                    return RawTransactionsViolations.builder()
                            .txnId(violatedData.getTransactionId())
                            .kpiId(Long.parseLong(violatedData.getKpiId()))
                            .responseType(violatedData.getResponseTimeType())
                            .violationTime(violatedData.getViolationTime())
                            .identifiedTime(System.currentTimeMillis())
                            .metadata(metaDataMap)
                            .operationType(violatedData.getOperationType())
                            .thresholdType(violatedData.getThresholdType())
                            .thresholds(thresholdsMap)
                            .value(violatedData.getValue())
                            .timestamp(DateHelper.getDate(violatedData.getViolationTime()))
                            .build();
                })
                .filter(Objects::nonNull)
                .forEach(m -> {
                    try {
                        List<String> indexDates = DateHelper.getWeeksAsString(m.getViolationTime(), m.getViolationTime());
                        scheduler.addToIndexQueue(new IndexRequest.Builder<>()
                                        .index(rawTransactionsViolationsIndex + "_" + accountIdentifier.get() + "_" + indexDates.get(0))
                                        .document(m)
                                        .build(),
                                accountIdentifier.get(), rawTransactionsViolationsIndex);
                    } catch (Exception e) {
                        metrics.updateOpenSearchErrors(1);
                        log.error("Exception while making Raw Txn Violations data to push into OpenSearch. ", e);
                    }
                });

    }

    public void insertRawBatchJobViolations(List<ViolatedData> violatedDataList) {

        if (violatedDataList.isEmpty()) return;

        AtomicReference<String> accountIdentifier = new AtomicReference<>();

        violatedDataList.parallelStream()
                .map(violatedData -> {
                    accountIdentifier.set(violatedData.getAccountId());

                    Map<String, Double> allThresholdsMap = new HashMap<>(violatedData.getThresholds());
                    Map<String, String> metaDataMap = new HashMap<>(violatedData.getMetaData());
                    Map<String, Double> thresholdsMap = new HashMap<>();
                    allThresholdsMap.forEach((key, value) -> {
                        if (key.equalsIgnoreCase("Upper") || key.equalsIgnoreCase("Lower")) {
                            thresholdsMap.put(key, value);
                        } else {
                            metaDataMap.put(key, String.valueOf(value));
                        }
                    });

                    return RawBatchJobViolations.builder()
                            .applicationId(violatedData.getAppIds())
                            .kpiId(Long.parseLong(violatedData.getKpiId()))
                            .batchId(violatedData.getBatchJob())
                            .violationTime(violatedData.getViolationTime())
                            .identifiedTime(System.currentTimeMillis())
                            .metadata(metaDataMap)
                            .operationType(violatedData.getOperationType())
                            .thresholdType(violatedData.getThresholdType())
                            .thresholds(thresholdsMap)
                            .value(violatedData.getValue())
                            .timestamp(DateHelper.getDate(violatedData.getViolationTime()))
                            .build();
                })
                .filter(Objects::nonNull)
                .forEach(m -> {
                    try {
                        List<String> indexDates = DateHelper.getWeeksAsString(m.getViolationTime(), m.getViolationTime());
                        scheduler.addToIndexQueue(new IndexRequest.Builder<>()
                                        .index(rawBatchJobViolationsIndex + "_" + accountIdentifier.get() + "_" + indexDates.get(0))
                                        .document(m)
                                        .build(),
                                accountIdentifier.get(), rawBatchJobViolationsIndex);
                    } catch (Exception e) {
                        metrics.updateOpenSearchErrors(1);
                        log.error("Exception while making Raw BatchJob Violations data to push into OpenSearch. ", e);
                    }
                });
    }

    public void insertAnomalies(List<AnomalyAccountPojo> anomaliesAccountList) {

        if (anomaliesAccountList.isEmpty()) return;

        anomaliesAccountList.parallelStream()
                .filter(Objects::nonNull)
                .peek(anomalies -> anomalies.getAnomalyDetails().setTimestamp(DateHelper.getDate(anomalies.getAnomalyDetails().getAnomalyCreatedTime())))
                .forEach(m -> {
                    try {
                        List<String> indexDates = DateHelper.getWeeksAsString(m.getAnomalyDetails().getAnomalyCreatedTime(), m.getAnomalyDetails().getAnomalyCreatedTime());
                        scheduler.addToIndexQueue(new IndexRequest.Builder<>()
                                        .index(anomaliesIndex + "_" + m.getAccountIdentifier() + "_" + indexDates.get(0))
                                        .id(m.getAnomalyDetails().getAnomalyId())
                                        .document(m.getAnomalyDetails())
                                        .build(),
                                m.getAccountIdentifier(), anomaliesIndex);
                    } catch (Exception e) {
                        metrics.updateOpenSearchErrors(1);
                        log.error("Exception while making Anomalies data to push into OpenSearch. ", e);
                    }
                });

    }

    public void processInstanceKpiThreshold(InstanceKpiThresholds instanceKpiThresholds, String accountIdentifier) {
        if (instanceKpiThresholds == null) {
            log.error("NULL instance kpi threshold found. Skipping this data point.");
            return;
        }

        //GET old instance kpi thresholds data in local cache
        String key = accountIdentifier + "#" + instanceKpiThresholds.getServiceIdentifier() + "#" + instanceKpiThresholds.getInstanceId() + "#"
                + instanceKpiThresholds.getKpiId() + "#" + instanceKpiThresholds.getKpiAttribute();
        InstanceKpiThresholds oldInstanceKpiThresholds = localCache.instanceKpiThresholdsConcurrentHashMap.getOrDefault(key, null);

        if (oldInstanceKpiThresholds != null) {
            log.trace("Existing instance kpi threshold details found in local cache for key {}", key);
            if (oldInstanceKpiThresholds.getStartTime() < instanceKpiThresholds.getStartTime()) {
                List<String> indexDates = DateHelper.getWeeksAsString(oldInstanceKpiThresholds.getStartTime(), oldInstanceKpiThresholds.getStartTime());
                String indexName = kpiThresholdsIndex + "_" + accountIdentifier + "_" + indexDates.get(0);
                String docId = oldInstanceKpiThresholds.getInstanceId() + "#" + oldInstanceKpiThresholds.getKpiId() + "#"
                        + oldInstanceKpiThresholds.getKpiAttribute() + "#" + oldInstanceKpiThresholds.getStartTime();

                Map<String, Object> kpiThresholds = new HashMap<>();
                kpiThresholds.put("endTime", instanceKpiThresholds.getStartTime() + "L");

                updateDocument(accountIdentifier, kpiThresholdsIndex, indexName, docId, kpiThresholds);
            } else {
                log.error("New instance kpi thresholds record has older start time that previously processed record. New record start time {}, Old record startTime {}", instanceKpiThresholds.getStartTime(), oldInstanceKpiThresholds.getStartTime());
                metrics.updateOldNorThresholds();
                return;
            }
        } else {
            IndexDocIdPojo idPojo;
            try {
                idPojo = checkExistingInstanceKpiThreshold(accountIdentifier, instanceKpiThresholds);
            } catch (IOException e) {
                metrics.updateOpenSearchErrors(1);
                log.error("Exception while getting Existing Kpis Thresholds document id.", e);
                return;
            }

            if (idPojo != null && idPojo.getIndexName() != null && idPojo.getDocId() != null && idPojo.getSource() != null) {
                try {
                    oldInstanceKpiThresholds = objectMapper.readValue(idPojo.getSource(), InstanceKpiThresholds.class);
                } catch (JsonProcessingException e) {
                    metrics.updateOpenSearchErrors(1);
                    log.error("Exception while parsing instance kpi thresholds", e);
                    return;
                }

                if (oldInstanceKpiThresholds.getStartTime() < instanceKpiThresholds.getStartTime()) {
                    if (!isThresholdChanged(oldInstanceKpiThresholds.getOperationType(), oldInstanceKpiThresholds.getThresholds(),
                            instanceKpiThresholds.getOperationType(), instanceKpiThresholds.getThresholds())) {
                        log.warn("Record found in OpenSearch has same operation type {} and threshold {} as current instance threshold data." +
                                " Skipping this data point.", oldInstanceKpiThresholds.getOperationType(), oldInstanceKpiThresholds.getThresholds());
                        metrics.updateDuplicateNorThresholds();

                        //Put old instance kpi thresholds data in local cache
                        long expiryTime = System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(cacheTimeout);
                        localCache.pushKpiThresholdData(DelayThresholdQueue.builder().key(key).type("instance").timeoutDuration(expiryTime).build(),
                                oldInstanceKpiThresholds, null);

                        return;
                    }
                } else {
                    log.warn("Record found in OpenSearch has recent start time {} than current instance threshold startTime {}." +
                            " Skipping this data point.", oldInstanceKpiThresholds.getStartTime(), instanceKpiThresholds.getStartTime());
                    metrics.updateOldNorThresholds();
                    return;
                }

                Map<String, Object> kpiThresholds = new HashMap<>();
                kpiThresholds.put("endTime", instanceKpiThresholds.getStartTime() + "L");

                updateDocument(accountIdentifier, kpiThresholdsIndex, idPojo.getIndexName(), idPojo.getDocId(), kpiThresholds);
            }
        }

        //Put new instance kpi thresholds data in local cache
        long expiryTime = System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(cacheTimeout);
        localCache.pushKpiThresholdData(DelayThresholdQueue.builder().key(key).type("instance").timeoutDuration(expiryTime).build(),
                instanceKpiThresholds, null);


        List<String> indexDates = DateHelper.getWeeksAsString(instanceKpiThresholds.getStartTime(), instanceKpiThresholds.getStartTime());
        String indexName = kpiThresholdsIndex + "_" + accountIdentifier + "_" + indexDates.get(0);

        metrics.updateInstanceKpiThresholdsMessages();

        instanceKpiThresholds.setTimestamp(DateHelper.getDate(instanceKpiThresholds.getStartTime()));
        try {
            String docId = instanceKpiThresholds.getInstanceId() + "#" + instanceKpiThresholds.getKpiId() + "#"
                    + instanceKpiThresholds.getKpiAttribute() + "#" + instanceKpiThresholds.getStartTime();
            log.trace("Pushing instance kpi thresholds data to OpenSearch in index {}, docId {}", indexName, docId);
            scheduler.addToIndexQueue(new IndexRequest.Builder<>()
                            .index(indexName)
                            .id(docId)
                            .document(instanceKpiThresholds)
                            .build(),
                    accountIdentifier, kpiThresholdsIndex);
        } catch (Exception e) {
            metrics.updateOpenSearchErrors(1);
            log.error("Exception while making instance kpis thresholds data push into OpenSearch. ", e);
        }

    }

    private IndexDocIdPojo checkExistingInstanceKpiThreshold(String accountIdentifier, InstanceKpiThresholds instanceKpiThresholds) throws IOException {

        OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, kpiThresholdsIndex);
        if (client == null) {
            log.error("NULL OpenSearch client for account {}, index {}.", accountIdentifier, kpiThresholdsIndex);
            return null;
        }

        String indexName = kpiThresholdsIndex + "_" + accountIdentifier + "*";

        List<NameValuePair> matchFields = new ArrayList<>();
        matchFields.add(new NameValuePair("kpiId", String.valueOf(instanceKpiThresholds.getKpiId())));
        matchFields.add(new NameValuePair("instanceId", instanceKpiThresholds.getInstanceId()));
        matchFields.add(new NameValuePair("thresholdType", instanceKpiThresholds.getThresholdType()));
        matchFields.add(new NameValuePair("kpiAttribute", instanceKpiThresholds.getKpiAttribute()));
        matchFields.add(new NameValuePair("endTime", String.valueOf(0)));

        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();
        for (NameValuePair entry : matchFields) {
            boolQueryBuilder.must(c -> c.matchPhrase(MatchPhraseQuery.of(d -> d.field(entry.getName()).query(entry.getValue()))));
        }

        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(indexName)
                .allowNoIndices(true)
                .query(new Query.Builder().bool(boolQueryBuilder.build()).build())
                .size(1)
                .sort(SortOptions.of(c -> c.field(FieldSort.of(d -> d.field("startTime").order(SortOrder.Desc)))))
                .build();

        log.trace("GET query to opensearch indices {} for fetching existing instance kpi thresholds data is {}", searchRequest.index(), printOSQuery(searchRequest));

        metrics.updateOpenSearchGetRequests();

        SearchResponse<?> searchResponse = client.search(searchRequest, Object.class);
        HitsMetadata<?> hits = searchResponse.hits();

        if (hits != null && hits.hits() != null && !hits.hits().isEmpty()) {
            IndexDocIdPojo idPojo = IndexDocIdPojo.builder()
                    .indexName(hits.hits().get(0).index())
                    .docId(hits.hits().get(0).id())
                    .source(gson.toJson(hits.hits().get(0).source()))
                    .build();
            log.trace("Instance kpi thresholds record found with docId {} and indexName {} from OpenSearch for kpiId {}, instanceId {}, thresholdType {}, kpiAttribute {}, endTime {}",
                    idPojo.getDocId(), idPojo.getIndexName(), instanceKpiThresholds.getKpiId(),
                    instanceKpiThresholds.getInstanceId(), instanceKpiThresholds.getThresholdType(), instanceKpiThresholds.getKpiAttribute(), 0);
            return idPojo;
        } else {
            log.trace("No instance kpi thresholds record found from OpenSearch for kpiId {}, instanceId {}, thresholdType {}, kpiAttribute {}, endTime {}", instanceKpiThresholds.getKpiId(),
                    instanceKpiThresholds.getInstanceId(), instanceKpiThresholds.getThresholdType(), instanceKpiThresholds.getKpiAttribute(), 0);
            return null;
        }
    }

    public void processTransactionKpiThreshold(TransactionKpiThresholds transactionKpiThresholds, String accountIdentifier) {
        if (transactionKpiThresholds == null) {
            log.error("NULL transaction kpi threshold found. Skipping this data point.");
            return;
        }

        //GET old transaction kpi thresholds data in local cache
        String key = accountIdentifier + "#" + transactionKpiThresholds.getTransactionId() + "#" + transactionKpiThresholds.getKpiId() + "#" + transactionKpiThresholds.getResponseType();
        TransactionKpiThresholds oldTransactionKpiThresholds = localCache.transactionKpiThresholdsConcurrentHashMap.getOrDefault(key, null);

        if (oldTransactionKpiThresholds != null) {
            log.trace("Existing transaction kpi threshold details found in local cache for key {}", key);
            if (oldTransactionKpiThresholds.getStartTime() < transactionKpiThresholds.getStartTime()) {
                List<String> indexDates = DateHelper.getWeeksAsString(oldTransactionKpiThresholds.getStartTime(), oldTransactionKpiThresholds.getStartTime());
                String indexName = transactionThresholdsIndex + "_" + accountIdentifier + "_" + indexDates.get(0);
                String docId = oldTransactionKpiThresholds.getTransactionId().hashCode() + "#" + oldTransactionKpiThresholds.getKpiId() + "#"
                        + oldTransactionKpiThresholds.getResponseType() + "#" + oldTransactionKpiThresholds.getStartTime();

                Map<String, Object> kpiThresholds = new HashMap<>();
                kpiThresholds.put("endTime", transactionKpiThresholds.getStartTime() + "L");

                updateDocument(accountIdentifier, transactionThresholdsIndex, indexName, docId, kpiThresholds);
            } else {
                log.error("New transaction kpi thresholds record has older start time that previously processed record. New record start time {}, Old record startTime {}", transactionKpiThresholds.getStartTime(), oldTransactionKpiThresholds.getStartTime());
                metrics.updateOldNorThresholds();
                return;
            }
        } else {
            IndexDocIdPojo idPojo;
            try {
                idPojo = checkExistingTransactionKpiThreshold(accountIdentifier, transactionKpiThresholds);
            } catch (IOException e) {
                metrics.updateOpenSearchErrors(1);
                log.error("Exception while getting Existing transaction kpis thresholds document id.", e);
                return;
            }

            if (idPojo != null && idPojo.getIndexName() != null && idPojo.getDocId() != null && idPojo.getSource() != null) {
                try {
                    oldTransactionKpiThresholds = objectMapper.readValue(idPojo.getSource(), TransactionKpiThresholds.class);
                } catch (JsonProcessingException e) {
                    metrics.updateOpenSearchErrors(1);
                    log.error("Exception while parsing transaction kpi thresholds", e);
                    return;
                }
                if (oldTransactionKpiThresholds != null) {
                    if (oldTransactionKpiThresholds.getStartTime() < transactionKpiThresholds.getStartTime()) {
                        if (!isThresholdChanged(oldTransactionKpiThresholds.getOperationType(), oldTransactionKpiThresholds.getThresholds(),
                                transactionKpiThresholds.getOperationType(), transactionKpiThresholds.getThresholds())) {
                            log.warn("Record found in OpenSearch has same operation type {} and threshold {} as current transaction threshold data." +
                                    " Skipping this data point.", oldTransactionKpiThresholds.getOperationType(), oldTransactionKpiThresholds.getThresholds());
                            metrics.updateDuplicateNorThresholds();

                            //Put old transaction kpi thresholds data in local cache
                            long expiryTime = System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(cacheTimeout);
                            localCache.pushKpiThresholdData(DelayThresholdQueue.builder().key(key).type("transaction").timeoutDuration(expiryTime).build(),
                                    null, oldTransactionKpiThresholds);

                            return;
                        }
                    } else {
                        log.warn("Record found in OpenSearch has recent start time {} than current transaction threshold startTime {}." +
                                " Skipping this data point.", oldTransactionKpiThresholds.getStartTime(), transactionKpiThresholds.getStartTime());
                        metrics.updateOldNorThresholds();
                        return;
                    }
                }

                Map<String, Object> kpiThresholds = new HashMap<>();
                kpiThresholds.put("endTime", transactionKpiThresholds.getStartTime() + "L");

                updateDocument(accountIdentifier, transactionThresholdsIndex, idPojo.getIndexName(), idPojo.getDocId(), kpiThresholds);
            }
        }

        //Put new transaction kpi thresholds data in local cache
        long expiryTime = System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(cacheTimeout);
        localCache.pushKpiThresholdData(DelayThresholdQueue.builder().key(key).type("transaction").timeoutDuration(expiryTime).build(),
                null, transactionKpiThresholds);

        List<String> indexDates = DateHelper.getWeeksAsString(transactionKpiThresholds.getStartTime(), transactionKpiThresholds.getStartTime());
        String indexName = transactionThresholdsIndex + "_" + accountIdentifier + "_" + indexDates.get(0);

        metrics.updateTransactionKpiThresholdsMessages();

        transactionKpiThresholds.setTimestamp(DateHelper.getDate(transactionKpiThresholds.getStartTime()));
        try {
            String docId = transactionKpiThresholds.getTransactionId().hashCode() + "#" + transactionKpiThresholds.getKpiId()
                    + "#" + transactionKpiThresholds.getResponseType() + "#" + transactionKpiThresholds.getStartTime();
            log.trace("Pushing transaction kpi thresholds data to OpenSearch in index {}, docId {}", indexName, docId);
            scheduler.addToIndexQueue(new IndexRequest.Builder<>()
                            .index(indexName)
                            .id(docId)
                            .document(transactionKpiThresholds)
                            .build(),
                    accountIdentifier, transactionThresholdsIndex);
        } catch (Exception e) {
            metrics.updateOpenSearchErrors(1);
            log.error("Exception while making Txn Thresholds data push into OpenSearch. ", e);
        }

    }

    private IndexDocIdPojo checkExistingTransactionKpiThreshold(String accountIdentifier, TransactionKpiThresholds transactionKpiThresholds) throws IOException {

        OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, transactionThresholdsIndex);
        if (client == null) {
            log.error("NULL OpenSearch client for account {}, index {}.", accountIdentifier, transactionThresholdsIndex);
            return null;
        }

        String indexName = transactionThresholdsIndex + "_" + accountIdentifier + "*";

        List<NameValuePair> matchFields = new ArrayList<>();
        matchFields.add(new NameValuePair("kpiId", String.valueOf(transactionKpiThresholds.getKpiId())));
        matchFields.add(new NameValuePair("transactionId", transactionKpiThresholds.getTransactionId()));
        matchFields.add(new NameValuePair("thresholdType", transactionKpiThresholds.getThresholdType()));
        matchFields.add(new NameValuePair("responseType", transactionKpiThresholds.getResponseType()));
        matchFields.add(new NameValuePair("endTime", String.valueOf(0)));

        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();
        for (NameValuePair entry : matchFields) {
            boolQueryBuilder.must(c -> c.matchPhrase(MatchPhraseQuery.of(d -> d.field(entry.getName()).query(entry.getValue()))));
        }

        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(indexName)
                .allowNoIndices(true)
                .query(new Query.Builder().bool(boolQueryBuilder.build()).build())
                .size(1)
                .sort(SortOptions.of(c -> c.field(FieldSort.of(d -> d.field("startTime").order(SortOrder.Desc)))))
                .build();

        log.trace("GET query to opensearch indices {} for fetching existing transaction kpi thresholds data is {}", searchRequest.index(), printOSQuery(searchRequest));

        metrics.updateOpenSearchGetRequests();

        SearchResponse<?> searchResponse = client.search(searchRequest, Object.class);
        HitsMetadata<?> hits = searchResponse.hits();

        if (hits != null && hits.hits() != null && !hits.hits().isEmpty()) {
            IndexDocIdPojo idPojo = IndexDocIdPojo.builder()
                    .indexName(hits.hits().get(0).index())
                    .docId(hits.hits().get(0).id())
                    .source(gson.toJson(hits.hits().get(0).source()))
                    .build();
            log.trace("Transaction kpi thresholds record found with docId {} and indexName {} from OpenSearch for kpiId {}, transactionId {}, thresholdType {}, responseType {}, endTime {}",
                    idPojo.getDocId(), idPojo.getIndexName(), transactionKpiThresholds.getKpiId(),
                    transactionKpiThresholds.getTransactionId(), transactionKpiThresholds.getThresholdType(), transactionKpiThresholds.getResponseType(), 0);
            return idPojo;
        } else {
            log.trace("No transaction kpi thresholds record found from OpenSearch for kpiId {}, transactionId {}, thresholdType {}, responseType {}, endTime {}", transactionKpiThresholds.getKpiId(),
                    transactionKpiThresholds.getTransactionId(), transactionKpiThresholds.getThresholdType(), transactionKpiThresholds.getResponseType(), 0);
            return null;
        }

    }

    private void updateDocument(String accountIdentifier, String indexPrefix, String indexName, String docId, Map<String, Object> updateField) {
        if (updateOSByScript) {
            StringBuilder updateFields = new StringBuilder();
            updateField.forEach((k, v) -> updateFields.append("ctx._source.").append(k).append("=").append(v));

            UpdateByQueryRequest updateByQueryRequest = new UpdateByQueryRequest.Builder()
                    .index(indexName)
                    .query(Query.of(c -> c.matchPhrase(d -> d.field("_id").query(docId))))
                    .script(c -> c.inline(d -> d.source("if(ctx._source.endTime == 0) {" + updateFields + "}")))
                    .build();
            log.trace("Updating document with id {} in index {} with endTime {} with updateByQueryRequest", docId, indexName, updateField.get("endTime"));
            scheduler.addToUpdateByQueryQueue(updateByQueryRequest, accountIdentifier, indexPrefix);
        } else {
            UpdateRequest<?, ?> updateRequest = new UpdateRequest.Builder<>()
                    .index(indexName)
                    .id(docId)
                    .doc(updateField)
                    .retryOnConflict(3)
                    .build();
            log.trace("Updating document with id {} in index {} with endTime {} with updateRequest", docId, indexName, updateField.get("endTime"));
            scheduler.addToUpdateQueue(updateRequest, accountIdentifier, indexPrefix);
        }
    }

    public boolean isThresholdChanged(String oldOperationType, Map<String, Double> oldThresholds, String newOperationType, Map<String, Double> newThresholds) {
        if (oldOperationType.equalsIgnoreCase(newOperationType)) {
            return !Objects.equals(oldThresholds.get("Upper"), newThresholds.get("Upper")) || !Objects.equals(oldThresholds.get("Lower"), newThresholds.get("Lower"));
        } else {
            return true;
        }
    }

    public List<InstanceKpiThresholds> getInstanceLevelThresholdDetails(String accountIdentifier, Set<String> appIds, Set<String> serviceIds, long fromTime, long toTime) {

        String indexName = kpiThresholdsIndex + "_" + accountIdentifier;
        try {
            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, kpiThresholdsIndex);
            if (client == null) {
                log.error("NULL OpenSearch client for account {}, index {}.", accountIdentifier, kpiThresholdsIndex);
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date -> indexNames.add(indexName + "_" + date));

            BoolQuery.Builder appIdsboolQueryBuilder = new BoolQuery.Builder();
            appIds.forEach(appId ->
                    appIdsboolQueryBuilder.should(c -> c.matchPhrase(MatchPhraseQuery.of(d -> d.field("applicationId").query(appId)))).minimumShouldMatch("1"));

            BoolQuery.Builder svcIdsboolQueryBuilder = new BoolQuery.Builder();
            appIds.forEach(servId ->
                    svcIdsboolQueryBuilder.should(c -> c.matchPhrase(MatchPhraseQuery.of(d -> d.field("serviceIdentifier").query(servId)))).minimumShouldMatch("1"));

            BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();
            boolQueryBuilder.must(c -> c.matchPhrase(MatchPhraseQuery.of(d -> d.field("thresholdType").query("REALTIME"))))
                    .must(c -> c.matchPhrase(MatchPhraseQuery.of(d -> d.field("endTime").query("0"))))
                    .must(c -> c.bool(appIdsboolQueryBuilder.build()))
                    .must(c -> c.bool(svcIdsboolQueryBuilder.build()));

            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index(indexNames)
                    .ignoreUnavailable(true)
                    .query(new Query.Builder().bool(boolQueryBuilder.build()).build())
                    .size(10000)
                    .build();

            log.trace("GET query to opensearch indices {} for fetching instance kpi thresholds data is {}", searchRequest.index(), printOSQuery(searchRequest));

            SearchResponse<?> response = client.search(searchRequest, Object.class);
            HitsMetadata<?> hits = response.hits();

            if (hits != null && hits.hits() != null && !hits.hits().isEmpty()) {
                return hits.hits().stream().map(hit -> {
                            try {
                                return objectMapper.readValue(gson.toJson(hit.source()), new TypeReference<InstanceKpiThresholds>() {
                                });
                            } catch (Exception e) {
                                log.error("Error occurred while mapping OpenSearch Instance threshold data to InstanceKpiThresholds bean from index{}. Details: {}", indexName, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            } else {
                metrics.updateOpenSearchErrors(1);
                log.error("No response obtained from opensearch for instance threshold indexes {}", indexNames);
                return Collections.emptyList();
            }
        } catch (Exception e) {
            metrics.updateOpenSearchErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexName, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    public List<TransactionKpiThresholds> getTransactionThresholdDetails(String accountIdentifier, Set<String> serviceIdentifiers, long fromTime, long toTime) {

        String indexName = transactionThresholdsIndex + "_" + accountIdentifier;
        try {
            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, transactionThresholdsIndex);
            if (client == null) {
                log.error("NULL OpenSearch client for account {}, index {}.", accountIdentifier, transactionThresholdsIndex);
                return Collections.emptyList();
            }

            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date -> indexNames.add(indexName + "_" + date));

            BoolQuery.Builder svcIdsboolQueryBuilder = new BoolQuery.Builder();
            serviceIdentifiers.forEach(servId ->
                    svcIdsboolQueryBuilder.should(c -> c.matchPhrase(MatchPhraseQuery.of(d -> d.field("serviceIdentifier").query(servId)))));

            BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();
            boolQueryBuilder.must(c -> c.matchPhrase(MatchPhraseQuery.of(d -> d.field("thresholdType").query("REALTIME"))))
                    .must(c -> c.matchPhrase(MatchPhraseQuery.of(d -> d.field("endTime").query("0"))))
                    .must(c -> c.bool(svcIdsboolQueryBuilder.build()));

            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index(indexNames)
                    .ignoreUnavailable(true)
                    .query(new Query.Builder().bool(boolQueryBuilder.build()).build())
                    .size(10000)
                    .build();

            log.trace("GET query to opensearch indices {} for fetching transaction kpi thresholds data is {}", searchRequest.index(), printOSQuery(searchRequest));

            SearchResponse<?> response = client.search(searchRequest, Object.class);
            HitsMetadata<?> hits = response.hits();

            if (hits != null && hits.hits() != null && !hits.hits().isEmpty()) {
                return hits.hits().stream().map(hit -> {
                            try {
                                return objectMapper.readValue(gson.toJson(hit.source()), new TypeReference<TransactionKpiThresholds>() {
                                });
                            } catch (Exception e) {
                                log.error("Error occurred while mapping OpenSearch transaction threshold data to TransactionKpiThresholds bean from index{}. Details: {}", indexName, accountIdentifier, e);
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            } else {
                metrics.updateOpenSearchErrors(1);
                log.error("No response obtained from opensearch for transaction threshold indexes {}", indexNames);
                return Collections.emptyList();
            }
        } catch (Exception e) {
            metrics.updateOpenSearchErrors(1);
            log.error("Error in fetching data from index {}. Details: accountId [{}]",
                    indexName, accountIdentifier, e);
        }
        return Collections.emptyList();
    }

    public void updateInstanceKpiClosedThresholds(String accountIdentifier, List<InstanceKpiThresholds> instanceKpiThresholds) {
        instanceKpiThresholds.forEach(instanceKpiThreshold -> triggerInstanceKpiClosedThresholdUpdateRequest(instanceKpiThreshold, accountIdentifier));
    }

    private void triggerInstanceKpiClosedThresholdUpdateRequest(InstanceKpiThresholds instanceKpiThreshold, String accountIdentifier) {
        try {
            String instanceId = instanceKpiThreshold.getInstanceId();
            long kpiId = instanceKpiThreshold.getKpiId();
            String kpiAttribute = instanceKpiThreshold.getKpiAttribute();
            long startTime = instanceKpiThreshold.getStartTime();

            String date = DateHelper.getWeeksAsString(startTime, startTime).get(0);
            String indexName = kpiThresholdsIndex + "_" + accountIdentifier + "_" + date;
            log.trace("Adding instance kpi closed threshold details to index {}: with details {}", indexName, instanceKpiThreshold);

            String docId = instanceId + "#" + kpiId + "#" + kpiAttribute + "#" + startTime;

            UpdateRequest<?, ?> updateRequest = new UpdateRequest.Builder<>()
                    .index(indexName)
                    .id(docId)
                    .doc(instanceKpiThreshold)
                    .build();
            scheduler.addToUpdateQueue(updateRequest, accountIdentifier, kpiThresholdsIndex);

            String key = accountIdentifier + "#" + instanceKpiThreshold.getServiceIdentifier() + "#" + instanceId
                    + "#" + kpiId + "#" + kpiAttribute;
            localCache.instanceKpiThresholdsConcurrentHashMap.remove(key);
        } catch (Exception e) {
            metrics.updateOpenSearchErrors(1);
            log.error("Exception while updating closed instance kpi thresholds data in OpenSearch. ", e);
        }
    }

    public void updateTxnKpiClosedThresholds(String accountIdentifier, List<TransactionKpiThresholds> txnKpiThresholds) {
        txnKpiThresholds.forEach(txnKpiThreshold -> triggerTxnKpiThresholdUpdateRequest(txnKpiThreshold, accountIdentifier));
    }

    private void triggerTxnKpiThresholdUpdateRequest(TransactionKpiThresholds txnKpiThreshold, String accountIdentifier) {
        try {
            int txnId = txnKpiThreshold.getTransactionId().hashCode();
            long kpiId = txnKpiThreshold.getKpiId();
            String responseType = txnKpiThreshold.getResponseType();
            long startTime = txnKpiThreshold.getStartTime();

            String date = DateHelper.getWeeksAsString(startTime, startTime).get(0);
            String indexName = transactionThresholdsIndex + "_" + accountIdentifier + "_" + date;
            log.trace("Adding transaction kpi closed threshold details to index {}: with details {}", indexName, txnKpiThreshold);

            String docId = txnId + "#" + kpiId + "#" + responseType + "#" + startTime;

            UpdateRequest<?, ?> updateRequest = new UpdateRequest.Builder<>()
                    .index(indexName)
                    .id(docId)
                    .doc(txnKpiThreshold)
                    .build();
            scheduler.addToUpdateQueue(updateRequest, accountIdentifier, transactionThresholdsIndex);

            String key = accountIdentifier + "#" + txnKpiThreshold.getTransactionId() + "#" + kpiId + "#" + responseType;
            localCache.transactionKpiThresholdsConcurrentHashMap.remove(key);
        } catch (Exception e) {
            metrics.updateOpenSearchErrors(1);
            log.error("Exception while updating closed txn kpi thresholds data in OpenSearch. ", e);
        }
    }

    private static ByteArrayOutputStream printOSQuery(RequestBase request) {
        ByteArrayOutputStream bas = new ByteArrayOutputStream();
        JsonpMapper mapper = new JacksonJsonpMapper();
        JsonGenerator generator = mapper.jsonProvider().createGenerator(bas);
        mapper.serialize(request, generator);
        generator.close();
        return bas;
    }

    /**
     * Insert or update anomaly data in OpenSearch
     */
    public void insertOrUpdateAnomalyData(AnomalyAccountPojo anomalyAccountPojo) {
        Anomalies anomalies = anomalyAccountPojo.getAnomalyDetails();
        try {
            String indexName = anomaliesIndex + "_" + anomalyAccountPojo.getAccountIdentifier() + "_" +
                             DateHelper.getWeeksAsString(anomalies.getAnomalyCreatedTime(),
                                     anomalies.getAnomalyCreatedTime()).get(0);

            anomalies.setTimestamp(DateHelper.getDate(anomalies.getAnomalyCreatedTime()));

            IndexRequest<AnomalyAccountPojo> indexRequest = new IndexRequest.Builder<AnomalyAccountPojo>()
                    .index(indexName)
                    .id(anomalies.getAnomalyId())
                    .document(anomalyAccountPojo)
                    .build();

            scheduler.addToIndexQueue(indexRequest, anomalyAccountPojo.getAccountIdentifier(), anomaliesIndex);
            log.debug("Anomaly data indexed in OpenSearch for anomalyId: {}, account: {}",
                    anomalies.getAnomalyId(), anomalyAccountPojo.getAccountIdentifier());
        } catch (Exception e) {
            metrics.updateOpenSearchErrors(1);
            log.error("Exception while indexing anomaly data in OpenSearch for anomalyId: {}, account: {}",
                    anomalies.getAnomalyId(), anomalyAccountPojo.getAccountIdentifier(), e);
        }
    }

    /**
     * Get anomaly data from OpenSearch.
     * Searches across time-based indices for the specified retention period.
     *
     * @param accountIdentifier the account identifier
     * @param anomalyId the anomaly ID to search for
     * @return AnomalyAccountPojo if found, null otherwise
     */
    public AnomalyAccountPojo getAnomalyFromOpenSearch(String accountIdentifier, String anomalyId) {
        try {
            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, anomaliesIndex);
            if (client == null) {
                log.error("NULL OpenSearch client for account {}, index {}.", accountIdentifier, anomaliesIndex);
                return null;
            }

            // Search across multiple time-based indices
            List<String> indexNames = new ArrayList<>();
            long currentTime = System.currentTimeMillis();
            long pastTime = currentTime - (anomalySearchRetentionDays * 24L * 60 * 60 * 1000); // Configurable retention period

            DateHelper.getWeeksAsString(pastTime, currentTime).forEach(date ->
                indexNames.add(anomaliesIndex + "_" + accountIdentifier + "_" + date));

            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index(indexNames)
                    .allowNoIndices(true)
                    .ignoreUnavailable(true)
                    .query(new Query.Builder()
                            .term(new TermQuery.Builder()
                                    .field("anomalyId")
                                    .value(FieldValue.of(anomalyId))
                                    .build())
                            .build())
                    .size(1)
                    .build();

            SearchResponse<?> response = client.search(searchRequest, Object.class);
            HitsMetadata<?> hits = response.hits();

            if (hits != null && hits.hits() != null && !hits.hits().isEmpty()) {
                Object source = hits.hits().get(0).source();
                Anomalies anomalies = objectMapper.readValue(gson.toJson(source), new TypeReference<Anomalies>() {});
                AnomalyAccountPojo anomalyAccountPojo = new AnomalyAccountPojo();
                anomalyAccountPojo.setAccountIdentifier(accountIdentifier);
                anomalyAccountPojo.setAnomalyDetails(anomalies);
                return anomalyAccountPojo;
            }
        } catch (Exception e) {
            metrics.updateOpenSearchErrors(1);
            log.error("Error retrieving anomaly from OpenSearch for anomalyId: {}, account: {}",
                     anomalyId, accountIdentifier, e);
        }
        return null;
    }

    public void insertAlertData(Alerts alerts, String accountIdentifier, String alertId) {
        try {
            String indexName = alertsIndex + "_" + accountIdentifier + "_" +
                    DateHelper.getWeeksAsString(alerts.getIdentifiedTime(), alerts.getIdentifiedTime()).get(0);

            alerts.setTimestamp(DateHelper.getDate(alerts.getIdentifiedTime()));

            IndexRequest<Alerts> indexRequest = new IndexRequest.Builder<Alerts>()
                    .index(indexName)
                    .id(alertId)
                    .document(alerts)
                    .build();

            scheduler.addToIndexQueue(indexRequest, accountIdentifier, alertsIndex);
            log.debug("Alerts data indexed in OpenSearch for anomalyId: {}, account: {}",
                    alerts.getAnomalyId(), accountIdentifier);
        } catch (Exception e) {
            metrics.updateOpenSearchErrors(1);
            log.error("Exception while indexing alerts data in OpenSearch for anomalyId: {}, account: {}",
                    alerts.getAnomalyId(), accountIdentifier, e);
        }
    }
}