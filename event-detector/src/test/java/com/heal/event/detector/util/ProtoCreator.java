package com.heal.event.detector.util;

import com.appnomic.appsone.common.protbuf.*;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.utility.Utils;
import com.heal.event.detector.pojos.enums.Intervals;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.ZoneOffset;
import java.util.*;

@Service
public class ProtoCreator {


    public AggregatedKpiProtos.AggregatedKpi createAggregatedKPIBodyNonGrpKpiType(String account, String application,
                                                                                  String service, String instance,
                                                                                  int kpiId, String kpiName,
                                                                                  int collectionInterval,
                                                                                  int timeZoneOffsetInSec,
                                                                                  String valueForNonGrpKpi,
                                                                                  KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType kpiType) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Calendar cal = Calendar.getInstance();
        String startTimeInGMT = dateFormat.format(cal.getTime());


        List<String> serviceList = new ArrayList<>(Arrays.asList(service.split(",")));

        List<String> appList = new ArrayList<>(Arrays.asList(application.split(",")));

        Map<String, String> metaData = new HashMap<>();
        metaData.put("sampleValue", "0.009");

        return AggregatedKpiProtos.AggregatedKpi.newBuilder()
                .setAccountId(account)
                .addAllApplicationId(appList)
                .setInstanceId(instance)
                .setTimeZoneOffsetInSec(timeZoneOffsetInSec)
                .addAllServiceId(serviceList)
                .setTimeInGMT(startTimeInGMT)
                .setAgentId("sample-agent")
                .putAllMetaData(metaData)
                .setKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                        .newBuilder()
                        .setKpiUid(kpiId)
                        .setTimeInGMT(startTimeInGMT)
                        .setKpiType(kpiType)
                        .setKpiName(kpiName)
                        .setVal(valueForNonGrpKpi)
                        .setCollectionInterval(collectionInterval)
                        .setIsKpiGroup(false)
                        .build()).build();
    }

    public AggregatedKpiProtos.AggregatedKpi createAggregatedKPIBodyGrpKpiType(String account, String application,
                                                                               String service, String instance,
                                                                               int kpiId, String kpiName,
                                                                               int collectionInterval,
                                                                               int timeZoneOffsetInSec,
                                                                               Map<String, String> groupKpiMap,
                                                                               KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType kpiType) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Calendar cal = Calendar.getInstance();
        String startTimeInGMT = dateFormat.format(cal.getTime());


        List<String> serviceList = new ArrayList<>(Arrays.asList(service.split(",")));

        List<String> appList = new ArrayList<>(Arrays.asList(application.split(",")));

        KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi groupKpi = KPIAgentMessageProtos
                .KPIAgentMessage.KpiData.GroupKpi.newBuilder().putAllPairs(groupKpiMap).build();

        return AggregatedKpiProtos.AggregatedKpi.newBuilder()
                .setAccountId(account)
                .addAllApplicationId(appList)
                .setInstanceId(instance)
                .setTimeZoneOffsetInSec(timeZoneOffsetInSec)
                .addAllServiceId(serviceList)
                .setTimeInGMT(startTimeInGMT)
                .setKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                        .newBuilder()
                        .setKpiUid(kpiId)
                        .setTimeInGMT(startTimeInGMT)
                        .setKpiType(kpiType)
                        .setKpiName(kpiName)
                        .setGroupKpi(groupKpi)
                        .setCollectionInterval(collectionInterval)
                        .setIsKpiGroup(true)
                        .build()).build();
    }

    public AggregatedKpiProtos.AggregatedKpi createAggregatedKPIBodyWatcherKpiType(String account, String application,
                                                                                   String service, String instance,
                                                                                   int kpiId, String kpiName,
                                                                                   int collectionInterval, boolean isKpiGroup,
                                                                                   int timeZoneOffsetInSec,
                                                                                   Map<String, KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi> watcherKpiMap,
                                                                                   KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType kpiType) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Calendar cal = Calendar.getInstance();
        String startTimeInGMT = dateFormat.format(cal.getTime());


        List<String> serviceList = new ArrayList<>(Arrays.asList(service.split(",")));

        List<String> appList = new ArrayList<>(Arrays.asList(application.split(",")));

        KPIAgentMessageProtos.KPIAgentMessage.KpiData.WatcherKpiValue watcherKpiValue = KPIAgentMessageProtos
                .KPIAgentMessage.KpiData.WatcherKpiValue.newBuilder().putAllKeyValuePair(watcherKpiMap).build();

        return AggregatedKpiProtos.AggregatedKpi.newBuilder()
                .setAccountId(account)
                .addAllApplicationId(appList)
                .setInstanceId(instance)
                .setTimeZoneOffsetInSec(timeZoneOffsetInSec)
                .addAllServiceId(serviceList)
                .setTimeInGMT(startTimeInGMT)
                .setKpiData(KPIAgentMessageProtos.KPIAgentMessage.KpiData
                        .newBuilder()
                        .setKpiUid(kpiId)
                        .setTimeInGMT(startTimeInGMT)
                        .setKpiType(kpiType)
                        .setKpiName(kpiName)
                        .setWatcherKpiValue(watcherKpiValue)
                        .setCollectionInterval(collectionInterval)
                        .setIsKpiGroup(isKpiGroup)
                        .build()).build();
    }

    public ViolatedData createViolatedDataKpiInfoType(String account, String application,
                                                      String service, String instance,
                                                      String kpiId, String attributeName, String kpiVal,
                                                      int timeZoneOffsetInSec, String thresholdType,
                                                      String operationType, String thresholdSeverity,
                                                      Map<String, Double> thresholdMap) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Calendar cal = Calendar.getInstance();
        String startTimeInGMT = dateFormat.format(cal.getTime());

        String timeInGMT = Utils.getKey(startTimeInGMT, Intervals.Minutely); //remove seconds

        long violatedTimeInEpochSec;
        try {
            violatedTimeInEpochSec = Utils.dateStrToLocalDateTime(timeInGMT).toEpochSecond(ZoneOffset.UTC);
        } catch (Exception e) {
            return null;
        }

        List<String> serviceList = new ArrayList<>(Arrays.asList(service.split(",")));
        List<String> appList = new ArrayList<>(Arrays.asList(application.split(",")));

        ViolatedData violatedData = new ViolatedData(account, appList);

        violatedData.setThresholdType(thresholdType);
        violatedData.setViolationFor(thresholdType.equalsIgnoreCase("Static") ? "SOR" : "NOR");
        violatedData.setTimezoneOffsetInSeconds(timeZoneOffsetInSec);
        violatedData.setViolationTime(violatedTimeInEpochSec * 1000);
        violatedData.setServiceList(serviceList);
        violatedData.setValue(kpiVal);
        violatedData.setEventType(ViolationEventType.KPI_VIOLATION);
        violatedData.setKpiId(kpiId);
        violatedData.setInstanceId(instance);
        violatedData.setKpiAttribute(attributeName);
        violatedData.setOperationType(operationType);
        violatedData.setThresholds(thresholdMap);
        violatedData.setThresholdSeverity(thresholdSeverity);
        violatedData.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        violatedData.setDisplayAttributeName(attributeName);
        violatedData.setIsInfo(1);
        violatedData.setMaintenanceExcluded(true);
        violatedData.setKpiViolationTime(startTimeInGMT);

        return violatedData;
    }

    public ViolatedEventProtos.ViolatedEvent createViolatedEventKpiInfoType(String account, String application,
                                                                            String service, String instance,
                                                                            String kpiId, String attributeName, String kpiVal,
                                                                            int timeZoneOffsetInSec, String thresholdType,
                                                                            String operationType, String thresholdSeverity,
                                                                            Map<String, Double> thresholdMap) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Calendar cal = Calendar.getInstance();
        String startTimeInGMT = dateFormat.format(cal.getTime());

        List<String> serviceList = new ArrayList<>(Arrays.asList(service.split(",")));

        List<String> appList = new ArrayList<>(Arrays.asList(application.split(",")));

        Map<String, String> metaData = new HashMap<>();
        metaData.put("sampleValue", "0.009");

        return ViolatedEventProtos.ViolatedEvent.newBuilder()
                .setAccountId(account)
                .addAllAppId(appList)
                .setTimezoneOffsetInSeconds(timeZoneOffsetInSec)
                .setThresholdType(thresholdType)
                .addKpis(ViolatedEventProtos.Kpi.newBuilder()
                        .setKpiInfo(ViolatedEventProtos.KpiInfo.newBuilder()
                                .setInstanceId(instance)
                                .setKpiId(kpiId)
                                .setKpiAttribute(attributeName)
                                .addAllSvcId(serviceList)
                                .putAllThresholds(thresholdMap)
                                .setOperationType(operationType)
                                .setThresholdSeverity(thresholdSeverity)
                                .setAgentId("sample-data")
                                .putAllMetaData(metaData)
                                .build())
                        .setValue(kpiVal)
                        .build())
                .setViolationTmeInGMT(startTimeInGMT)
                .build();
    }

    public ViolatedEventProtos.ViolatedEvent createViolatedEventKpiInfoConfigWatchType(String account, String application,
                                                                                       String service, String instance,
                                                                                       String kpiId, String attributeName, String kpiVal,
                                                                                       int timeZoneOffsetInSec, String thresholdType,
                                                                                       String operationType, String thresholdSeverity) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Calendar cal = Calendar.getInstance();
        String startTimeInGMT = dateFormat.format(cal.getTime());

        List<String> serviceList = new ArrayList<>(Arrays.asList(service.split(",")));

        List<String> appList = new ArrayList<>(Arrays.asList(application.split(",")));

        return ViolatedEventProtos.ViolatedEvent.newBuilder()
                .setAccountId(account)
                .addAllAppId(appList)
                .setTimezoneOffsetInSeconds(timeZoneOffsetInSec)
                .setThresholdType(thresholdType)
                .addKpis(ViolatedEventProtos.Kpi.newBuilder()
                        .setKpiInfo(ViolatedEventProtos.KpiInfo.newBuilder()
                                .setInstanceId(instance)
                                .setKpiId(kpiId)
                                .setKpiAttribute(attributeName)
                                .addAllSvcId(serviceList)
                                .setOperationType(operationType)
                                .setThresholdSeverity(thresholdSeverity)
                                .build())
                        .setValue(kpiVal)
                        .build())
                .setViolationTmeInGMT(startTimeInGMT)
                .build();
    }

    public ViolatedEventProtos.ViolatedEvent createViolatedEventTxnInfoType(String account, String application,
                                                                            String service,
                                                                            String transactionId,
                                                                            String kpiId,
                                                                            PSAgentMessageProtos.ResponseTime.ResponseTimeType responseTimeType,
                                                                            String kpiVal, String grpId,
                                                                            int timeZoneOffsetInSec, String thresholdType,
                                                                            String operationType, String thresholdSeverity,
                                                                            Map<String, Double> thresholdMap) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Calendar cal = Calendar.getInstance();
        String startTimeInGMT = dateFormat.format(cal.getTime());

        List<String> appList = new ArrayList<>(Arrays.asList(application.split(",")));

        return ViolatedEventProtos.ViolatedEvent.newBuilder()
                .setAccountId(account)
                .addAllAppId(appList)
                .setTimezoneOffsetInSeconds(timeZoneOffsetInSec)
                .setThresholdType(thresholdType)
                .addTransactions(ViolatedEventProtos.Transaction.newBuilder()
                        .setTxnInfo(ViolatedEventProtos.TransactionInfo.newBuilder()
                                .setGroupId(grpId)
                                .setKpiId(kpiId)
                                .setTransactionId(transactionId)
                                .setSvcId(service)
                                .setResponseTimeType(responseTimeType)
                                .setOperationType(operationType)
                                .putAllThresholds(thresholdMap)
                                .setThresholdSeverity(thresholdSeverity)
                                .build())
                        .setValue(kpiVal)
                        .build())
                .setViolationTmeInGMT(startTimeInGMT)
                .build();
    }

    public ViolatedEventProtos.ViolatedEvent createViolatedEventBatchJobInfoType(String account, String application,
                                                                                 String batchJobId,
                                                                                 String kpiId,
                                                                                 String kpiVal,
                                                                                 int timeZoneOffsetInSec, String thresholdType,
                                                                                 String operationType, String thresholdSeverity,
                                                                                 Map<String, Double> thresholdMap,
                                                                                 Map<String, String> metaDataMap) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Calendar cal = Calendar.getInstance();
        String startTimeInGMT = dateFormat.format(cal.getTime());

        List<String> appList = new ArrayList<>(Arrays.asList(application.split(",")));

        return ViolatedEventProtos.ViolatedEvent.newBuilder()
                .setAccountId(account)
                .addAllAppId(appList)
                .setTimezoneOffsetInSeconds(timeZoneOffsetInSec)
                .setThresholdType(thresholdType)
                .addBatchJob(ViolatedEventProtos.BatchJob.newBuilder()
                        .setBatchJob(batchJobId)
                        .setKpiId(kpiId)
                        .setOperationType(operationType)
                        .putAllThresholds(thresholdMap)
                        .setThresholdSeverity(thresholdSeverity)
                        .setValue(kpiVal)
                        .putAllMetaData(metaDataMap)
                        .build())
                .setViolationTmeInGMT(startTimeInGMT)
                .build();
    }

    public ThresholdProtos.Threshold createThresholdProtoKpiInfoType(String account, String application,
                                                                     String service, String instance,
                                                                     String kpiId, String attributeName,
                                                                     int timeZoneOffsetInSec, String thresholdType,
                                                                     String operationType, String thresholdSeverity,
                                                                     Map<String, Double> thresholdMap) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Calendar cal = Calendar.getInstance();
        String startTimeInGMT = dateFormat.format(cal.getTime());

        List<String> serviceList = new ArrayList<>(Arrays.asList(service.split(",")));

        return ThresholdProtos.Threshold.newBuilder()
                .setAccountId(account)
                .setTimeInGMT(startTimeInGMT)
                .setTimezoneOffsetInSeconds(timeZoneOffsetInSec)
                .setThresholdType(thresholdType)
                .setApplicationId(application)
                .addKpiThreshold(ViolatedEventProtos.KpiInfo.newBuilder()
                        .setInstanceId(instance)
                        .setKpiId(kpiId)
                        .setKpiAttribute(attributeName)
                        .addAllSvcId(serviceList)
                        .putAllThresholds(thresholdMap)
                        .setOperationType(operationType)
                        .setThresholdSeverity(thresholdSeverity)
                        .build())
                .build();
    }

    public ThresholdProtos.Threshold createThresholdProtoTxnInfoType(String account, String application,
                                                                     String service,
                                                                     String transactionId,
                                                                     String kpiId,
                                                                     PSAgentMessageProtos.ResponseTime.ResponseTimeType responseTimeType,
                                                                     String grpId,
                                                                     int timeZoneOffsetInSec, String thresholdType,
                                                                     String operationType, String thresholdSeverity,
                                                                     Map<String, Double> thresholdMap) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        Calendar cal = Calendar.getInstance();
        String startTimeInGMT = dateFormat.format(cal.getTime());

        return ThresholdProtos.Threshold.newBuilder()
                .setAccountId(account)
                .setTimeInGMT(startTimeInGMT)
                .setTimezoneOffsetInSeconds(timeZoneOffsetInSec)
                .setThresholdType(thresholdType)
                .setApplicationId(application)
                .addTransactionThreshold(ViolatedEventProtos.TransactionInfo.newBuilder()
                        .setGroupId(grpId)
                        .setKpiId(kpiId)
                        .setTransactionId(transactionId)
                        .setSvcId(service)
                        .setResponseTimeType(responseTimeType)
                        .setOperationType(operationType)
                        .putAllThresholds(thresholdMap)
                        .setThresholdSeverity(thresholdSeverity)
                        .build())
                .build();
    }

    public Map<String, Double> thresholdMapProvider() {
        Map<String, Double> thresholdMap = new HashMap<>();
        thresholdMap.put("Lower", 10.0);
        thresholdMap.put("Upper", 20.0);
        return thresholdMap;
    }

    public Map<String, String> metadataMapProvider() {
        Map<String, String> metadataMap = new HashMap<>();
        metadataMap.put("grpName", "DiskSpace");
        return metadataMap;
    }
}
