package com.heal.event.detector.core;

import com.heal.configuration.pojos.*;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.pojos.enums.PersistenceSuppressionStatus;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.utility.cache.CacheWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

public class PersistenceSuppressionTest {
    @Mock
    private RedisUtilities redisUtilitiesMock;
    @Mock
    private CacheWrapper cacheWrapper;
    @Mock
    AnomalyManagementService anomalyManagementService;

    private PersistenceSuppression persistenceSuppression;
    private PersistenceSuppressionConfiguration persistenceSuppressionConfiguration;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        persistenceSuppression = Mockito.spy(new PersistenceSuppression());
        persistenceSuppression.redisUtilities = redisUtilitiesMock;
        persistenceSuppression.cacheWrapper = cacheWrapper;
        persistenceSuppression.anomalyManagementService = anomalyManagementService;

        // Manually set @Value fields
        persistenceSuppression.highSeverityIdSignal = "433";
        persistenceSuppression.mediumSeverityIdSignal = "432";
        persistenceSuppression.lowSeverityIdSignal = "431";
        persistenceSuppression.entityTypeInstance = "INSTANCE";
        persistenceSuppression.entityTypeTransaction = "TRANSACTION";
        persistenceSuppression.entityTypeService = "SERVICE";
        persistenceSuppression.transactionComponentIdentifier = "Transaction";

        persistenceSuppressionConfiguration = mock(PersistenceSuppressionConfiguration.class);
        when(persistenceSuppressionConfiguration.getLowPersistence()).thenReturn(2);
        when(persistenceSuppressionConfiguration.getLowSuppression()).thenReturn(3);
        when(persistenceSuppressionConfiguration.getMediumPersistence()).thenReturn(4);
        when(persistenceSuppressionConfiguration.getMediumSuppression()).thenReturn(5);
        when(persistenceSuppressionConfiguration.getHighPersistence()).thenReturn(6);
        when(persistenceSuppressionConfiguration.getHighSuppression()).thenReturn(7);
    }

    @Test
    void testGetPersSuppAndCollectionIntervalAtServiceConf_Instance() {
        // Arrange
        String entityType = persistenceSuppression.entityTypeInstance;
        String accountId = "acc";
        String instanceId = "inst";
        String kpiId = "101";
        String serviceId = "431";
        String severityId = persistenceSuppression.lowSeverityIdSignal;

        CompInstKpiEntity kpiDetails = mock(CompInstKpiEntity.class);
        when(cacheWrapper.getInstanceKPIDetails(accountId, instanceId, Integer.parseInt(kpiId))).thenReturn(kpiDetails);
        when(kpiDetails.getCollectionInterval()).thenReturn(80);

        when(persistenceSuppression.getPersistenceSuppressionServiceConf(any(), any() , anyInt())).thenReturn(persistenceSuppressionConfiguration);

        // Act
        Map<String, Integer> result = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                entityType, accountId, instanceId, kpiId, serviceId, severityId);

        // Assert
        assertEquals(80, result.get("collectionInterval"));
        assertEquals(2, result.get("persistence"));
        assertEquals(3, result.get("suppression"));
    }

    @Test
    void testGetPersSuppAndCollectionIntervalAtServiceConf_Transaction() {
        // Arrange
        String entityType = persistenceSuppression.entityTypeTransaction;
        String accountId = "acc";
        String txnId = "txn";
        String kpiId = "101";
        String serviceId = "432";
        String severityId = persistenceSuppression.mediumSeverityIdSignal;

        ComponentKpiEntity componentKpiDetails = mock(ComponentKpiEntity.class);
        ComponentCommonVersion componentCommonVersion = mock(ComponentCommonVersion.class);
        when(componentCommonVersion.getCollectionInterval()).thenReturn(70);
        when(componentKpiDetails.getCommonVersionDetails()).thenReturn(Collections.singletonList(componentCommonVersion));
        when(cacheWrapper.getComponentKPIDetails(eq(accountId), eq(persistenceSuppression.transactionComponentIdentifier), eq(kpiId))).thenReturn(componentKpiDetails);
        when(persistenceSuppression.getPersistenceSuppressionServiceConf(any(), any() , anyInt())).thenReturn(persistenceSuppressionConfiguration);

        // Act
        Map<String, Integer> result = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                entityType, accountId, txnId, kpiId, serviceId, severityId);

        // Assert
        assertEquals(70, result.get("collectionInterval"));
        assertEquals(4, result.get("persistence"));
        assertEquals(5, result.get("suppression"));
    }

    @Test
    void testPersistenceSuppressionMetForSeverity_persistence() {
        ViolationStatus status = ViolationStatus.builder()
                .violationCount(1)
                .persistence(2)
                .suppression(3)
                .build();
        Map<String, ViolationStatus> map = new HashMap<>();
        map.put("431", status);
        ViolationDetails details = ViolationDetails.builder().violationStatusMap(map).build();
        PersistenceSuppressionStatus result = persistenceSuppression.persistenceSuppressionMetForSeverity(details, "431");
        assertEquals(PersistenceSuppressionStatus.PERSISTENCE, result);
    }

    @Test
    void testPersistenceSuppressionMetForSeverity_continue() {
        ViolationStatus status = ViolationStatus.builder()
                .violationCount(2)
                .persistence(2)
                .suppression(3)
                .build();
        Map<String, ViolationStatus> map = new HashMap<>();
        map.put("431", status);
        ViolationDetails details = ViolationDetails.builder().violationStatusMap(map).build();
        PersistenceSuppressionStatus result = persistenceSuppression.persistenceSuppressionMetForSeverity(details, "431");
        assertEquals(PersistenceSuppressionStatus.CONTINUE, result);
    }

    @Test
    void testPersistenceSuppressionMetForSeverity_suppression() {
        ViolationStatus status = ViolationStatus.builder()
                .violationCount(5)
                .persistence(2)
                .suppression(3)
                .build();
        Map<String, ViolationStatus> map = new HashMap<>();
        map.put("431", status);
        ViolationDetails details = ViolationDetails.builder().violationStatusMap(map).build();
        PersistenceSuppressionStatus result = persistenceSuppression.persistenceSuppressionMetForSeverity(details, "431");
        assertEquals(PersistenceSuppressionStatus.SUPPRESSION, result);
    }

    @Test
    void testPersistenceSuppressionMetForSeverity_error_nullDetails() {
        PersistenceSuppressionStatus result = persistenceSuppression.persistenceSuppressionMetForSeverity(null, "431");
        assertEquals(PersistenceSuppressionStatus.ERROR, result);
    }

    @Test
    void testPersistenceSuppressionMetForSeverity_error_noStatusMap() {
        ViolationDetails details = ViolationDetails.builder().violationStatusMap(null).build();
        PersistenceSuppressionStatus result = persistenceSuppression.persistenceSuppressionMetForSeverity(details, "431");
        assertEquals(PersistenceSuppressionStatus.ERROR, result);
    }

    @Test
    void testPersistenceSuppressionMetForSeverity_error_noSeverity() {
        Map<String, ViolationStatus> map = new HashMap<>();
        ViolationDetails details = ViolationDetails.builder().violationStatusMap(map).build();
        PersistenceSuppressionStatus result = persistenceSuppression.persistenceSuppressionMetForSeverity(details, "431");
        assertEquals(PersistenceSuppressionStatus.ERROR, result);
    }

    @Test
    void testPersistenceSuppressionMetForSeverity_error_nullStatus() {
        Map<String, ViolationStatus> map = new HashMap<>();
        map.put("431", null);
        ViolationDetails details = ViolationDetails.builder().violationStatusMap(map).build();
        PersistenceSuppressionStatus result = persistenceSuppression.persistenceSuppressionMetForSeverity(details, "431");
        assertEquals(PersistenceSuppressionStatus.ERROR, result);
    }

    @Test
    void testGetPersSuppAndCollectionIntervalAtServiceConf_UnknownEntityType() {
        // Arrange
        String entityType = "UNKNOWN";
        String accountId = "acc";
        String instanceId = "inst";
        String kpiId = "101";
        String serviceId = "svc";
        String severityId = persistenceSuppression.lowSeverityIdSignal;

        // Act
        Map<String, Integer> result = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                entityType, accountId, instanceId, kpiId, serviceId, severityId);

        // Assert
        assertEquals(60, result.get("collectionInterval")); // default value
        assertNull(result.get("persistence"));
        assertNull(result.get("suppression"));
    }

    @Test
    void testGetPersSuppAndCollectionIntervalAtServiceConf_InstanceKpiDetailsNull() {
        // Arrange
        String entityType = persistenceSuppression.entityTypeInstance;
        String accountId = "acc";
        String instanceId = "inst";
        String kpiId = "101";
        String serviceId = "svc";
        String severityId = persistenceSuppression.lowSeverityIdSignal;

        when(cacheWrapper.getInstanceKPIDetails(accountId, instanceId, Integer.parseInt(kpiId))).thenReturn(null);
        // Act
        Map<String, Integer> result = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                entityType, accountId, instanceId, kpiId, serviceId, severityId);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetPersSuppAndCollectionIntervalAtServiceConf_TransactionKpiDetailsNull() {
        // Arrange
        String entityType = persistenceSuppression.entityTypeTransaction;
        String accountId = "acc";
        String txnId = "txn";
        String kpiId = "101";
        String serviceId = "svc";
        String severityId = persistenceSuppression.lowSeverityIdSignal;

        when(cacheWrapper.getComponentKPIDetails(eq(accountId), eq(persistenceSuppression.transactionComponentIdentifier), eq(kpiId))).thenReturn(null);
        // Act
        Map<String, Integer> result = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                entityType, accountId, txnId, kpiId, serviceId, severityId);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetPersSuppAndCollectionIntervalAtServiceConf_ServiceConfThrowsException() {
        // Arrange
        String entityType = persistenceSuppression.entityTypeInstance;
        String accountId = "acc";
        String instanceId = "inst";
        String kpiId = "101";
        String serviceId = "svc";
        String severityId = persistenceSuppression.lowSeverityIdSignal;

        CompInstKpiEntity kpiDetails = mock(CompInstKpiEntity.class);
        when(cacheWrapper.getInstanceKPIDetails(accountId, instanceId, Integer.parseInt(kpiId))).thenReturn(kpiDetails);
        when(kpiDetails.getCollectionInterval()).thenReturn(80);
        doThrow(new RuntimeException("fail")).when(persistenceSuppression).getPersistenceSuppressionServiceConf(any(), any(), anyInt());

        // Act
        Map<String, Integer> result = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                entityType, accountId, instanceId, kpiId, serviceId, severityId);

        // Assert
        assertEquals(80, result.get("collectionInterval"));
        assertNull(result.get("persistence"));
        assertNull(result.get("suppression"));
    }

    @Test
    void testGetPersSuppAndCollectionIntervalAtServiceConf_ServiceConfNull() {
        // Arrange
        String entityType = persistenceSuppression.entityTypeInstance;
        String accountId = "acc";
        String instanceId = "inst";
        String kpiId = "101";
        String serviceId = "svc";
        String severityId = persistenceSuppression.lowSeverityIdSignal;

        CompInstKpiEntity kpiDetails = mock(CompInstKpiEntity.class);
        when(cacheWrapper.getInstanceKPIDetails(accountId, instanceId, Integer.parseInt(kpiId))).thenReturn(kpiDetails);
        when(kpiDetails.getCollectionInterval()).thenReturn(80);
        when(persistenceSuppression.getPersistenceSuppressionServiceConf(any(), any(), anyInt())).thenReturn(null);

        // Act
        Map<String, Integer> result = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                entityType, accountId, instanceId, kpiId, serviceId, severityId);

        // Assert
        assertEquals(80, result.get("collectionInterval"));
        assertNull(result.get("persistence"));
        assertNull(result.get("suppression"));
    }

    @Test
    void testGetPersSuppAndCollectionIntervalAtServiceConf_SeverityIdNull() {
        // Arrange
        String entityType = persistenceSuppression.entityTypeInstance;
        String accountId = "acc";
        String instanceId = "inst";
        String kpiId = "101";
        String serviceId = "svc";
        String severityId = null;

        CompInstKpiEntity kpiDetails = mock(CompInstKpiEntity.class);
        when(cacheWrapper.getInstanceKPIDetails(accountId, instanceId, Integer.parseInt(kpiId))).thenReturn(kpiDetails);
        when(kpiDetails.getCollectionInterval()).thenReturn(80);
        when(persistenceSuppression.getPersistenceSuppressionServiceConf(any(), any(), anyInt())).thenReturn(persistenceSuppressionConfiguration);

        // Act
        Map<String, Integer> result = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                entityType, accountId, instanceId, kpiId, serviceId, severityId);

        // Assert
        assertEquals(80, result.get("collectionInterval"));
        assertEquals(0, result.get("persistence"));
        assertEquals(0, result.get("suppression"));
    }

    @Test
    void testGetPersSuppAndCollectionIntervalAtServiceConf_SeverityIdNotMatching() {
        // Arrange
        String entityType = persistenceSuppression.entityTypeInstance;
        String accountId = "acc";
        String instanceId = "inst";
        String kpiId = "101";
        String serviceId = "svc";
        String severityId = "999";

        CompInstKpiEntity kpiDetails = mock(CompInstKpiEntity.class);
        when(cacheWrapper.getInstanceKPIDetails(accountId, instanceId, Integer.parseInt(kpiId))).thenReturn(kpiDetails);
        when(kpiDetails.getCollectionInterval()).thenReturn(80);
        when(persistenceSuppression.getPersistenceSuppressionServiceConf(any(), any(), anyInt())).thenReturn(persistenceSuppressionConfiguration);

        // Act
        Map<String, Integer> result = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                entityType, accountId, instanceId, kpiId, serviceId, severityId);

        // Assert
        assertEquals(80, result.get("collectionInterval"));
        assertEquals(0, result.get("persistence"));
        assertEquals(0, result.get("suppression"));
    }

    @Test
    void testGetPersSuppAndCollectionIntervalAtServiceConf_HighSeverity() {
        // Arrange
        String entityType = persistenceSuppression.entityTypeInstance;
        String accountId = "acc";
        String instanceId = "inst";
        String kpiId = "101";
        String serviceId = "svc";
        String severityId = persistenceSuppression.highSeverityIdSignal;

        CompInstKpiEntity kpiDetails = mock(CompInstKpiEntity.class);
        when(cacheWrapper.getInstanceKPIDetails(accountId, instanceId, Integer.parseInt(kpiId))).thenReturn(kpiDetails);
        when(kpiDetails.getCollectionInterval()).thenReturn(80);
        when(persistenceSuppression.getPersistenceSuppressionServiceConf(any(), any(), anyInt())).thenReturn(persistenceSuppressionConfiguration);

        // Act
        Map<String, Integer> result = persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(
                entityType, accountId, instanceId, kpiId, serviceId, severityId);

        // Assert
        assertEquals(80, result.get("collectionInterval"));
        assertEquals(6, result.get("persistence"));
        assertEquals(7, result.get("suppression"));
    }

    @Test
    void testApplyPersistenceSuppressionCreateUpdateAnomaly_ViolationDetailsNull() {
        ViolatedData violatedData = new ViolatedData("acc", Collections.singletonList("app1"));
        violatedData.setInstanceId("inst1");
        violatedData.setKpiId("101");
        violatedData.setKpiAttribute("ALL");
        violatedData.setEventType(ViolationEventType.KPI_VIOLATION);
        violatedData.setMetaData(new HashMap<>());
        CompInstKpiEntity kpiDetails = mock(CompInstKpiEntity.class);
        KpiCategoryDetails kpiCategoryDetails = mock(KpiCategoryDetails.class);
        kpiCategoryDetails.setIdentifier("1");
        kpiDetails.setIsInfo(0);
        kpiDetails.setCategoryDetails(kpiCategoryDetails);
        kpiDetails.setIdentifier("1");
        when(kpiDetails.getCategoryDetails()).thenReturn(kpiCategoryDetails);
        when(cacheWrapper.getInstanceKPIDetails(any(), any(), anyInt())).thenReturn(kpiDetails);
        violatedData.setViolationDetails(null);

        // Should return early, no anomaly created/updated
        persistenceSuppression.applyPersistenceSuppressionCreateUpdateAnomaly(violatedData);
        verify(anomalyManagementService, never()).createAnomaly(any(), any(), any(), any());
        verify(anomalyManagementService, never()).updateAnomaly(any(), any(), any(), any());
    }

    @Test
    void testApplyPersistenceSuppressionCreateUpdateAnomaly_ViolationStatusHighestSeverityNull() {
        ViolatedData violatedData = new ViolatedData("acc", Collections.singletonList("app1"));
        violatedData.setInstanceId("inst1");
        violatedData.setKpiId("101");
        violatedData.setKpiAttribute("ALL");
        violatedData.setEventType(ViolationEventType.TXN_VIOLATION);
        violatedData.setMetaData(new HashMap<>());
        ComponentKpiEntity componentKpiDetails = mock(ComponentKpiEntity.class);
        KpiCategoryDetails kpiCategoryDetails = mock(KpiCategoryDetails.class);
        kpiCategoryDetails.setIdentifier("1");
        componentKpiDetails.setIsInfo(0);
        componentKpiDetails.setCategoryDetails(kpiCategoryDetails);
        componentKpiDetails.setIdentifier("1");
        when(componentKpiDetails.getCategoryDetails()).thenReturn(kpiCategoryDetails);
        when(cacheWrapper.getComponentKPIDetails(any(), any(), any())).thenReturn(componentKpiDetails);
        ViolationDetails violationDetails = ViolationDetails.builder()
                .violationStatusMap(new HashMap<>())
                .highestViolatedSeverity("431")
                .build();
        violatedData.setViolationDetails(violationDetails);

        // Should return early, no anomaly created/updated
        persistenceSuppression.applyPersistenceSuppressionCreateUpdateAnomaly(violatedData);
        verify(anomalyManagementService, never()).createAnomaly(any(), any(), any(), any());
        verify(anomalyManagementService, never()).updateAnomaly(any(), any(), any(), any());
    }

    @Test
    void testApplyPersistenceSuppressionCreateUpdateAnomaly_ViolationCountLessThanPersistence() {
        ViolatedData violatedData = new ViolatedData("acc", Collections.singletonList("app1"));
        violatedData.setInstanceId("inst1");
        violatedData.setKpiId("101");
        violatedData.setKpiAttribute("ALL");
        violatedData.setServiceList(Collections.singletonList("svc1"));
        violatedData.setThresholdType("Static");
        violatedData.setOperationType("gt");
        violatedData.setViolationTime(123456789L);
        violatedData.setViolationFor("SOR");
        violatedData.setEventType(ViolationEventType.KPI_VIOLATION);
        violatedData.setKpiType(com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        Map<String, String> metaData = new HashMap<>();
        metaData.put("anomalyScore", "1.0");
        violatedData.setMetaData(metaData);
        CompInstKpiEntity kpiDetails = mock(CompInstKpiEntity.class);
        KpiCategoryDetails kpiCategoryDetails = mock(KpiCategoryDetails.class);
        kpiCategoryDetails.setIdentifier("1");
        kpiDetails.setIsInfo(0);
        kpiDetails.setCategoryDetails(kpiCategoryDetails);
        kpiDetails.setIdentifier("1");
        when(kpiDetails.getCategoryDetails()).thenReturn(kpiCategoryDetails);
        when(cacheWrapper.getInstanceKPIDetails(any(), any(), anyInt())).thenReturn(kpiDetails);
        ViolationStatus status = ViolationStatus.builder()
                .violationCount(1)
                .persistence(2)
                .suppression(3)
                .violationStartTime(1000L)
                .lastViolationTime(2000L)
                .build();
        Map<String, ViolationStatus> statusMap = new HashMap<>();
        statusMap.put("431", status);
        ViolationDetails violationDetails = ViolationDetails.builder()
                .violationStatusMap(statusMap)
                .highestViolatedSeverity("431")
                .violationLevel("INSTANCE")
                .build();
        violatedData.setViolationDetails(violationDetails);

        persistenceSuppression.applyPersistenceSuppressionCreateUpdateAnomaly(violatedData);
        verify(anomalyManagementService, never()).createAnomaly(any(), any(), any(), any());
        verify(anomalyManagementService, never()).updateAnomaly(any(), any(), any(), any());
    }

    @Test
    void testApplyPersistenceSuppressionCreateUpdateAnomaly_ViolationCountEqualsPersistence() {
        ViolatedData violatedData = new ViolatedData("acc", Collections.singletonList("app1"));
        violatedData.setInstanceId("inst1");
        violatedData.setKpiId("101");
        violatedData.setKpiAttribute("ALL");
        violatedData.setServiceList(Collections.singletonList("svc1"));
        violatedData.setThresholdType("Static");
        violatedData.setOperationType("gt");
        violatedData.setViolationTime(123456789L);
        violatedData.setViolationFor("SOR");
        violatedData.setEventType(ViolationEventType.KPI_VIOLATION);
        violatedData.setKpiType(com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        Map<String, String> metaData = new HashMap<>();
        metaData.put("anomalyScore", "1.0");
        violatedData.setMetaData(metaData);
        CompInstKpiEntity kpiDetails = mock(CompInstKpiEntity.class);
        KpiCategoryDetails kpiCategoryDetails = mock(KpiCategoryDetails.class);
        kpiCategoryDetails.setIdentifier("1");
        kpiDetails.setIsInfo(0);
        kpiDetails.setCategoryDetails(kpiCategoryDetails);
        kpiDetails.setIdentifier("1");
        when(kpiDetails.getCategoryDetails()).thenReturn(kpiCategoryDetails);
        when(cacheWrapper.getInstanceKPIDetails(any(), any(), anyInt())).thenReturn(kpiDetails);
        ViolationStatus status = ViolationStatus.builder()
                .violationCount(2)
                .persistence(2)
                .suppression(3)
                .violationStartTime(1000L)
                .lastViolationTime(2000L)
                .build();
        Map<String, ViolationStatus> statusMap = new HashMap<>();
        statusMap.put("431", status);
        ViolationDetails violationDetails = ViolationDetails.builder()
                .violationStatusMap(statusMap)
                .highestViolatedSeverity("431")
                .violationLevel("INSTANCE")
                .build();
        violatedData.setViolationDetails(violationDetails);
        AnomalyAccountPojo anomalyAccountPojo = mock(AnomalyAccountPojo.class);
        when(anomalyManagementService.createAnomaly(any(), any(), any(), any())).thenReturn(anomalyAccountPojo);

        persistenceSuppression.applyPersistenceSuppressionCreateUpdateAnomaly(violatedData);
        verify(anomalyManagementService, times(1)).createAnomaly(any(), any(), any(), any());
        verify(anomalyManagementService, never()).updateAnomaly(any(), any(), any(), any());
    }

    @Test
    void testApplyPersistenceSuppressionCreateUpdateAnomaly_ViolationCountGreaterThanPersistence() {
        ViolatedData violatedData = new ViolatedData("acc", Collections.singletonList("app1"));
        violatedData.setInstanceId("inst1");
        violatedData.setKpiId("101");
        violatedData.setKpiAttribute("ALL");
        violatedData.setServiceList(Collections.singletonList("svc1"));
        violatedData.setThresholdType("Static");
        violatedData.setOperationType("gt");
        violatedData.setViolationTime(123456789L);
        violatedData.setViolationFor("SOR");
        violatedData.setEventType(ViolationEventType.KPI_VIOLATION);
        violatedData.setKpiType(com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        Map<String, String> metaData = new HashMap<>();
        metaData.put("anomalyScore", "1.0");
        violatedData.setMetaData(metaData);
        CompInstKpiEntity kpiDetails = mock(CompInstKpiEntity.class);
        KpiCategoryDetails kpiCategoryDetails = mock(KpiCategoryDetails.class);
        kpiCategoryDetails.setIdentifier("1");
        kpiDetails.setIsInfo(0);
        kpiDetails.setCategoryDetails(kpiCategoryDetails);
        kpiDetails.setIdentifier("1");
        when(kpiDetails.getCategoryDetails()).thenReturn(kpiCategoryDetails);
        when(cacheWrapper.getInstanceKPIDetails(any(), any(), anyInt())).thenReturn(kpiDetails);
        ViolationStatus status = ViolationStatus.builder()
                .violationCount(7)
                .persistence(2)
                .suppression(3)
                .violationStartTime(1000L)
                .lastViolationTime(2000L)
                .build();
        Map<String, ViolationStatus> statusMap = new HashMap<>();
        statusMap.put("431", status);
        ViolationDetails violationDetails = ViolationDetails.builder()
                .violationStatusMap(statusMap)
                .highestViolatedSeverity("431")
                .violationLevel("INSTANCE")
                .build();
        violatedData.setViolationDetails(violationDetails);

        AnomalyAccountPojo anomalyAccountPojo = mock(AnomalyAccountPojo.class);
        when(anomalyManagementService.updateAnomaly(any(), any(), any(), any())).thenReturn(anomalyAccountPojo);

        AnomalyAccountPojo result = persistenceSuppression.applyPersistenceSuppressionCreateUpdateAnomaly(violatedData);
        assertEquals(result, anomalyAccountPojo);
        verify(anomalyManagementService, never()).createAnomaly(any(), any(), any(), any());
        verify(anomalyManagementService, times(1)).updateAnomaly(any(), any(), any(), any());
    }

    @Test
    void testGetServiceConfiguration_ServiceNull() {
        // Arrange
        String accountId = "acc";
        String serviceId = "svc";
        when(cacheWrapper.getServiceDetails(accountId, serviceId)).thenReturn(null);

        // Act
        ServiceConfiguration result = persistenceSuppression.getServiceConfiguration(accountId, serviceId);

        // Assert
        assertNull(result);
    }

    @Test
    void testGetServiceConfiguration_ServiceConfigurationNull() {
        // Arrange
        String accountId = "acc";
        String serviceId = "svc";
        com.heal.configuration.pojos.Service service = mock(com.heal.configuration.pojos.Service.class);
        when(service.getServiceConfiguration()).thenReturn(null);
        when(cacheWrapper.getServiceDetails(accountId, serviceId)).thenReturn(service);

        // Act
        ServiceConfiguration result = persistenceSuppression.getServiceConfiguration(accountId, serviceId);

        // Assert
        assertNull(result);
    }

    @Test
    void testGetServiceConfiguration_ServiceConfigurationPresent() {
        // Arrange
        String accountId = "acc";
        String serviceId = "svc";
        ServiceConfiguration serviceConfiguration = mock(ServiceConfiguration.class);
        com.heal.configuration.pojos.Service service = mock(com.heal.configuration.pojos.Service.class);
        when(service.getServiceConfiguration()).thenReturn(serviceConfiguration);
        when(cacheWrapper.getServiceDetails(accountId, serviceId)).thenReturn(service);

        // Act
        ServiceConfiguration result = persistenceSuppression.getServiceConfiguration(accountId, serviceId);

        // Assert
        assertEquals(serviceConfiguration, result);
    }

    @Test
    void testGetPersistenceSuppressionServiceConf_ServiceConfigurationNull() {
        // Arrange
        String accountId = "acc";
        String serviceId = "svc";
        int collectionInterval = 120;
        when(persistenceSuppression.getServiceConfiguration(accountId, serviceId)).thenReturn(null);

        // Act
        PersistenceSuppressionConfiguration result = persistenceSuppression.getPersistenceSuppressionServiceConf(accountId, serviceId, collectionInterval);

        // Assert
        assertNull(result);
    }

    @Test
    void testGetPersistenceSuppressionServiceConf_NoMatchingConfig() {
        // Arrange
        String accountId = "acc";
        String serviceId = "svc";
        int collectionInterval = 120;
        ServiceConfiguration serviceConfiguration = mock(ServiceConfiguration.class);
        List<PersistenceSuppressionConfiguration> configs = new ArrayList<>();
        PersistenceSuppressionConfiguration conf = mock(PersistenceSuppressionConfiguration.class);
        when(conf.getStartCollectionInterval()).thenReturn(3);
        when(conf.getEndCollectionInterval()).thenReturn(4);
        configs.add(conf);
        when(serviceConfiguration.getPersistenceSuppressionConfigurations()).thenReturn(configs);
        when(persistenceSuppression.getServiceConfiguration(accountId, serviceId)).thenReturn(serviceConfiguration);

        // Act
        PersistenceSuppressionConfiguration result = persistenceSuppression.getPersistenceSuppressionServiceConf(accountId, serviceId, collectionInterval);

        // Assert
        assertNull(result);
    }

    @Test
    void testGetPersistenceSuppressionServiceConf_MatchingConfig() {
        // Arrange
        String accountId = "acc";
        String serviceId = "svc";
        int collectionInterval = 180; // 3 minutes
        ServiceConfiguration serviceConfiguration = mock(ServiceConfiguration.class);
        List<PersistenceSuppressionConfiguration> configs = new ArrayList<>();
        PersistenceSuppressionConfiguration conf = mock(PersistenceSuppressionConfiguration.class);
        when(conf.getStartCollectionInterval()).thenReturn(2);
        when(conf.getEndCollectionInterval()).thenReturn(4);
        configs.add(conf);
        when(serviceConfiguration.getPersistenceSuppressionConfigurations()).thenReturn(configs);
        when(persistenceSuppression.getServiceConfiguration(accountId, serviceId)).thenReturn(serviceConfiguration);

        // Act
        PersistenceSuppressionConfiguration result = persistenceSuppression.getPersistenceSuppressionServiceConf(accountId, serviceId, collectionInterval);

        // Assert
        assertEquals(conf, result);
    }
}
