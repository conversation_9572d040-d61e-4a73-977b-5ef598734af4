package com.heal.event.detector.core;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.Alerts;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.cache.CacheWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Instant;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CreateAnomalyManagementServiceTest {

    @InjectMocks
    private AnomalyManagementService anomalyService;

    @Mock
    private RedisUtilities redisUtilities;

    @Mock
    private CacheWrapper cacheWrapper;

    @Mock
    private Account account;

    @Mock
    private Transaction transaction;

    @Mock
    private CompInstClusterDetails compInstClusterDetails;

    @Mock
    private HealthMetrics metrics;

    private AnomalyAccountPojo anomalyAccountPojo;

    private Alerts alerts;

    private String json;

    private ViolationDetails violationDetails;

    @BeforeEach
    void setup() throws JsonProcessingException {
        ReflectionTestUtils.setField(anomalyService, "entityTypeInstance", "INSTANCE");
        ReflectionTestUtils.setField(anomalyService, "entityTypeTransaction", "TRANSACTION");

        anomalyAccountPojo = new AnomalyAccountPojo();
        anomalyAccountPojo.setAccountIdentifier("demo");
        Anomalies anomalies = new Anomalies();
        anomalies.setAnomalyStartTime(1656634020000L);
        anomalies.setAnomalyEndTime(1656634020000L);
        anomalies.setAnomalyCreatedTime(1656634020000L);
        anomalies.setLastAlertTime(1656634020000L);
        anomalies.setAnomalyStatus("Open");
        anomalies.setIdentifiedTime(1656634297733L);
        anomalies.setEntityType("ComponentInstance");
        anomalies.setEntityId("<entityIdentifier>");
        anomalies.setStartSeverityId(431);
        anomalies.setLastSeverityId(431);
        anomalies.setCategoryId("Disk IO");
        anomalies.setKpiAttribute("ALL");
        anomalies.setKpiId(16);
        anomalies.setKpiIdentifier("DEVICE_BUSY");
        anomalies.setServiceId(Collections.singleton("CC_Service"));

// Metadata
        Map<String, String> metadata = new HashMap<>();
        metadata.put("isMaintenanceExcluded", "0");
        metadata.put("violationLevel", "SERVICE");
        metadata.put("serviceIdentifier", "CC_Service");
        metadata.put("isInformatic", "0");
        metadata.put("kpiType", "Core");
        metadata.put("attributeName", "dm-2");
        metadata.put("anomalyLevel", "SERVICE");
        metadata.put("closingWindow", "3");
        metadata.put("maxDataBreaks", "30");
        metadata.put("remaindersCount", String.valueOf(0));
        metadata.put("closeWindowResetCount", String.valueOf(0));
        metadata.put("databreakResetCount", String.valueOf(0));
        anomalies.setMetadata(metadata);

        // Additional fields
        anomalies.setSeveritiesEnabled(Set.of("431", "432", "233"));
        anomalies.setViolationCountsReset(0);
        anomalies.setOperationType("lesser than");
        anomalies.setSignalIds(Set.of("E-2-16-3-**********"));
        anomalies.setThresholdType("Static");
        anomalies.setClosingReason("Violations are stopped/Maintenance window/Data collection stopped");
        anomalies.setTimestamp(String.valueOf(Instant.parse("2022-07-01T00:07:00.000Z")));

        anomalyAccountPojo.setAnomalyDetails(anomalies);

        alerts = new Alerts();
        alerts.setAnomalyId("AE-2-12-16-T-S-dm-2-********");
        alerts.setAlertTime(1656634020000L); // Persistence/Suppression meet time
        alerts.setAlertStatus("Open");
        alerts.setIdentifiedTime(1656634297733L);
        alerts.setEntityType("ComponentInstance");
        alerts.setEntityId("<entityIdentifier>"); // replace with actual entity ID
        alerts.setSeverityId("431"); // Low
        alerts.setCategoryId("Disk IO");
        alerts.setKpiAttribute("dm-2");
        alerts.setKpiId(16);
        alerts.setKpiIdentifier("DEVICE_BUSY");
        alerts.setServiceId(Set.of("CC_Service"));

        Map<String, String> alertsMetadata = new HashMap<>();
        alertsMetadata.put("isMaintenanceExcluded", "0");
        alertsMetadata.put("violationLevel", "SERVICE");
        alertsMetadata.put("isInformatic", "0");
        alertsMetadata.put("kpiType", "Core");
        alertsMetadata.put("attributeName", "dm-2");
        alerts.setMetadata(alertsMetadata);

        alerts.setOperationType("lesser than");
        alerts.setThresholdType("Static");
        alerts.setPersistence("3");
        alerts.setSuppression("5");
        alerts.setAlertType("Persistence");

        Map<String, Double> thresholds = new HashMap<>();
        thresholds.put("Upper", 19.0);
        thresholds.put("Lower", 0.0);
        alerts.setThresholds(thresholds);

        alerts.setAnomalyScore("0.0");
        alerts.setValue("0.019999999552965164");
        alerts.setTimestamp("2022-07-01T00:07:00.000Z");

        json = """
                {
                  "violationStatusMap": {
                    "431": {
                      "violationCount": 1,
                      "violationStartTime": 1749211440000,
                      "lastViolationTime": 1749211440000,
                      "persistence": 3,
                      "suppression": 2,
                      "thresholds": {
                        "Upper": 0,
                        "Lower": 65
                      },
                      "operationName": "greater than"
                    }
                  },
                  "anomalyEventStatus": null,
                  "violationLevel": "INSTANCE",
                  "highestViolatedSeverity": "431"
                }
                """;

        ObjectMapper objectMapper = new ObjectMapper();
        violationDetails = objectMapper.readValue(json, ViolationDetails.class);
    }

    @Test
    public void createAnomaly_ShouldSucceed_WhenInstanceIdIsAvailable() {
        List<String> appIds = List.of("app-123");

        when(cacheWrapper.getAccountDetails(anyString())).thenReturn(account);
        when(cacheWrapper.getInstanceDetails(anyString(), anyString())).thenReturn(compInstClusterDetails);

        doNothing().when(metrics).updateAnomalyCreateCount(anyInt());
        doNothing().when(metrics).updateAlertCount(anyInt());

        // Act
        AnomalyAccountPojo createAnomalyAccountPojo = anomalyService.createAnomaly(anomalyAccountPojo, alerts, appIds, violationDetails);

        // Assert
        assertNotNull(createAnomalyAccountPojo);
        assertEquals("Open", createAnomalyAccountPojo.getAnomalyDetails().getAnomalyStatus());
    }

    @Test
    public void createAnomaly_ShouldSucceed_WhenTransactionIdIsAvailable() throws JsonProcessingException {
        List<String> appIds = List.of("app-123");
        anomalyAccountPojo.getAnomalyDetails().setEntityType("TRANSACTION");

        when(cacheWrapper.getAccountDetails(anyString())).thenReturn(account);
        when(cacheWrapper.getTransactionDetails(anyString(), anyString())).thenReturn(transaction);

        // Act
        AnomalyAccountPojo createAnomalyAccountPojo = anomalyService.createAnomaly(anomalyAccountPojo, alerts, appIds, violationDetails);

        // Assert
        assertNotNull(createAnomalyAccountPojo);
        assertEquals("Open", createAnomalyAccountPojo.getAnomalyDetails().getAnomalyStatus());
    }

    @Test
    public void createAnomaly_ShouldSucceed_WhenAccountIsNull() throws JsonProcessingException {
        List<String> appIds = List.of("app-123");

        when(cacheWrapper.getAccountDetails(anyString())).thenReturn(null);

        // Act
        AnomalyAccountPojo createAnomalyAccountPojo = anomalyService.createAnomaly(anomalyAccountPojo, alerts, appIds, violationDetails);

        // Assert
        assertNotNull(createAnomalyAccountPojo);
        assertEquals("Open", createAnomalyAccountPojo.getAnomalyDetails().getAnomalyStatus());
    }

    @Test
    public void createAnomaly_ShouldSucceed_WhenViolationDetailsIsNull() {
        List<String> appIds = List.of("app-123");

        when(cacheWrapper.getAccountDetails(anyString())).thenReturn(account);
        when(cacheWrapper.getInstanceDetails(anyString(), anyString())).thenReturn(compInstClusterDetails);

        // Act
        AnomalyAccountPojo createAnomalyAccountPojo = anomalyService.createAnomaly(anomalyAccountPojo, alerts, appIds, null);

        // Assert
        assertNotNull(createAnomalyAccountPojo);
        assertEquals("Open", createAnomalyAccountPojo.getAnomalyDetails().getAnomalyStatus());
    }
}