package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.appnomic.appsone.common.protbuf.PSAgentMessageProtos;
import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.event.detector.config.OpenSearchConfig;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.scheduler.OSDataPushScheduler;
import com.heal.event.detector.util.ProtoCreator;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Spy;
import org.opensearch.client.opensearch.core.SearchResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@SpringBootTest
public class AnomalyEventsProcessIT {

    @Spy
    AnomalyEventsProcess anomalyEventsProcess;

    @Autowired
    ProtoCreator protoCreator;

    @Autowired
    GetViolatedData getViolatedData;

    @Autowired
    PersistenceSuppression persistenceSuppression;

    @Autowired
    PerSuppProcessWatcherKPI perSuppProcessWatcherKPI;

    @Autowired
    RedisTemplate<String, Object> redisTemplate;

    @Spy
    PrepareAnomalyData prepareAnomalyData;

    @Autowired
    ObjectMapper objectMapper;

    @Spy
    OpenSearchRepo openSearchRepo;

    @Spy
    OpenSearchConfig openSearchConfig;

    @Value("${opensearch.kpi.violations.index:integration_test_ed_kpi_violations}")
    String rawKpisViolationsIndex;

    @Value("${opensearch.transaction.violations.index:integration_test_ed_transaction_violations}")
    String rawTransactionsViolationsIndex;

    @Value("${opensearch.batchjob.violations.index:integration_test_ed_batch_job_violations}")
    String rawBatchJobViolationsIndex;

    @Value("${opensearch.anomalies.index:integration_test_ed_anomalies}")
    String anomaliesIndex;

    @Value("${opensearch.kpi.thresholds.index:integration_test_ed_kpis_thresholds}")
    String kpiThresholdsIndex;

    @Value("${opensearch.transaction.thresholds.index:integration_test_ed_transactions_thresholds}")
    String transactionThresholdsIndex;

    @Autowired
    OSDataPushScheduler scheduler;

    @BeforeEach
    public void initialize() {

        anomalyEventsProcess.persistenceSuppression = persistenceSuppression;
        anomalyEventsProcess.perSuppProcessWatcherKPI = perSuppProcessWatcherKPI;

        openSearchRepo.rawKpisViolationsIndex = rawKpisViolationsIndex;
        openSearchRepo.rawTransactionsViolationsIndex = rawTransactionsViolationsIndex;
        openSearchRepo.rawBatchJobViolationsIndex = rawBatchJobViolationsIndex;
        openSearchRepo.anomaliesIndex = anomaliesIndex;
        openSearchRepo.kpiThresholdsIndex = kpiThresholdsIndex;
        openSearchRepo.transactionThresholdsIndex = transactionThresholdsIndex;
        openSearchRepo.objectMapper = objectMapper;
//        scheduler.isTestCase = true;
        openSearchRepo.scheduler = scheduler;
        anomalyEventsProcess.openSearchRepo = openSearchRepo;

        anomalyEventsProcess.prepareAnomalyData = prepareAnomalyData;
        anomalyEventsProcess.outOfOrderValue = 5;

    }

    @AfterEach
    public void cleanUp() {

        OpenSearchClient client = openSearchConfig.getOpenSearchClient("", rawKpisViolationsIndex);
        try {
            client.indices().delete(new org.opensearch.client.opensearch.indices.DeleteIndexRequest.Builder().index("integration_test_ed_*").allowNoIndices(true).build());
        } catch (Exception e) {
            log.warn("Couldn't find any OS index with pattern integration_test_ed_*.");
        }

    }

    private org.opensearch.client.opensearch.core.SearchResponse<?> searchOS(OpenSearchClient client, String indexPrefix, String accountId) {
        org.opensearch.client.opensearch.core.SearchRequest searchRequest = new org.opensearch.client.opensearch.core.SearchRequest.Builder()
                .index(indexPrefix + "_" + accountId + "_" + Utils.getDateForOSIndex())
                .ignoreUnavailable(true)
                .size(10000)
                .build();
        try {
            return client.search(searchRequest, Object.class);
        } catch (Exception e) {
            log.error("Exception while searching for index data. ", e);
            return null;
        }
    }

    @Test
    public void processViolatedKpiData_kpiInfoType_invalidAccountId() {

        String accountIdentifier = "Sample_Acc_" + RandomStringUtils.random(16, true, true);
        String kpiId = "24";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DC";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "NB-Web-App", "NB-Web-Service-DR", instanceIdentifier,
                        kpiId, "ALL",
                        "60.0", 19800, "Static",
                        "greater than", "Severe", protoCreator.thresholdMapProvider());

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);

        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedData, true);
        assert violatedData.isEmpty();
        assert anomalyEventList.isEmpty();

    }

    @Test
    public void processViolatedKpiData_kpiInfoType_invalidInstanceId() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "24";
        String instanceIdentifier = "Sample_Inst_" + RandomStringUtils.random(16, true, true);

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "NB-Web-App", "NB-Web-Service-DR", instanceIdentifier,
                        kpiId, "ALL",
                        "60.0", 19800, "Static",
                        "greater than", "Severe", protoCreator.thresholdMapProvider());

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);

        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedData, true);
        assert violatedData.isEmpty();
        assert anomalyEventList.isEmpty();

    }

    @Test
    public void processViolatedKpiData_kpiInfoType_invalidServiceId() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "24";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DC";
        String serviceIdentifier = "Sample_Serv_" + RandomStringUtils.random(16, true, true);

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier,
                        "NB-Web-App", serviceIdentifier, instanceIdentifier,
                        kpiId, "ALL",
                        "60.0", 19800, "Static",
                        "greater than", "Severe", protoCreator.thresholdMapProvider());

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);

        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedData, true);
        assert violatedData.isEmpty();
        assert anomalyEventList.isEmpty();

    }

    @Test
    public void processViolatedKpiData_txnInfoType_invalidAccountId() {

        String accountIdentifier = "Sample_Acc_" + RandomStringUtils.random(16, true, true);
        String kpiId = "24";
        String transactionIdentifier = "fldTxnMNU_P";
        String grpId = "5";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventTxnInfoType(accountIdentifier,
                        "NB-Web-App", "NB-Web-Service-DR", transactionIdentifier,
                        kpiId, PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC,
                        "60.0", grpId, 19800, "Static",
                        "greater than", "Severe", protoCreator.thresholdMapProvider());

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);

        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedData, true);
        assert violatedData.isEmpty();
        assert anomalyEventList.isEmpty();

    }

    @Test
    public void processViolatedKpiData_txnInfoType_invalidServiceId() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "24";
        String transactionIdentifier = "fldTxnMNU_P";
        String grpId = "5";
        String serviceIdentifier = "Sample_Serv_" + RandomStringUtils.random(16, true, true);

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventTxnInfoType(accountIdentifier,
                        "NB-Web-App", serviceIdentifier, transactionIdentifier,
                        kpiId, PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC,
                        "60.0", grpId, 19800, "Static",
                        "greater than", "Severe", protoCreator.thresholdMapProvider());

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);

        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedData, true);
        assert violatedData.isEmpty();
        assert anomalyEventList.isEmpty();

    }

    @Test
    public void processViolatedKpiData_txnInfoType_invalidTransactionId() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "24";
        String transactionIdentifier = "Sample_transaction_" + RandomStringUtils.random(16, true, true);
        String grpId = "5";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventTxnInfoType(accountIdentifier,
                        "NB-Web-App", "NB-Web-Service-DR", transactionIdentifier,
                        kpiId, PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC,
                        "60.0", grpId, 19800, "Static",
                        "greater than", "Severe", protoCreator.thresholdMapProvider());

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);

        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedData, true);
        assert violatedData.isEmpty();
        assert anomalyEventList.isEmpty();

    }

    @Test
    public void processViolatedKpiData_batchJobInfoType_invalidAccountId() {

        String accountIdentifier = "Sample_Acc_" + RandomStringUtils.random(16, true, true);
        String kpiId = "24";
        String batchJobIdentifier = "random";

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventBatchJobInfoType(accountIdentifier,
                        "NB-Web-App", batchJobIdentifier,
                        kpiId, "60.0", 19800, "Static",
                        "greater than", "Severe", protoCreator.thresholdMapProvider(),
                        protoCreator.metadataMapProvider());

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);

        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedData, true);
        assert violatedData.isEmpty();
        assert anomalyEventList.isEmpty();

    }


    @Test
    public void processViolatedKpiData_kpiInfoType_nonGrp_instLvlPerSupValueNonNull_noAnomalyGen() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "8";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DR";
        String attributeName = "ALL";
        String serviceIdentifier = "ENET-App-Service-DR";
        String applicationIdentifier = "enet_3_DR";
        String redisKey = "/eventDetector/" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        String redisHashKey = "EVENTDETECTOR_" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

        Map<String, Double> thresholdMap = new HashMap<>();
        thresholdMap.put("Upper", 0.0);
        thresholdMap.put("Lower", 50.0);
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier, applicationIdentifier,
                        serviceIdentifier, instanceIdentifier, kpiId, attributeName,
                        "80.0", 19800, "Static",
                        "greater than", "Severe", thresholdMap);

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);
        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedData, true);
        scheduler.pushToOSBulkIndexing();
        SearchResponse<?> response = searchOS(openSearchConfig.getOpenSearchClient("", rawKpisViolationsIndex), rawKpisViolationsIndex, accountIdentifier);

        assert violatedData.size() == 1;
        assert anomalyEventList.isEmpty();
        assert response != null;

        if (!violatedData.isEmpty()) {
            for (ViolatedData vd : violatedData) {
                assert vd.getViolationDetails() != null : "violationDetails should be set on ViolatedData";
            }
        }

        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

    }

    @Test
    public void processViolatedKpiData_kpiInfoType_nonGrp_instLvlPerSupValueNonNull_AnomalyGen() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "8";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DR";
        String attributeName = "ALL";
        String serviceIdentifier = "ENET-App-Service-DR";
        String applicationIdentifier = "enet_3_DR";
        int noOfIteration = 5;
        String redisKey = "/eventDetector/" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        String redisHashKey = "EVENTDETECTOR_" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

        Map<String, Double> thresholdMap = new HashMap<>();
        thresholdMap.put("Upper", 0.0);
        thresholdMap.put("Lower", 50.0);
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier, applicationIdentifier,
                        serviceIdentifier, instanceIdentifier, kpiId, attributeName,
                        "80.0", 19800, "Static",
                        "greater than", "Severe", thresholdMap);

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);
        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = new ArrayList<>();
        for (int i = 0; i < noOfIteration; i++) {
            anomalyEventList.addAll(anomalyEventsProcess.processViolatedKpiData(violatedData, true));
        }
        scheduler.pushToOSBulkIndexing();
        OpenSearchClient client = openSearchConfig.getOpenSearchClient("", rawKpisViolationsIndex);
        SearchResponse<?> rawKpiResponse = searchOS(client, rawKpisViolationsIndex, accountIdentifier);
        SearchResponse<?> anomaliesResponse = searchOS(client, anomaliesIndex, accountIdentifier);

        assert violatedData.size() == 1;
        assert anomalyEventList.size() == 1;
        assert rawKpiResponse != null;
        assert anomaliesResponse != null;

        if (!violatedData.isEmpty()) {
            for (ViolatedData vd : violatedData) {
                assert vd.getViolationDetails() != null : "violationDetails should be set on ViolatedData";
            }
        }

        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

    }

    @Test
    public void processViolatedKpiData_kpiInfoType_Grp_instLvlPerSupValueNonNull_noAnomalyGen() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "18";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DR";
        String attributeName = "ALL";
        String serviceIdentifier = "ENET-App-Service-DR";
        String applicationIdentifier = "enet_3_DR";
        String redisKey = "/eventDetector/" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        String redisHashKey = "EVENTDETECTOR_" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

        Map<String, Double> thresholdMap = new HashMap<>();
        thresholdMap.put("Upper", 0.0);
        thresholdMap.put("Lower", 1000.0);
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier, applicationIdentifier,
                        serviceIdentifier, instanceIdentifier, kpiId, attributeName,
                        "1024.0", 19800, "Static",
                        "greater than", "Severe", thresholdMap);

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);
        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedData, true);
        scheduler.pushToOSBulkIndexing();
        SearchResponse<?> response = searchOS(openSearchConfig.getOpenSearchClient("", rawKpisViolationsIndex), rawKpisViolationsIndex, accountIdentifier);

        assert violatedData.size() == 1;
        assert anomalyEventList.isEmpty();
        assert response != null;

        if (!violatedData.isEmpty()) {
            for (ViolatedData vd : violatedData) {
                assert vd.getViolationDetails() != null : "violationDetails should be set on ViolatedData";
            }
        }

        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

    }

    @Test
    public void processViolatedKpiData_kpiInfoType_Grp_instLvlPerSupValueNonNull_AnomalyGen() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "18";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DR";
        String attributeName = "ALL";
        String serviceIdentifier = "ENET-App-Service-DR";
        String applicationIdentifier = "enet_3_DR";
        int noOfIteration = 3;
        String redisKey = "/eventDetector/" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        String redisHashKey = "EVENTDETECTOR_" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

        Map<String, Double> thresholdMap = new HashMap<>();
        thresholdMap.put("Upper", 0.0);
        thresholdMap.put("Lower", 1000.0);
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier, applicationIdentifier,
                        serviceIdentifier, instanceIdentifier, kpiId, attributeName,
                        "1024.0", 19800, "Static",
                        "greater than", "Severe", thresholdMap);

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);
        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = new ArrayList<>();
        for (int i = 0; i < noOfIteration; i++) {
            anomalyEventList.addAll(anomalyEventsProcess.processViolatedKpiData(violatedData, true));
        }
        scheduler.pushToOSBulkIndexing();
        OpenSearchClient client = openSearchConfig.getOpenSearchClient("", rawKpisViolationsIndex);
        SearchResponse<?> rawKpiResponse = searchOS(client, rawKpisViolationsIndex, accountIdentifier);
        SearchResponse<?> anomaliesResponse = searchOS(client, anomaliesIndex, accountIdentifier);

        assert violatedData.size() == 1;
        assert anomalyEventList.size() == 1;
        assert rawKpiResponse != null;
        assert anomaliesResponse != null;

        if (!violatedData.isEmpty()) {
            for (ViolatedData vd : violatedData) {
                assert vd.getViolationDetails() != null : "violationDetails should be set on ViolatedData";
            }
        }

        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

    }

    @Test
    public void processViolatedKpiData_kpiInfoType_nonGrp_instLvlPerSupValueNull_noAnomalyGen() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "1";
        String instanceIdentifier = "WINDOWS_54_Host-DC";
        String attributeName = "ALL";
        String serviceIdentifier = "IIS-Web-Service-DC";
        String applicationIdentifier = "microbanking_1_DC";
        String redisKey = "/eventDetector/" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        String redisHashKey = "EVENTDETECTOR_" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

        Map<String, Double> thresholdMap = new HashMap<>();
        thresholdMap.put("Upper", 0.0);
        thresholdMap.put("Lower", 70.0);
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier, applicationIdentifier,
                        serviceIdentifier, instanceIdentifier, kpiId, attributeName,
                        "80.0", 19800, "Static",
                        "greater than", "Severe", thresholdMap);

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);
        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedData, true);
        scheduler.pushToOSBulkIndexing();
        SearchResponse<?> response = searchOS(openSearchConfig.getOpenSearchClient("", rawKpisViolationsIndex), rawKpisViolationsIndex, accountIdentifier);

        assert violatedData.size() == 1;
        assert anomalyEventList.isEmpty();
        assert response != null;

        if (!violatedData.isEmpty()) {
            for (ViolatedData vd : violatedData) {
                assert vd.getViolationDetails() != null : "violationDetails should be set on ViolatedData";
            }
        }

        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

    }

    @Test
    public void processViolatedKpiData_kpiInfoType_nonGrp_instLvlPerSupValueNull_AnomalyGen() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "1";
        String instanceIdentifier = "WINDOWS_54_Host-DC";
        String attributeName = "ALL";
        String serviceIdentifier = "IIS-Web-Service-DC";
        String applicationIdentifier = "microbanking_1_DC";
        int noOfIteration = 6;
        String redisKey = "/eventDetector/" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        String redisHashKey = "EVENTDETECTOR_" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

        Map<String, Double> thresholdMap = new HashMap<>();
        thresholdMap.put("Upper", 0.0);
        thresholdMap.put("Lower", 70.0);
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier, applicationIdentifier,
                        serviceIdentifier, instanceIdentifier, kpiId, attributeName,
                        "80.0", 19800, "Static",
                        "greater than", "Severe", thresholdMap);

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);
        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = new ArrayList<>();
        for (int i = 0; i < noOfIteration; i++) {
            anomalyEventList.addAll(anomalyEventsProcess.processViolatedKpiData(violatedData, true));
        }
        scheduler.pushToOSBulkIndexing();
        OpenSearchClient client = openSearchConfig.getOpenSearchClient("", rawKpisViolationsIndex);
        SearchResponse<?> rawKpiResponse = searchOS(client, rawKpisViolationsIndex, accountIdentifier);
        SearchResponse<?> anomaliesResponse = searchOS(client, anomaliesIndex, accountIdentifier);

        assert violatedData.size() == 1;
        assert anomalyEventList.size() == 1;
        assert rawKpiResponse != null;
        assert anomaliesResponse != null;

        if (!violatedData.isEmpty()) {
            for (ViolatedData vd : violatedData) {
                assert vd.getViolationDetails() != null : "violationDetails should be set on ViolatedData";
            }
        }

        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

    }

    @Test
    public void processViolatedKpiData_kpiInfoType_Grp_instLvlPerSupValueNull_noAnomalyGen() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "18";
        String instanceIdentifier = "WINDOWS_54_Host-DC";
        String attributeName = "ALL";
        String serviceIdentifier = "IIS-Web-Service-DC";
        String applicationIdentifier = "microbanking_1_DC";
        String redisKey = "/eventDetector/" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        String redisHashKey = "EVENTDETECTOR_" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

        Map<String, Double> thresholdMap = new HashMap<>();
        thresholdMap.put("Upper", 0.0);
        thresholdMap.put("Lower", 60.0);
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier, applicationIdentifier,
                        serviceIdentifier, instanceIdentifier, kpiId, attributeName,
                        "80.0", 19800, "Static",
                        "greater than", "Severe", thresholdMap);

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);
        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedData, true);
        scheduler.pushToOSBulkIndexing();
        SearchResponse<?> response = searchOS(openSearchConfig.getOpenSearchClient("", rawKpisViolationsIndex), rawKpisViolationsIndex, accountIdentifier);

        assert violatedData.size() == 1;
        assert anomalyEventList.isEmpty();
        assert response != null;

        if (!violatedData.isEmpty()) {
            for (ViolatedData vd : violatedData) {
                assert vd.getViolationDetails() != null : "violationDetails should be set on ViolatedData";
            }
        }

        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

    }

    @Test
    public void processViolatedKpiData_kpiInfoType_Grp_instLvlPerSupValueNull_AnomalyGen() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "18";
        String instanceIdentifier = "WINDOWS_54_Host-DC";
        String attributeName = "ALL";
        String serviceIdentifier = "IIS-Web-Service-DC";
        String applicationIdentifier = "microbanking_1_DC";
        int noOfIteration = 3;
        String redisKey = "/eventDetector/" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        String redisHashKey = "EVENTDETECTOR_" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

        Map<String, Double> thresholdMap = new HashMap<>();
        thresholdMap.put("Upper", 0.0);
        thresholdMap.put("Lower", 60.0);
        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoType(accountIdentifier, "microbanking_1_DC",
                        serviceIdentifier, instanceIdentifier, kpiId, attributeName,
                        "80.0", 19800, "Static",
                        "greater than", "Severe", thresholdMap);

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);
        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = new ArrayList<>();
        for (int i = 0; i < noOfIteration; i++) {
            anomalyEventList.addAll(anomalyEventsProcess.processViolatedKpiData(violatedData, true));
        }
        scheduler.pushToOSBulkIndexing();
        OpenSearchClient client = openSearchConfig.getOpenSearchClient("", rawKpisViolationsIndex);
        SearchResponse<?> rawKpiResponse = searchOS(client, rawKpisViolationsIndex, accountIdentifier);
        SearchResponse<?> anomaliesResponse = searchOS(client, anomaliesIndex, accountIdentifier);

        assert violatedData.size() == 1;
        assert anomalyEventList.size() == 1;
        assert rawKpiResponse != null;
        assert anomaliesResponse != null;

        if (!violatedData.isEmpty()) {
            for (ViolatedData vd : violatedData) {
                assert vd.getViolationDetails() != null : "violationDetails should be set on ViolatedData";
            }
        }

        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

    }

    @Test
    public void processViolatedKpiData_kpiInfoType_ConfigWatch_instLvlPerSupValueNonNull_AnomalyGen() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "259";
        String instanceIdentifier = "SOLARIS_ENET_HOST_112_Inst_1-DR";
        String attributeName = "mysql.username";
        String serviceIdentifier = "ENET-App-Service-DR";
        String applicationIdentifier = "enet_3_DR";
        String redisKey = "/eventDetector/" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        String redisHashKey = "EVENTDETECTOR_" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoConfigWatchType(accountIdentifier, applicationIdentifier,
                        serviceIdentifier, instanceIdentifier, kpiId, attributeName,
                        "a" + Constants.DATA_SPLITTER_DEFAULT + "b" + Constants.DATA_SPLITTER_DEFAULT + "Modified", 19800, "Static",
                        "not between", "Severe");

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);
        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedData, true);
        scheduler.pushToOSBulkIndexing();
        SearchResponse<?> response = searchOS(openSearchConfig.getOpenSearchClient("", rawKpisViolationsIndex), rawKpisViolationsIndex, accountIdentifier);

        assert violatedData.size() == 1;
        assert anomalyEventList.size() == 1;
        assert response != null;

        if (!violatedData.isEmpty()) {
            for (ViolatedData vd : violatedData) {
                assert vd.getViolationDetails() != null : "violationDetails should be set on ViolatedData";
            }
        }

        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

    }

    @Test
    public void processViolatedKpiData_kpiInfoType_ConfigWatch_instLvlPerSupValueNull_AnomalyGen() {

        String accountIdentifier = "qa-d681ef13-d690-4917-jkhg-6c79b-1";
        String kpiId = "259";
        String instanceIdentifier = "WINDOWS_54_Host-DC";
        String attributeName = "ALL";
        String serviceIdentifier = "IIS-Web-Service-DC";
        String applicationIdentifier = "microbanking_1_DC";
        String redisKey = "/eventDetector/" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        String redisHashKey = "EVENTDETECTOR_" + accountIdentifier + "_" + applicationIdentifier + "_"
                + instanceIdentifier + "_" + kpiId + "_" + attributeName + "_SOR";
        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
                .createViolatedEventKpiInfoConfigWatchType(accountIdentifier, applicationIdentifier,
                        serviceIdentifier, instanceIdentifier, kpiId, attributeName,
                        "80.0", 19800, "Static",
                        "greater than", "Severe");

        List<ViolatedData> violatedData = getViolatedData.getViolatedDataObject(violatedEvent);
        List<AnomalyEventProtos.AnomalyEvent> anomalyEventList = anomalyEventsProcess.processViolatedKpiData(violatedData, true);
        scheduler.pushToOSBulkIndexing();
        OpenSearchClient client = openSearchConfig.getOpenSearchClient("", rawKpisViolationsIndex);
        SearchResponse<?> rawKpiResponse = searchOS(client, rawKpisViolationsIndex, accountIdentifier);
        SearchResponse<?> anomaliesResponse = searchOS(client, anomaliesIndex, accountIdentifier);

        assert violatedData.size() == 1;
        assert anomalyEventList.size() == 1;
        assert rawKpiResponse != null;
        assert anomaliesResponse != null;

        if (!violatedData.isEmpty()) {
            for (ViolatedData vd : violatedData) {
                assert vd.getViolationDetails() != null : "violationDetails should be set on ViolatedData";
            }
        }

        redisTemplate.opsForHash().delete(redisKey, redisHashKey);

    }

}
