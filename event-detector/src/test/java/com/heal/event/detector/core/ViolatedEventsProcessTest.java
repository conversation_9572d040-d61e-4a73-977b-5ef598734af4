package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AggregatedKpiProtos;
import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.google.protobuf.ProtocolStringList;
import com.heal.configuration.pojos.*;
import com.heal.event.detector.pojos.MetaData;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.util.ProtoCreator;
import com.heal.event.detector.utility.Utils;
import com.heal.event.detector.exception.EventDetectorException;
import com.heal.event.detector.utility.cache.CacheWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.lang.reflect.Method;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;


class ViolatedEventsProcessTest {

    private ViolatedEventsProcess violatedEventsProcess;

    @Autowired
    ProtoCreator protoCreator;

    @Mock
    CacheWrapper cacheWrapperMock;

    @Mock
    GetViolatedData getViolatedDataMock;

    @Mock
    RedisUtilities redisUtilitiesMock;

    @BeforeEach
    void setUp() {
        violatedEventsProcess = new ViolatedEventsProcess();
        getViolatedDataMock = mock(GetViolatedData.class);
        cacheWrapperMock = mock(CacheWrapper.class);
        redisUtilitiesMock = mock(RedisUtilities.class);
        violatedEventsProcess.getViolatedData = getViolatedDataMock;
        violatedEventsProcess.cacheWrapper = cacheWrapperMock;
        violatedEventsProcess.redisUtilities = redisUtilitiesMock;
    }

    public CompInstKpiEntity getInstanceDetails(int instanceId, String attributeName, int kpiId, Map<String, Double> thresholdMap,
                                                String operation, int severity, int generateAnomaly, int excludeMaintenance) {
        List<KpiViolationConfig> kpiViolationConfigList = new ArrayList<>();
        kpiViolationConfigList.add(KpiViolationConfig.builder()
                .operation(operation)
                .minThreshold(thresholdMap.get("Lower"))
                .maxThreshold(thresholdMap.get("Upper"))
                .severity(severity)
                .generateAnomaly(generateAnomaly)
                .excludeMaintenance(excludeMaintenance)
                .kpiId(kpiId)
                .compInstanceId(instanceId)
                .build());
        Map<String, List<KpiViolationConfig>> kpiViolationConfigMap = new HashMap<>();
        kpiViolationConfigMap.put(attributeName, kpiViolationConfigList);

        return CompInstKpiEntity.builder()
                .kpiViolationConfig(kpiViolationConfigMap)
                .build();
    }

    @Test
    void testGetServiceKpiViolationConfig_serviceReturnsNull() throws Exception {
        ViolatedEventsProcess process = new ViolatedEventsProcess();
        process.cacheWrapper = mock(CacheWrapper.class);

        ProtocolStringList mockAppIdList = mock(ProtocolStringList.class);
        when(mockAppIdList.get(0)).thenReturn("app1");
        when(mockAppIdList.size()).thenReturn(1);
        ProtocolStringList serviceIdList = mock(ProtocolStringList.class);
        when(serviceIdList.get(0)).thenReturn("svc1");
        when(serviceIdList.size()).thenReturn(1);
        when(serviceIdList.iterator()).thenReturn(List.of("svc1").iterator());

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        when(aggregatedKpi.getAccountId()).thenReturn("acc");
        when(aggregatedKpi.getServiceIdList()).thenReturn(serviceIdList);
        when(aggregatedKpi.getKpiData()).thenReturn(mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class));
        when(aggregatedKpi.getKpiData().getKpiUid()).thenReturn(1);
        when(aggregatedKpi.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");

        when(process.cacheWrapper.getServiceKPIDetails(any(), any(), anyInt())).thenReturn(null);

        MetaData metaData = new MetaData();

        Method method = ViolatedEventsProcess.class.getDeclaredMethod(
                "getServiceKpiViolationConfig",
                AggregatedKpiProtos.AggregatedKpi.class, boolean.class, MetaData.class
        );
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, List<KpiViolationConfig>> result = (Map<String, List<KpiViolationConfig>>) method.invoke(process, aggregatedKpi, false, metaData);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetServiceKpiViolationConfig_emptyConfig() throws Exception {
        ViolatedEventsProcess process = new ViolatedEventsProcess();
        process.cacheWrapper = mock(CacheWrapper.class);

        ProtocolStringList mockAppIdList = mock(ProtocolStringList.class);
        when(mockAppIdList.get(0)).thenReturn("app1");
        when(mockAppIdList.size()).thenReturn(1);
        ProtocolStringList serviceIdList = mock(ProtocolStringList.class);
        when(serviceIdList.get(0)).thenReturn("svc1");
        when(serviceIdList.size()).thenReturn(1);
        when(serviceIdList.iterator()).thenReturn(List.of("svc1").iterator());

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        when(aggregatedKpi.getAccountId()).thenReturn("acc");
        when(aggregatedKpi.getServiceIdList()).thenReturn(serviceIdList);
        when(aggregatedKpi.getKpiData()).thenReturn(mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class));
        when(aggregatedKpi.getKpiData().getKpiUid()).thenReturn(1);

        KpiDetails kpiDetails = new KpiDetails();
        kpiDetails.setKpiViolationConfig(new HashMap<>());
        when(process.cacheWrapper.getServiceKPIDetails(any(), any(), anyInt())).thenReturn(kpiDetails);

        MetaData metaData = new MetaData();

        Method method = ViolatedEventsProcess.class.getDeclaredMethod(
                "getServiceKpiViolationConfig",
                AggregatedKpiProtos.AggregatedKpi.class, boolean.class, MetaData.class
        );
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, List<KpiViolationConfig>> result = (Map<String, List<KpiViolationConfig>>) method.invoke(process, aggregatedKpi, false, metaData);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetServiceKpiViolationConfig_allConfigsFilteredOut() throws Exception {
        ViolatedEventsProcess process = new ViolatedEventsProcess();
        process.cacheWrapper = mock(CacheWrapper.class);

        ProtocolStringList mockAppIdList = mock(ProtocolStringList.class);
        when(mockAppIdList.get(0)).thenReturn("app1");
        when(mockAppIdList.size()).thenReturn(1);
        ProtocolStringList serviceIdList = mock(ProtocolStringList.class);
        when(serviceIdList.get(0)).thenReturn("svc1");
        when(serviceIdList.size()).thenReturn(1);
        when(serviceIdList.iterator()).thenReturn(List.of("svc1").iterator());

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        when(aggregatedKpi.getAccountId()).thenReturn("acc");
        when(aggregatedKpi.getServiceIdList()).thenReturn(serviceIdList);
        when(aggregatedKpi.getKpiData()).thenReturn(mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class));
        when(aggregatedKpi.getKpiData().getKpiUid()).thenReturn(1);

        // Configs that will be filtered out (generateAnomaly != 1)
        KpiViolationConfig config = KpiViolationConfig.builder()
                .operation("greater than")
                .minThreshold(10.0)
                .maxThreshold(100.0)
                .generateAnomaly(0)
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .build();
        Map<String, List<KpiViolationConfig>> configMap = new HashMap<>();
        configMap.put("ALL", List.of(config));
        KpiDetails kpiDetails = new KpiDetails();
        kpiDetails.setKpiViolationConfig(configMap);

        when(process.cacheWrapper.getServiceKPIDetails(any(), any(), anyInt())).thenReturn(kpiDetails);

        MetaData metaData = new MetaData();

        Method method = ViolatedEventsProcess.class.getDeclaredMethod(
                "getServiceKpiViolationConfig",
                AggregatedKpiProtos.AggregatedKpi.class, boolean.class, MetaData.class
        );
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, List<KpiViolationConfig>> result = (Map<String, List<KpiViolationConfig>>) method.invoke(process, aggregatedKpi, false, metaData);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetServiceKpiViolationConfig_statusZero() throws Exception {
        ViolatedEventsProcess process = new ViolatedEventsProcess();
        process.cacheWrapper = mock(CacheWrapper.class);

        ProtocolStringList mockAppIdList = mock(ProtocolStringList.class);
        when(mockAppIdList.get(0)).thenReturn("app1");
        when(mockAppIdList.size()).thenReturn(1);
        ProtocolStringList serviceIdList = mock(ProtocolStringList.class);
        when(serviceIdList.get(0)).thenReturn("svc1");
        when(serviceIdList.size()).thenReturn(1);
        when(serviceIdList.iterator()).thenReturn(List.of("svc1").iterator());

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        when(aggregatedKpi.getAccountId()).thenReturn("acc");
        when(aggregatedKpi.getServiceIdList()).thenReturn(serviceIdList);
        when(aggregatedKpi.getKpiData()).thenReturn(mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class));
        when(aggregatedKpi.getKpiData().getKpiUid()).thenReturn(1);

        // Configs that will be filtered out (generateAnomaly != 1)
        KpiViolationConfig config = KpiViolationConfig.builder()
                .operation("greater than")
                .minThreshold(10.0)
                .maxThreshold(100.0)
                .status(0) // status 0 disables
                .generateAnomaly(1)
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .definedBy("USER")
                .build();
        Map<String, List<KpiViolationConfig>> configMap = new HashMap<>();
        configMap.put("ALL", List.of(config));
        KpiDetails kpiDetails = new KpiDetails();
        kpiDetails.setKpiViolationConfig(configMap);

        when(process.cacheWrapper.getServiceKPIDetails(any(), any(), anyInt())).thenReturn(kpiDetails);

        MetaData metaData = new MetaData();

        Method method = ViolatedEventsProcess.class.getDeclaredMethod(
                "getServiceKpiViolationConfig",
                AggregatedKpiProtos.AggregatedKpi.class, boolean.class, MetaData.class
        );
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, List<KpiViolationConfig>> result = (Map<String, List<KpiViolationConfig>>) method.invoke(process, aggregatedKpi, false, metaData);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetServiceKpiViolationConfig_isCluster() throws Exception {
        ViolatedEventsProcess process = new ViolatedEventsProcess();
        process.cacheWrapper = mock(CacheWrapper.class);

        ProtocolStringList mockAppIdList = mock(ProtocolStringList.class);
        when(mockAppIdList.get(0)).thenReturn("app1");
        when(mockAppIdList.size()).thenReturn(1);
        ProtocolStringList serviceIdList = mock(ProtocolStringList.class);
        when(serviceIdList.get(0)).thenReturn("svc1");
        when(serviceIdList.size()).thenReturn(1);
        when(serviceIdList.iterator()).thenReturn(List.of("svc1").iterator());

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        when(aggregatedKpi.getAccountId()).thenReturn("acc");
        when(aggregatedKpi.getServiceIdList()).thenReturn(serviceIdList);
        when(aggregatedKpi.getKpiData()).thenReturn(mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class));
        when(aggregatedKpi.getKpiData().getKpiUid()).thenReturn(1);

        // Configs that will be filtered out (generateAnomaly != 1)
        KpiViolationConfig config = KpiViolationConfig.builder()
                .operation("greater than")
                .minThreshold(10.0)
                .maxThreshold(100.0)
                .status(1)
                .generateAnomaly(1)
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .definedBy("USER")
                .applicableTo("CLUSTERS")
                .build();
        Map<String, List<KpiViolationConfig>> configMap = new HashMap<>();
        configMap.put("ALL", List.of(config));
        KpiDetails kpiDetails = new KpiDetails();
        kpiDetails.setKpiViolationConfig(configMap);

        when(process.cacheWrapper.getServiceKPIDetails(any(), any(), anyInt())).thenReturn(kpiDetails);

        MetaData metaData = new MetaData();

        Method method = ViolatedEventsProcess.class.getDeclaredMethod(
                "getServiceKpiViolationConfig",
                AggregatedKpiProtos.AggregatedKpi.class, boolean.class, MetaData.class
        );
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, List<KpiViolationConfig>> result = (Map<String, List<KpiViolationConfig>>) method.invoke(process, aggregatedKpi, true, metaData);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetServiceKpiViolationConfig_validConfigReturned() throws Exception {
        ViolatedEventsProcess process = new ViolatedEventsProcess();
        process.cacheWrapper = mock(CacheWrapper.class);

        ProtocolStringList mockAppIdList = mock(ProtocolStringList.class);
        when(mockAppIdList.get(0)).thenReturn("app1");
        when(mockAppIdList.size()).thenReturn(1);
        ProtocolStringList serviceIdList = mock(ProtocolStringList.class);
        when(serviceIdList.get(0)).thenReturn("svc1");
        when(serviceIdList.size()).thenReturn(1);
        when(serviceIdList.iterator()).thenReturn(List.of("svc1").iterator());

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        when(aggregatedKpi.getAccountId()).thenReturn("acc");
        when(aggregatedKpi.getServiceIdList()).thenReturn(serviceIdList);
        when(aggregatedKpi.getKpiData()).thenReturn(mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class));
        when(aggregatedKpi.getKpiData().getKpiUid()).thenReturn(1);
        when(aggregatedKpi.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");

        // Config that passes all filters
        KpiViolationConfig configLow = KpiViolationConfig.builder()
                .operation("greater than")
                .minThreshold(60.0)
                .maxThreshold(0.0)
                .status(1)
                .generateAnomaly(1)
                .definedBy("USER")
                .applicableTo("INSTANCES")
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .startTime("2025-05-21 09:00:00")
                .endTime("2025-05-21 11:00:00")
                .build();
        KpiViolationConfig configMed = KpiViolationConfig.builder()
                .operation("greater than")
                .minThreshold(70.0)
                .maxThreshold(0.0)
                .status(1)
                .generateAnomaly(1)
                .definedBy("USER")
                .applicableTo("INSTANCES")
                .thresholdSeverity("Medium")
                .thresholdSeverityId(432)
                .startTime("2025-05-21 10:01:00")
                .endTime("2025-05-21 11:00:00")
                .build();
        Map<String, List<KpiViolationConfig>> configMap = new HashMap<>();
        configMap.put("ALL", List.of(configLow, configMed));
        KpiDetails kpiDetails = new KpiDetails();
        kpiDetails.setKpiViolationConfig(configMap);

        when(process.cacheWrapper.getServiceKPIDetails(any(), any(), anyInt())).thenReturn(kpiDetails);

        // To pass the applicableTo filter, isCluster must be false and applicableTo must be "INSTANCES"
        MetaData metaData = new MetaData();

        // Also, mock checkLatestServiceKpiViolationConfig to return true
        ViolatedEventsProcess spyProcess = Mockito.spy(process);
        Method checkLatestMethod = ViolatedEventsProcess.class.getDeclaredMethod(
                "checkLatestServiceKpiViolationConfig",
                AggregatedKpiProtos.AggregatedKpi.class, KpiViolationConfig.class, String.class, MetaData.class
        );
        checkLatestMethod.setAccessible(true);
//        Mockito.doReturn(true).when(spyProcess).checkLatestServiceKpiViolationConfig(any(), any(), any(), any());

        Method method = ViolatedEventsProcess.class.getDeclaredMethod(
                "getServiceKpiViolationConfig",
                AggregatedKpiProtos.AggregatedKpi.class, boolean.class, MetaData.class
        );
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, List<KpiViolationConfig>> result = (Map<String, List<KpiViolationConfig>>) method.invoke(spyProcess, aggregatedKpi, false, metaData);
        assertFalse(result.isEmpty());
        assertTrue(result.containsKey("ALL"));
        assertEquals(1, result.get("ALL").size());
        assertEquals(configLow, result.get("ALL").get(0));
    }

    @Test
    void testGetViolationsData_nonGroupKpi_emptyConfig() throws Exception {
        ViolatedEventsProcess process = new ViolatedEventsProcess();
        process.getViolatedData = mock(GetViolatedData.class);
        process.redisUtilities = redisUtilitiesMock;

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class);
        when(aggregatedKpi.getKpiData()).thenReturn(kpiData);
        when(kpiData.getIsKpiGroup()).thenReturn(false);
        when(kpiData.getVal()).thenReturn("55");
        when(aggregatedKpi.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");
        when(kpiData.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");

        Map<String, List<KpiViolationConfig>> configMap = new HashMap<>();
        configMap.put("ALL", new ArrayList<>());

        MetaData metaData = MetaData.builder().violationLevel("INSTANCE").build();

        ViolationDetails violationDetails = ViolationDetails.builder().build();
        ViolationStatus violationStatus = ViolationStatus.builder()
                .violationCount(2)
                .persistence(1)
                .suppression(2)
                .thresholds(Map.of("Lower", 90.0, "Upper", 0.0))
                .operationName("greater than")
                .build();
        Map<String, ViolationStatus> statusMap = new HashMap<>();
        statusMap.put("433", violationStatus);
        violationDetails.setViolationStatusMap(statusMap);
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(violationDetails);

        Method method = ViolatedEventsProcess.class.getDeclaredMethod(
                "getViolationsData",
                AggregatedKpiProtos.AggregatedKpi.class, Map.class, MetaData.class
        );
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<ViolatedData> result = (List<ViolatedData>) method.invoke(process, aggregatedKpi, configMap, metaData);
        assertTrue(result == null || result.isEmpty());
    }

    @Test
    void testGetViolationsData_groupKpi_allEmptyConfigs() throws Exception {
        ViolatedEventsProcess process = new ViolatedEventsProcess();
        process.getViolatedData = mock(GetViolatedData.class);
        process.redisUtilities = redisUtilitiesMock;

        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class);
        when(aggregatedKpi.getKpiData()).thenReturn(kpiData);
        when(kpiData.getIsKpiGroup()).thenReturn(true);
        when(aggregatedKpi.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");
        when(kpiData.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");

        // Mock groupKpi pairs
        KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi groupKpi = mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.GroupKpi.class);
        when(kpiData.getGroupKpi()).thenReturn(groupKpi);
        Map<String, String> pairsMap = new HashMap<>();
        pairsMap.put("attr1", "55");
        pairsMap.put("attr2", "65");
        when(groupKpi.getPairsMap()).thenReturn(pairsMap);

        // Empty configs for all attributes
        Map<String, List<KpiViolationConfig>> configMap = new HashMap<>();
        configMap.put("attr1", new ArrayList<>());
        configMap.put("attr2", new ArrayList<>());

        MetaData metaData = MetaData.builder().violationLevel("INSTANCE").build();

        ViolationDetails violationDetails = ViolationDetails.builder().build();
        ViolationStatus violationStatus = ViolationStatus.builder()
                .violationCount(2)
                .persistence(1)
                .suppression(2)
                .thresholds(Map.of("Lower", 90.0, "Upper", 0.0))
                .operationName("greater than")
                .build();
        Map<String, ViolationStatus> statusMap = new HashMap<>();
        statusMap.put("433", violationStatus);
        violationDetails.setViolationStatusMap(statusMap);
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(violationDetails);

        Method method = ViolatedEventsProcess.class.getDeclaredMethod(
                "getViolationsData",
                AggregatedKpiProtos.AggregatedKpi.class, Map.class, MetaData.class
        );
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<ViolatedData> result = (List<ViolatedData>) method.invoke(process, aggregatedKpi, configMap, metaData);
        assertTrue(result == null || result.isEmpty());
    }

    @Test
    void testViolatedKpi_validConfig_violationOccurs() throws Exception {
        String accountIdentifier = "demo";
        String instanceIdentifier = "inst1";
        String value = "55";
        String attributeName = "ALL";
        ProtocolStringList mockAppIdList = mock(ProtocolStringList.class);
        when(mockAppIdList.get(0)).thenReturn("app1");
        when(mockAppIdList.size()).thenReturn(1);
        ProtocolStringList serviceIdList = mock(ProtocolStringList.class);
        when(serviceIdList.get(0)).thenReturn("svc1");
        when(serviceIdList.size()).thenReturn(1);
        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class);
        when(aggregatedKpi.getKpiData()).thenReturn(kpiData);
        when(aggregatedKpi.getAccountId()).thenReturn(accountIdentifier);
        when(aggregatedKpi.getInstanceId()).thenReturn(instanceIdentifier);
        when(aggregatedKpi.getApplicationIdList()).thenReturn(mockAppIdList);
        when(aggregatedKpi.getServiceIdList()).thenReturn(serviceIdList);
        when(aggregatedKpi.getTimeZoneOffsetInSec()).thenReturn(0);
        when(aggregatedKpi.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");
        when(aggregatedKpi.getMetaDataMap()).thenReturn(new HashMap<>());
        when(kpiData.getKpiUid()).thenReturn(1);
        when(kpiData.getVal()).thenReturn(value);
        when(kpiData.getKpiType()).thenReturn(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        when(kpiData.getIsKpiGroup()).thenReturn(false);
        when(kpiData.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");
        List<KpiViolationConfig> configList = new ArrayList<>();
        KpiViolationConfig config = KpiViolationConfig.builder()
                .operation("greater than")
                .minThreshold(30.0)
                .maxThreshold(100.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .build();
        configList.add(config);
        Map<String, List<KpiViolationConfig>> violationProfiles = new HashMap<>();
        violationProfiles.put(attributeName, configList);
        ViolationDetails violationDetails = ViolationDetails.builder().build();
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(violationDetails);
        ViolatedData violatedDataMock = mock(ViolatedData.class);
        when(getViolatedDataMock.handleViolationDetails(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), anyLong(), any(), any(), any(), any())).thenReturn(violationDetails);
        when(getViolatedDataMock.getViolatedDataForHighestSeverity(any(), any(), any(), any(), any(), any(), any(), anyLong(), any(), any(), any(), any())).thenReturn(violatedDataMock);
        MetaData metaData = new MetaData();
        metaData.setViolationLevel("INSTANCE");
        metaData.setServiceIdentifier("svc1");
        Method method = ViolatedEventsProcess.class.getDeclaredMethod("violatedKpi",
                AggregatedKpiProtos.AggregatedKpi.class, String.class, String.class, Map.class, MetaData.class);
        method.setAccessible(true);
        ViolatedData result = (ViolatedData) method.invoke(violatedEventsProcess, aggregatedKpi, attributeName, value, violationProfiles, metaData);
        assertNotNull(result, "Result should not be null for valid violation config.");
    }

    @Test
    void testViolatedKpi_noConfig_returnsNull() throws Exception {
        String value = "65";
        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class);
        when(aggregatedKpi.getKpiData()).thenReturn(kpiData);
        when(aggregatedKpi.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");
        when(kpiData.getKpiUid()).thenReturn(1);
        when(kpiData.getVal()).thenReturn(value);
        Map<String, List<KpiViolationConfig>> violationProfiles = new HashMap<>();
        violationProfiles.put("ALL", new ArrayList<>());
        MetaData metaData = new MetaData();
        Method method = ViolatedEventsProcess.class.getDeclaredMethod("violatedKpi",
                AggregatedKpiProtos.AggregatedKpi.class, String.class, String.class, Map.class, MetaData.class);
        method.setAccessible(true);
        ViolatedData result = (ViolatedData) method.invoke(violatedEventsProcess, aggregatedKpi, "ALL", value, violationProfiles, metaData);
        assertNull(result, "Result should be null when violation config is unavailable.");
    }

    @Test
    void testViolatedKpi_statusZero_skipsConfig() throws Exception {
        String value = "55";
        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class);
        when(aggregatedKpi.getKpiData()).thenReturn(kpiData);
        when(aggregatedKpi.getAccountId()).thenReturn("acc");
        when(aggregatedKpi.getInstanceId()).thenReturn("inst");
        when(aggregatedKpi.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");
        when(kpiData.getKpiUid()).thenReturn(1);
        when(kpiData.getVal()).thenReturn(value);
        List<KpiViolationConfig> configList = new ArrayList<>();
        KpiViolationConfig config = KpiViolationConfig.builder()
                .operation("greater than")
                .minThreshold(30.0)
                .maxThreshold(100.0)
                .status(0)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .build();
        configList.add(config);
        Map<String, List<KpiViolationConfig>> violationProfiles = new HashMap<>();
        violationProfiles.put("ALL", configList);
        ViolationDetails violationDetails = ViolationDetails.builder().build();
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(violationDetails);
        MetaData metaData = new MetaData();
        Method method = ViolatedEventsProcess.class.getDeclaredMethod("violatedKpi",
                AggregatedKpiProtos.AggregatedKpi.class, String.class, String.class, Map.class, MetaData.class);
        method.setAccessible(true);
        ViolatedData result = (ViolatedData) method.invoke(violatedEventsProcess, aggregatedKpi, "ALL", value, violationProfiles, metaData);
        assertNull(result, "Result should be null when status is 0 (disabled config).");
    }

    @Test
    void testViolatedKpi_thresholdNull_skipsConfig() throws Exception {
        String value = "55";
        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class);
        when(aggregatedKpi.getKpiData()).thenReturn(kpiData);
        when(aggregatedKpi.getAccountId()).thenReturn("acc");
        when(aggregatedKpi.getInstanceId()).thenReturn("inst");
        when(aggregatedKpi.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");
        when(kpiData.getKpiUid()).thenReturn(1);
        when(kpiData.getVal()).thenReturn(value);
        List<KpiViolationConfig> configList = new ArrayList<>();
        KpiViolationConfig config = KpiViolationConfig.builder()
                .operation(null)
                .minThreshold(null)
                .maxThreshold(null)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .build();
        configList.add(config);
        Map<String, List<KpiViolationConfig>> violationProfiles = new HashMap<>();
        violationProfiles.put("ALL", configList);
        ViolationDetails violationDetails = ViolationDetails.builder().build();
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(violationDetails);
        MetaData metaData = new MetaData();
        Method method = ViolatedEventsProcess.class.getDeclaredMethod("violatedKpi",
                AggregatedKpiProtos.AggregatedKpi.class, String.class, String.class, Map.class, MetaData.class);
        method.setAccessible(true);
        ViolatedData result = (ViolatedData) method.invoke(violatedEventsProcess, aggregatedKpi, "ALL", value, violationProfiles, metaData);
        assertNull(result, "Result should be null when thresholds or operation are null.");
    }

    @Test
    void testViolatedKpi_withinThresholds_noViolation() throws Exception {
        String value = "35";
        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class);
        when(aggregatedKpi.getKpiData()).thenReturn(kpiData);
        when(aggregatedKpi.getAccountId()).thenReturn("acc");
        when(aggregatedKpi.getInstanceId()).thenReturn("inst");
        when(aggregatedKpi.getTimeInGMT()).thenReturn("2025-05-21 10:00:00");
        when(kpiData.getKpiUid()).thenReturn(1);
        when(kpiData.getVal()).thenReturn(value);
        List<KpiViolationConfig> configList = new ArrayList<>();
        KpiViolationConfig config = KpiViolationConfig.builder()
                .operation("between")
                .minThreshold(30.0)
                .maxThreshold(40.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .build();
        configList.add(config);
        Map<String, List<KpiViolationConfig>> violationProfiles = new HashMap<>();
        violationProfiles.put("ALL", configList);
        ViolationDetails violationDetails = ViolationDetails.builder().build();
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(violationDetails);
        try (MockedStatic<Utils> utilsMockedStatic = mockStatic(Utils.class)) {
            utilsMockedStatic.when(() -> Utils.dateStrToLocalDateTime(anyString())).thenCallRealMethod();
            utilsMockedStatic.when(() -> Utils.applyThreshold(anyDouble(), anyString(), anyMap())).thenReturn(null);
            MetaData metaData = new MetaData();
            Method method = ViolatedEventsProcess.class.getDeclaredMethod("violatedKpi",
                    AggregatedKpiProtos.AggregatedKpi.class, String.class, String.class, Map.class, MetaData.class);
            method.setAccessible(true);
            ViolatedData result = (ViolatedData) method.invoke(violatedEventsProcess, aggregatedKpi, "ALL", value, violationProfiles, metaData);
            assertNull(result, "Result should be null when KPI value is within thresholds (no violation).");
        }
    }

    @Test
    void testViolatedKpi_dateParseException_returnsNull() throws Exception {
        String value = "55";
        AggregatedKpiProtos.AggregatedKpi aggregatedKpi = mock(AggregatedKpiProtos.AggregatedKpi.class);
        KPIAgentMessageProtos.KPIAgentMessage.KpiData kpiData = mock(KPIAgentMessageProtos.KPIAgentMessage.KpiData.class);
        when(aggregatedKpi.getKpiData()).thenReturn(kpiData);
        when(aggregatedKpi.getAccountId()).thenReturn("acc");
        when(aggregatedKpi.getInstanceId()).thenReturn("inst");
        when(aggregatedKpi.getTimeInGMT()).thenReturn("bad-date");
        when(kpiData.getKpiUid()).thenReturn(1);
        when(kpiData.getVal()).thenReturn(value);
        List<KpiViolationConfig> configList = new ArrayList<>();
        KpiViolationConfig config = KpiViolationConfig.builder()
                .operation("greater than")
                .minThreshold(30.0)
                .maxThreshold(100.0)
                .status(1)
                .generateAnomaly(1)
                .excludeMaintenance(1)
                .thresholdSeverity("Low")
                .thresholdSeverityId(431)
                .build();
        configList.add(config);
        Map<String, List<KpiViolationConfig>> violationProfiles = new HashMap<>();
        violationProfiles.put("ALL", configList);
        ViolationDetails violationDetails = ViolationDetails.builder().build();
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(violationDetails);
        try (MockedStatic<Utils> utilsMockedStatic = mockStatic(Utils.class)) {
            utilsMockedStatic.when(() -> Utils.dateStrToLocalDateTime(anyString())).thenThrow(new EventDetectorException("bad date"));
            MetaData metaData = new MetaData();
            Method method = ViolatedEventsProcess.class.getDeclaredMethod("violatedKpi",
                    AggregatedKpiProtos.AggregatedKpi.class, String.class, String.class, Map.class, MetaData.class);
            method.setAccessible(true);
            ViolatedData result = (ViolatedData) method.invoke(violatedEventsProcess, aggregatedKpi, "ALL", value, violationProfiles, metaData);
            assertNull(result, "Result should be null if date parsing throws exception.");
        }
    }
}