package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.PSAgentMessageProtos;
import com.appnomic.appsone.common.protbuf.ThresholdProtos;
import com.heal.event.detector.config.OpenSearchConfig;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.scheduler.OSDataPushScheduler;
import com.heal.event.detector.util.ProtoCreator;
import com.heal.event.detector.utility.Utils;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Spy;
import org.opensearch.client.opensearch.core.SearchRequest;
import org.opensearch.client.opensearch.core.SearchResponse;
import org.opensearch.client.opensearch.indices.DeleteIndexRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@SpringBootTest
public class ThresholdDataProcessIT {

    @Spy
    ThresholdDataProcess thresholdDataProcess;

    @Autowired
    ProtoCreator protoCreator;

    @Autowired
    OpenSearchRepo openSearchRepo;

    @Autowired
    OpenSearchConfig openSearchConfig;

    @Value("${opensearch.kpi.violations.index:integration_test_ed_kpi_violations}")
    String rawKpisViolationsIndex;

    @Value("${opensearch.transaction.violations.index:integration_test_ed_transaction_violations}")
    String rawTransactionsViolationsIndex;

    @Value("${opensearch.batchjob.violations.index:integration_test_ed_batch_job_violations}")
    String rawBatchJobViolationsIndex;

    @Value("${opensearch.anomalies.index:integration_test_ed_anomalies}")
    String anomaliesIndex;

    @Value("${opensearch.kpi.thresholds.index:integration_test_ed_kpis_thresholds}")
    String kpiThresholdsIndex;

    @Value("${opensearch.transaction.thresholds.index:integration_test_ed_transactions_thresholds}")
    String transactionThresholdsIndex;

    @Autowired
    OSDataPushScheduler scheduler;

    @BeforeEach
    public void initialize() {
        openSearchRepo.rawKpisViolationsIndex = rawKpisViolationsIndex;
        openSearchRepo.rawTransactionsViolationsIndex = rawTransactionsViolationsIndex;
        openSearchRepo.rawBatchJobViolationsIndex = rawBatchJobViolationsIndex;
        openSearchRepo.anomaliesIndex = anomaliesIndex;
        openSearchRepo.kpiThresholdsIndex = kpiThresholdsIndex;
        openSearchRepo.transactionThresholdsIndex = transactionThresholdsIndex;
        openSearchRepo.openSearchConfig = openSearchConfig;
//        scheduler.isTestCase = true;
        openSearchRepo.scheduler = scheduler;
        thresholdDataProcess.openSearchRepo = openSearchRepo;
    }

    @AfterEach
    public void cleanUp() {

        OpenSearchClient client = openSearchConfig.getOpenSearchClient("", kpiThresholdsIndex);
        try {
            client.indices().delete(new DeleteIndexRequest.Builder().index("integration_test_ed_*").allowNoIndices(true).build());
        } catch (Exception e) {
            log.warn("Couldn't find any OS index with pattern integration_test_ed_*.");
        }

    }

    private SearchResponse<?> searchOS(OpenSearchClient client, String indexPrefix, String accountId) {
        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(indexPrefix + "_" + accountId + "_" + Utils.getDateForOSIndex())
                .ignoreUnavailable(true)
                .size(10000)
                .build();
        try {
            return client.search(searchRequest, Object.class);
        } catch (Exception e) {
            log.error("Exception while searching for index data. ", e);
            return null;
        }
    }


    @Test
    public void processThresholdData_kpiType_invalidThresholdDetails() {

        Map<String, Double> thresholdMap = new HashMap<>();
        String accountId = "d681ef13-d690-4917-jkhg-6c79b-9";
        String application = "NB-Web-App";
        String kpiId = "60";
        ThresholdProtos.Threshold thresholdData = protoCreator.createThresholdProtoKpiInfoType(accountId,
                application, "NB-Web-Service-DR", "PC_HTTPD_233", kpiId,
                "ALL", 19800, "Realtime",
                "greater than", "Default", thresholdMap);

        thresholdDataProcess.processAndSinkThresholdData(thresholdData);
        scheduler.pushToOSBulkIndexing();
        SearchResponse<?> response = searchOS(openSearchConfig.getOpenSearchClient("", kpiThresholdsIndex), kpiThresholdsIndex, accountId);

        assert response == null;

    }

    @Test
    public void processThresholdData_txnType_invalidThresholdDetails() {

        Map<String, Double> thresholdMap = new HashMap<>();
        String accountId = "d681ef13-d690-4917-jkhg-6c79b-9";
        String application = "NB-Web-App";
        String kpiId = "60";
        ThresholdProtos.Threshold thresholdData = protoCreator.createThresholdProtoTxnInfoType(accountId,
                application, "NB-Web-Service-DR", "transaction_1", kpiId,
                PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "2", 19800,
                "Realtime", "greater than", "Default", thresholdMap);

        thresholdDataProcess.processAndSinkThresholdData(thresholdData);
        scheduler.pushToOSBulkIndexing();
        SearchResponse<?> response = searchOS(openSearchConfig.getOpenSearchClient("", kpiThresholdsIndex), kpiThresholdsIndex, accountId);

        assert response == null;

    }

    @Test
    public void processThresholdData_kpiType() {

        String kpiId = "60";
        String accountId = "d681ef13-d690-4917-jkhg-6c79b-9";
        ThresholdProtos.Threshold thresholdData = protoCreator.createThresholdProtoKpiInfoType(accountId,
                "NB-Web-App", "NB-Web-Service-DR", "PC_HTTPD_233", kpiId,
                "ALL", 19800, "Realtime",
                "greater than", "Default", protoCreator.thresholdMapProvider());

        thresholdDataProcess.processAndSinkThresholdData(thresholdData);
        scheduler.pushToOSBulkIndexing();
        SearchResponse<?> response = searchOS(openSearchConfig.getOpenSearchClient("", kpiThresholdsIndex), kpiThresholdsIndex, accountId);

        assert (response != null);

    }

    @Test
    public void processThresholdData_txnType() {

        String kpiId = "60";
        String accountId = "d681ef13-d690-4917-jkhg-6c79b-9";
        ThresholdProtos.Threshold thresholdData = protoCreator.createThresholdProtoTxnInfoType(accountId,
                "NB-Web-App", "NB-Web-Service-DR", "transaction_1", kpiId,
                PSAgentMessageProtos.ResponseTime.ResponseTimeType.DC, "2", 19800,
                "Realtime", "greater than", "Default",
                protoCreator.thresholdMapProvider());

        thresholdDataProcess.processAndSinkThresholdData(thresholdData);
        scheduler.pushToOSBulkIndexing();
        SearchResponse<?> response = searchOS(openSearchConfig.getOpenSearchClient("", transactionThresholdsIndex), transactionThresholdsIndex, accountId);

        assert (response != null);

    }

}
