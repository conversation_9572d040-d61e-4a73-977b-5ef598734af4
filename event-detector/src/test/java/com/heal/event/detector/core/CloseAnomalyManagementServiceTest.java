package com.heal.event.detector.core;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.ViolationDetails;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.protbuf.AnomalySummaryProtos;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.utility.HealthMetrics;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Instant;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CloseAnomalyManagementServiceTest {

    @InjectMocks
    private AnomalyManagementService anomalyService;

    @Mock
    private RedisUtilities redisUtilities;

    @Mock
    private OpenSearchRepo openSearchRepo;

    @Mock
    private HealthMetrics metrics;

    private AnomalyAccountPojo anomalyAccountPojo;

    private String json;

    private ViolationDetails violationDetails;

    private AnomalySummaryProtos.AnomalySummary anomalySummary;

    @BeforeEach
    void setup() throws JsonProcessingException {
        ReflectionTestUtils.setField(anomalyService, "entityTypeInstance", "INSTANCE");
        ReflectionTestUtils.setField(anomalyService, "entityTypeTransaction", "TRANSACTION");

        anomalyAccountPojo = new AnomalyAccountPojo();
        anomalyAccountPojo.setAccountIdentifier("demo");
        Anomalies anomalies = new Anomalies();
        anomalies.setAnomalyId("AE-2-12-16-T-S-dm-2-********");
        anomalies.setAnomalyStartTime(1656634020000L);
        anomalies.setAnomalyEndTime(1656634020000L);
        anomalies.setAnomalyCreatedTime(1656634020000L);
        anomalies.setLastAlertTime(1656634020000L);
        anomalies.setAnomalyStatus("Close");
        anomalies.setIdentifiedTime(1656634297733L);
        anomalies.setEntityType("ComponentInstance");
        anomalies.setEntityId("<entityIdentifier>");
        anomalies.setStartSeverityId(431);
        anomalies.setLastSeverityId(431);
        anomalies.setCategoryId("Disk IO");
        anomalies.setKpiAttribute("dm-2");
        anomalies.setKpiId(16);
        anomalies.setKpiIdentifier("DEVICE_BUSY");
        anomalies.setServiceId(Collections.singleton("CC_Service"));

        // Metadata
        Map<String, String> metadata = new HashMap<>();
        metadata.put("isMaintenanceExcluded", "0");
        metadata.put("violationLevel", "SERVICE");
        metadata.put("serviceIdentifier", "CC_Service");
        metadata.put("isInformatic", "0");
        metadata.put("kpiType", "Core");
        metadata.put("attributeName", "dm-2");
        metadata.put("anomalyLevel", "SERVICE");
        metadata.put("closingWindow", "3");
        metadata.put("maxDataBreaks", "30");
        metadata.put("remaindersCount", String.valueOf(0));
        metadata.put("closeWindowResetCount", String.valueOf(0));
        metadata.put("databreakResetCount", String.valueOf(0));
        metadata.put("alertOccurrenceCount", String.valueOf(0));
        anomalies.setMetadata(metadata);

        // Additional fields
        anomalies.setSeveritiesEnabled(Set.of("431", "432", "233"));
        anomalies.setViolationCountsReset(0);
        anomalies.setOperationType("lesser than");
        anomalies.setSignalIds(Set.of("E-2-16-3-**********"));
        anomalies.setThresholdType("Static");
        anomalies.setClosingReason("Violations are stopped/Maintenance window/Data collection stopped");
        anomalies.setTimestamp(String.valueOf(Instant.parse("2022-07-01T00:07:00.000Z")));

        List<String> appIds = List.of("app-123");
        anomalySummary = AnomalySummaryProtos.AnomalySummary.newBuilder()
                .setAccountIdentifier(anomalyAccountPojo.getAccountIdentifier())
                .setAnomalyId(anomalies.getAnomalyId())
                .setAnomalyStatus(anomalies.getAnomalyStatus())
                .setClosingReason(anomalies.getClosingReason())
                .setKpiId(String.valueOf(anomalies.getKpiId()))
                .setViolationFor(anomalies.getThresholdType())
                .setEntityType(anomalies.getEntityType())
                .setEntityIdentifier(anomalies.getEntityId())
                .setKpiAttribute(anomalies.getKpiAttribute())
                .addAllAppId(appIds)
                .build();
        anomalyAccountPojo.setAnomalyDetails(anomalies);

        json = """
                {
                  "violationStatusMap": {
                    "431": {
                      "violationCount": 1,
                      "violationStartTime": *************,
                      "lastViolationTime": *************,
                      "persistence": 3,
                      "suppression": 2,
                      "thresholds": {
                        "Upper": 0,
                        "Lower": 65
                      },
                      "operationName": "greater than"
                    }
                  },
                  "anomalyEventStatus": {
                    "anomalyEventId": "AE-2-12-16-T-S-dm-2-********",
                    "anomalyStartTime": *************,
                    "anomalyStatus": "Open",
                    "lastAnomalySeverity": "High"
                    },
                  "violationLevel": "INSTANCE",
                  "highestViolatedSeverity": "431"
                }
                """;

        ObjectMapper objectMapper = new ObjectMapper();
        violationDetails = objectMapper.readValue(json, ViolationDetails.class);
    }

    //Anomaly Create tests
    @Test
    public void closeAnomaly_ShouldSucceed_WhenApplicationSettingsIsAvailable() {
        doReturn(violationDetails).when(redisUtilities).getViolationDetails(any(), any(), any(), any(), any(), any());
        doReturn(anomalyAccountPojo).when(openSearchRepo).getAnomalyFromOpenSearch(anyString(), anyString());
        doNothing().when(metrics).updateAnomalyCloseCount(anyInt());
        doNothing().when(metrics).updateAlertCount(anyInt());

        // Act
        AnomalyAccountPojo anomalyAccountPojo = anomalyService.closeAnomaly(anomalySummary);

        // Assert
        assertNotNull(anomalyAccountPojo);
        assertEquals("Close", anomalyAccountPojo.getAnomalyDetails().getAnomalyStatus());
    }

    @Test
    public void closeAnomaly_ShouldNull_WhenAnomalyIdIsNotAvailable() {
        doNothing().when(metrics).updateViolatedEventProcessingErrors();

        anomalySummary = AnomalySummaryProtos.AnomalySummary.newBuilder()
                .setAnomalyId("")
                        .build();

        // Act
        AnomalyAccountPojo anomalyAccountPojo = anomalyService.closeAnomaly(anomalySummary);

        // Assert
        assertNull(anomalyAccountPojo);
    }

    @Test
    public void closeAnomaly_ShouldNull_WhenExistingViolationDetailsIsNull() {
        doReturn(violationDetails).when(redisUtilities).getViolationDetails(any(), any(), any(), any(), any(), any());

        doReturn(null).when(openSearchRepo).getAnomalyFromOpenSearch(anyString(), anyString());
        doNothing().when(metrics).updateViolatedEventProcessingErrors();

        // Act
        AnomalyAccountPojo anomalyAccountPojo = anomalyService.closeAnomaly(anomalySummary);

        // Assert
        assertNull(anomalyAccountPojo);
    }
}

