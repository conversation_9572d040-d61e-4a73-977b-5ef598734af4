package com.heal.event.detector.repo;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.heal.configuration.pojos.opensearch.Alerts;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.util.DateHelper;
import com.heal.event.detector.config.OpenSearchConfig;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.scheduler.OSDataPushScheduler;
import com.heal.event.detector.utility.HealthMetrics;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch.core.IndexRequest;
import org.opensearch.client.opensearch.core.SearchRequest;
import org.opensearch.client.opensearch.core.SearchResponse;
import org.opensearch.client.opensearch.core.search.Hit;
import org.opensearch.client.opensearch.core.search.HitsMetadata;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.internal.verification.VerificationModeFactory.times;

@ExtendWith(MockitoExtension.class)
class OpenSearchRepoTest {

    @InjectMocks
    private OpenSearchRepo openSearchRepo;

    @Mock
    public OSDataPushScheduler scheduler;

    @Mock
    private HealthMetrics metrics;

    @Mock
    public OpenSearchConfig openSearchConfig;

    @Mock
    private OpenSearchClient openSearchClient;

    @Mock
    private Gson gson;

    @Mock
    private ObjectMapper objectMapper;

    private static final String ANOMALY_ID = "anomaly-123";
    private static final String ACCOUNT_ID = "account-456";

    @BeforeEach
    void setUp() {
        // Manually inject the value for anomaliesIndex
        ReflectionTestUtils.setField(openSearchRepo, "anomaliesIndex", "test_anomalies_index");
        ReflectionTestUtils.setField(openSearchRepo, "alertsIndex", "test_alerts_index");
    }

    @Test
    void testInsertOrUpdateAnomalyData_successfulIndexing() {
        Anomalies anomaly = new Anomalies();
        anomaly.setAnomalyId(ANOMALY_ID);
        anomaly.setAnomalyCreatedTime(System.currentTimeMillis());

        AnomalyAccountPojo pojo = new AnomalyAccountPojo();
        pojo.setAccountIdentifier(ACCOUNT_ID);
        pojo.setAnomalyDetails(anomaly);

        List<String> weekList = Collections.singletonList("2025W24");
        long createdTime = anomaly.getAnomalyCreatedTime();

        try (MockedStatic<DateHelper> mockedDateHelper = Mockito.mockStatic(DateHelper.class)) {
            mockedDateHelper.when(() -> DateHelper.getWeeksAsString(createdTime, createdTime)).thenReturn(weekList);
            mockedDateHelper.when(() -> DateHelper.getDate(createdTime)).thenReturn("2025-06-13T10:00:00Z");

            openSearchRepo.insertOrUpdateAnomalyData(pojo);

            ArgumentCaptor<IndexRequest<AnomalyAccountPojo>> captor = ArgumentCaptor.forClass(IndexRequest.class);

            verify(scheduler).addToIndexQueue(captor.capture(), eq(pojo.getAccountIdentifier()), eq("test_anomalies_index"));

            IndexRequest<AnomalyAccountPojo> indexRequest = captor.getValue();
            assertEquals(ANOMALY_ID, indexRequest.id());
            assertTrue(indexRequest.index().contains(pojo.getAccountIdentifier()));

            verify(metrics, never()).updateOpenSearchErrors(anyInt());
        }
    }

    @Test
    void testInsertOrUpdateAnomalyData_exceptionHandling() {
        Anomalies anomaly = new Anomalies();
        anomaly.setAnomalyId(ANOMALY_ID);
        anomaly.setAnomalyCreatedTime(System.currentTimeMillis());

        AnomalyAccountPojo pojo = new AnomalyAccountPojo();
        pojo.setAccountIdentifier(ACCOUNT_ID);
        pojo.setAnomalyDetails(anomaly);

        List<String> weekList = Collections.singletonList("2025W24");
        long createdTime = anomaly.getAnomalyCreatedTime();

        try (MockedStatic<DateHelper> mockedDateHelper = Mockito.mockStatic(DateHelper.class)) {
            mockedDateHelper.when(() -> DateHelper.getWeeksAsString(createdTime, createdTime)).thenReturn(weekList);
            mockedDateHelper.when(() -> DateHelper.getDate(createdTime)).thenReturn("2025-06-13T10:00:00Z");

            doThrow(new RuntimeException("indexing failed")).when(scheduler)
                    .addToIndexQueue(any(), anyString(), anyString());

            openSearchRepo.insertOrUpdateAnomalyData(pojo);

            verify(metrics, times(1)).updateOpenSearchErrors(1);
        }
    }

    @Test
    void testGetAnomalyFromOpenSearch_Successful() throws Exception {
        String accountId = "acct1";
        String anomalyId = "anomaly123";

        SearchResponse<Object> searchResponse = mock(SearchResponse.class);
        HitsMetadata<Object> hitsMetadata = mock(HitsMetadata.class);
        Hit<Object> hit = mock(Hit.class);
        Object hitSource = new Object();
        AnomalyAccountPojo expectedPojo = new AnomalyAccountPojo();

        when(openSearchConfig.getOpenSearchClient(accountId, "test_anomalies_index")).thenReturn(openSearchClient);
        when(openSearchClient.search(any(SearchRequest.class), eq(Object.class))).thenReturn(searchResponse);
        when(searchResponse.hits()).thenReturn(hitsMetadata);
        when(hitsMetadata.hits()).thenReturn(List.of(hit));
        when(hit.source()).thenReturn(hitSource);
        when(gson.toJson(hitSource)).thenReturn("{json}");
        when(objectMapper.readValue(eq("{json}"), ArgumentMatchers.<TypeReference<AnomalyAccountPojo>>any()))
                .thenReturn(expectedPojo);

        AnomalyAccountPojo result = openSearchRepo.getAnomalyFromOpenSearch(accountId, anomalyId);

        assertNotNull(result);
        assertEquals(expectedPojo, result);
    }

    @Test
    void testGetAnomalyFromOpenSearch_NullClient() {
        String accountId = "acct1";
        String anomalyId = "anomaly123";

        when(openSearchConfig.getOpenSearchClient(accountId, "test_anomalies_index")).thenReturn(null);

        AnomalyAccountPojo result = openSearchRepo.getAnomalyFromOpenSearch(accountId, anomalyId);

        assertNull(result);
        verify(metrics, never()).updateOpenSearchErrors(anyInt()); // no error, just null client
    }

    @Test
    void testGetAnomalyFromOpenSearch_Exception() throws Exception {
        String accountId = "acct1";
        String anomalyId = "anomaly123";

        when(openSearchConfig.getOpenSearchClient(accountId, "test_anomalies_index")).thenReturn(openSearchClient);
        when(openSearchClient.search(any(SearchRequest.class), eq(Object.class))).thenThrow(new RuntimeException("OpenSearch error"));

        AnomalyAccountPojo result = openSearchRepo.getAnomalyFromOpenSearch(accountId, anomalyId);

        assertNull(result);
        verify(metrics).updateOpenSearchErrors(1);
    }

    @Test
    void testInsertOrUpdateAlertData_Success() {
        Alerts alerts = mock(Alerts.class);
        String accountId = "acct123";
        String alertId = "alert123";

        long identifiedTime = System.currentTimeMillis();

        when(alerts.getIdentifiedTime()).thenReturn(identifiedTime);

        try (MockedStatic<DateHelper> dateHelperMockedStatic = mockStatic(DateHelper.class)) {
            dateHelperMockedStatic.when(() -> DateHelper.getWeeksAsString(identifiedTime, identifiedTime))
                    .thenReturn(List.of("2025W24"));
            dateHelperMockedStatic.when(() -> DateHelper.getDate(identifiedTime))
                    .thenReturn("2025-06-13");

            openSearchRepo.insertAlertData(alerts, accountId, alertId);

            verify(alerts).setTimestamp("2025-06-13");
            verify(scheduler).addToIndexQueue(any(), eq(accountId), eq("test_alerts_index"));
            verify(metrics, never()).updateOpenSearchErrors(anyInt());
        }
    }

    @Test
    void testInsertOrUpdateAlertData_Exception() {
        Alerts alerts = mock(Alerts.class);
        String accountId = "acct123";
        String alertId = "alert123";

        long identifiedTime = System.currentTimeMillis();

        when(alerts.getIdentifiedTime()).thenReturn(identifiedTime);

        try (MockedStatic<DateHelper> dateHelperMockedStatic = mockStatic(DateHelper.class)) {
            dateHelperMockedStatic.when(() -> DateHelper.getWeeksAsString(identifiedTime, identifiedTime))
                    .thenThrow(new RuntimeException("Date error"));
            dateHelperMockedStatic.when(() -> DateHelper.getDate(identifiedTime))
                    .thenReturn("2025-06-13");

            openSearchRepo.insertAlertData(alerts, accountId, alertId);

            verify(metrics).updateOpenSearchErrors(1);
            verify(scheduler, never()).addToIndexQueue(any(), anyString(), anyString());
        }
    }
}