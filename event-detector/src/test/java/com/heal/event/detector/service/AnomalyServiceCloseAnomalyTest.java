package com.heal.event.detector.service;

import com.heal.configuration.protbuf.AnomalySummaryProtos;
import com.heal.event.detector.core.AnomalyManagementService;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.repo.RedisUtilities;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.HashMap;
import java.util.Map;

@SpringBootTest
@ExtendWith(SpringExtension.class)
public class AnomalyServiceCloseAnomalyTest {

        @Autowired
        private AnomalyManagementService anomalyService;

        @Autowired
        private RedisUtilities redisUtilities;

        @Autowired
        private OpenSearchRepo openSearchRepo;

        @Test
        void testCloseAnomaly_success() {
            // Arrange
            Map<String, String> metadata = new HashMap<>();
            metadata.put("serviceIdentifier", "CC_Service");
            AnomalySummaryProtos.AnomalySummary anomalySummary = AnomalySummaryProtos.AnomalySummary.newBuilder()
                    .setAnomalyId("AE-3-75-2-C-S-64695-********")
                    .setAccountIdentifier("demo")
                    .setEntityIdentifier("65b5d4b0-cf19-4233-9b2f-9d45ab6f545f")
                    .setEntityType("INSTANCE")
                    .setKpiId("2")
                    .setKpiAttribute("64695")
                    .setAnomalyStatus("Close")
                    .setViolationFor("SOR")
                    .setClosingReason("Not violated")
                    .putAllMetadata(metadata)
                    .build();

            // Act
            AnomalyAccountPojo anomalyAccountPojo = anomalyService.closeAnomaly(anomalySummary);

            // Assert
            Assertions.assertEquals(anomalySummary.getAnomalyId(), anomalyAccountPojo.getAnomalyDetails().getAnomalyId());
        }
}
