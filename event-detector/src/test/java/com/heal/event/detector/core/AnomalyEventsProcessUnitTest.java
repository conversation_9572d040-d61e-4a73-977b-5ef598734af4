package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.heal.configuration.pojos.ApplicationSettings;
import com.heal.configuration.pojos.ComponentKpiEntity;
import com.heal.configuration.pojos.KpiCategoryDetails;
import com.heal.configuration.pojos.opensearch.Alerts;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import com.heal.event.detector.repo.OpenSearchRepo;
import com.heal.event.detector.service.EventForwarderToQueue;
import com.heal.event.detector.utility.HealthMetrics;
import com.heal.event.detector.utility.cache.CacheWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AnomalyEventsProcessUnitTest {

    @InjectMocks
    private AnomalyEventsProcess anomalyEventsProcess;

    @Mock
    private OpenSearchRepo openSearchRepo;

    @Mock
    private PrepareAnomalyData prepareAnomalyData;

    @Mock
    private PersistenceSuppression persistenceSuppression;

    @Mock
    private PerSuppProcessWatcherKPI perSuppProcessWatcherKPI;

    @Mock
    private PerSuppBatchJob perSuppBatchJob;

    @Mock
    private HealthMetrics metrics;

    @Mock
    private EventForwarderToQueue forwarder;

    @Mock
    private CacheWrapper cacheWrapper;

    private List<ViolatedData> violatedDataList;
    private ViolatedData kpiViolatedData;
    private ViolatedData watcherViolatedData;
    private ViolatedData batchJobViolatedData;
    private AnomalyAccountPojo anomalyAccountPojo;
    private AnomalyEventProtos.AnomalyEvent anomalyEvent;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(anomalyEventsProcess, "pushRawAnomalies", true);
        ReflectionTestUtils.setField(anomalyEventsProcess, "globalAccountIdentifier", "e573f852-5057-11e9-8fd2-b37b61e52317");
        ReflectionTestUtils.setField(anomalyEventsProcess, "transactionComponentIdentifier", "Transaction");
        ReflectionTestUtils.setField(anomalyEventsProcess, "batchComponentIdentifier", "BatchProcess");


        // Setup KPI violation data
        kpiViolatedData = new ViolatedData("account-123", Arrays.asList("app-1", "app-2"));
        kpiViolatedData.setEventType(ViolationEventType.KPI_VIOLATION);
        kpiViolatedData.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        kpiViolatedData.setServiceList(Arrays.asList("service-1"));
        kpiViolatedData.setKpiId("kpi-123");
        kpiViolatedData.setInstanceId("instance-123");
        kpiViolatedData.setKpiAttribute("CPU_Usage");
        kpiViolatedData.setValue("85.5");
        kpiViolatedData.setViolationTime(System.currentTimeMillis());

        // Setup watcher violation data
        watcherViolatedData = new ViolatedData("account-123", Arrays.asList("app-1"));
        watcherViolatedData.setEventType(ViolationEventType.KPI_VIOLATION);
        watcherViolatedData.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.FileWatch);
        watcherViolatedData.setServiceList(Arrays.asList("service-1"));
        watcherViolatedData.setKpiId("watcher-kpi-456");
        watcherViolatedData.setInstanceId("instance-456");
        watcherViolatedData.setKpiAttribute("File_Status");

        // Setup batch job violation data
        batchJobViolatedData = new ViolatedData("account-123", Arrays.asList("app-1"));
        batchJobViolatedData.setEventType(ViolationEventType.BATCH_JOB_VIOLATION);
        batchJobViolatedData.setKpiId("batch-kpi-789");
        batchJobViolatedData.setBatchJob("daily-batch");
        batchJobViolatedData.setValue("FAILED");

        violatedDataList = Arrays.asList(kpiViolatedData, watcherViolatedData, batchJobViolatedData);

        // Setup anomaly account pojo
        anomalyAccountPojo = new AnomalyAccountPojo();
        anomalyAccountPojo.setAccountIdentifier("account-123");
        
        Anomalies anomalies = new Anomalies();
        anomalies.setAnomalyId("AE-123-456");
        anomalies.setAnomalyStartTime(System.currentTimeMillis());
        anomalies.setAnomalyEndTime(System.currentTimeMillis());
        anomalies.setAnomalyCreatedTime(System.currentTimeMillis());
        anomalies.setEntityType("ComponentInstance");
        anomalies.setEntityId("instance-123");
        anomalies.setKpiId(123);
        anomalies.setKpiAttribute("CPU_Usage");
        anomalies.setThresholdType("Static");
        anomalies.setOperationType("greater than");
        
        Map<String, String> metadata = new HashMap<>();
        metadata.put("appIds", "app-1,app-2");
        metadata.put("alertId", "alert-123");
        metadata.put("thresholdsUpper", "80.0");
        metadata.put("thresholdsLower", "70.0");
        anomalies.setMetadata(metadata);
        
        anomalyAccountPojo.setAnomalyDetails(anomalies);

        // Setup anomaly event proto
        anomalyEvent = AnomalyEventProtos.AnomalyEvent.newBuilder()
                .setAnomalyId("AE-123-456")
                .setAccountId("account-123")
                .addAllAppId(Arrays.asList("app-1", "app-2"))
                .setStartTimeGMT(System.currentTimeMillis())
                .setEndTimeGMT(System.currentTimeMillis())
                .setAnomalyTriggerTimeGMT(System.currentTimeMillis())
                .setThresholdType("Static")
                .setOperationType("greater than")
                .setKpis(AnomalyEventProtos.KpiInfo.newBuilder()
                        .setKpiId("123")
                        .setKpiAttribute("CPU_Usage")
                        .setValue("85.5")
                        .putAllMetadata(metadata)
                        .setInstanceId(anomalies.getEntityId())
                        .addSvcId("service-1")
                        .setIsWorkload(true)
                        .build())
                .setBatchInfo(AnomalyEventProtos.BatchInfo.newBuilder()
                        .setBatchJob("daily-batch")
                        .setKpiId("batch-kpi-789")
                        .setThresholdSeverity("HIGH")
                        .setValue("FAILED")
                        .setIsWorkload(false)
                        .build())
                .build();
    }

    @Test
    void testProcessViolatedKpiData_ReturnsAnomalyEvents_WhenApplicationSettingsIsNull() {
        // Given
        List<AnomalyEventProtos.AnomalyEvent> expectedEvents = Arrays.asList(anomalyEvent);

        // Mock internal method calls
        doNothing().when(openSearchRepo).insertRawKpiViolations(anyList());
        doNothing().when(openSearchRepo).insertRawTxnViolations(anyList());
        doNothing().when(openSearchRepo).insertRawBatchJobViolations(anyList());

        doReturn(anomalyAccountPojo).when(persistenceSuppression).applyPersistenceSuppressionCreateUpdateAnomaly(any());

        when(perSuppProcessWatcherKPI.applyPersistenceSuppressionBasedOnType(
                watcherViolatedData, 0, 0, false, watcherViolatedData.getServiceList().get(0), 1))
                .thenReturn(anomalyEvent);

        when(perSuppBatchJob.applyPersistenceSupp(any(ViolatedData.class)))
                .thenReturn(anomalyEvent);

        when(prepareAnomalyData.getAllAnomalies(anyList()))
                .thenReturn(expectedEvents);

        doNothing().when(openSearchRepo).insertAnomalies(anyList());
        doNothing().when(openSearchRepo).insertAlertData(any(Alerts.class), anyString(), anyString());
        doNothing().when(forwarder).sendAnomalyOutputToActionQueue(anyList());
        doNothing().when(forwarder).sendAnomalyOutputToSignalQueue(any(AnomalyEventProtos.AnomalyEvent.class));
        doNothing().when(forwarder).sendAnomalyMessagesQueue(any(AnomalyEventProtos.AnomalyEvent.class));
        when(cacheWrapper.getMstTypes()).thenReturn(Collections.emptyList());
        when(cacheWrapper.getApplicationSettings(anyString(), anyString())).thenReturn(null);

        ComponentKpiEntity basicKpiEntity = ComponentKpiEntity.builder()
                .categoryDetails(KpiCategoryDetails.builder()
                        .id(123)
                        .isWorkLoad(true)
                        .name("Performance")
                        .identifier("kpi-123")
                        .build())
                .identifier("kpi-123")
                .type("transactional")
                .build();

        doReturn(basicKpiEntity).when(cacheWrapper).getComponentKPIDetails("e573f852-5057-11e9-8fd2-b37b61e52317", "Transaction", "123");
        doReturn(basicKpiEntity).when(cacheWrapper).getComponentKPIDetails("e573f852-5057-11e9-8fd2-b37b61e52317", "BatchProcess", "123");

        // When
        List<AnomalyEventProtos.AnomalyEvent> result = 
                anomalyEventsProcess.processViolatedKpiData(violatedDataList, true);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("AE-123-456", result.get(0).getAnomalyId());

        // Verify method calls
        verify(openSearchRepo).insertRawKpiViolations(anyList());
        verify(openSearchRepo).insertRawTxnViolations(anyList());
        verify(openSearchRepo).insertRawBatchJobViolations(anyList());
        verify(persistenceSuppression).applyPersistenceSuppressionCreateUpdateAnomaly(any(ViolatedData.class));
        verify(perSuppProcessWatcherKPI).applyPersistenceSuppressionBasedOnType(
                any(ViolatedData.class), anyInt(), anyInt(), anyBoolean(), anyString(), anyInt());
        verify(perSuppBatchJob).applyPersistenceSupp(any(ViolatedData.class));
        verify(prepareAnomalyData).getAllAnomalies(anyList());
        verify(openSearchRepo).insertAnomalies(anyList());
        verify(forwarder).sendAnomalyOutputToActionQueue(anyList());
        verify(forwarder).sendAnomalyOutputToSignalQueue(any(AnomalyEventProtos.AnomalyEvent.class));
        verify(forwarder).sendAnomalyMessagesQueue(any(AnomalyEventProtos.AnomalyEvent.class));
    }

    @Test
    void testProcessViolatedKpiData_EmptyList_ReturnsNull() {
        // Given
        List<ViolatedData> emptyList = Collections.emptyList();

        when(prepareAnomalyData.getAllAnomalies(anyList()))
                .thenReturn(Collections.emptyList());

        // When
        List<AnomalyEventProtos.AnomalyEvent> result = 
                anomalyEventsProcess.processViolatedKpiData(emptyList, false);

        // Then
        assertNull(result);

        // Verify no processing occurred
        verify(openSearchRepo, never()).insertAnomalies(anyList());
        verify(forwarder, never()).sendAnomalyOutputToActionQueue(anyList());
    }

    @Test
    void testProcessViolatedKpiData_ReturnsAnomalyEvents_WhenApplicationSettingsIsNotNull() {
        // Given
        List<AnomalyEventProtos.AnomalyEvent> expectedEvents = Arrays.asList(anomalyEvent);

        // Mock internal method calls
        doNothing().when(openSearchRepo).insertRawKpiViolations(anyList());
        doNothing().when(openSearchRepo).insertRawTxnViolations(anyList());
        doNothing().when(openSearchRepo).insertRawBatchJobViolations(anyList());

        doReturn(anomalyAccountPojo).when(persistenceSuppression).applyPersistenceSuppressionCreateUpdateAnomaly(any());

        when(perSuppProcessWatcherKPI.applyPersistenceSuppressionBasedOnType(
                watcherViolatedData, 0, 0, false, watcherViolatedData.getServiceList().get(0), 1))
                .thenReturn(anomalyEvent);

        when(perSuppBatchJob.applyPersistenceSupp(any(ViolatedData.class)))
                .thenReturn(anomalyEvent);

        when(prepareAnomalyData.getAllAnomalies(anyList()))
                .thenReturn(expectedEvents);

        doNothing().when(openSearchRepo).insertAnomalies(anyList());
        doNothing().when(openSearchRepo).insertAlertData(any(Alerts.class), anyString(), anyString());
        doNothing().when(forwarder).sendAnomalyOutputToActionQueue(anyList());
        doNothing().when(forwarder).sendAnomalyOutputToSignalQueue(any(AnomalyEventProtos.AnomalyEvent.class));
        doNothing().when(forwarder).sendAnomalyMessagesQueue(any(AnomalyEventProtos.AnomalyEvent.class));
        when(cacheWrapper.getMstTypes()).thenReturn(Collections.emptyList());
        ApplicationSettings appSettings = new ApplicationSettings();
        appSettings.setTypeName("signal-detector");
        when(cacheWrapper.getApplicationSettings(anyString(), anyString())).thenReturn(appSettings);

        ComponentKpiEntity basicKpiEntity = ComponentKpiEntity.builder()
                .categoryDetails(KpiCategoryDetails.builder()
                        .id(123)
                        .isWorkLoad(true)
                        .name("Performance")
                        .identifier("kpi-123")
                        .build())
                .identifier("kpi-123")
                .type("transactional")
                .build();

        doReturn(basicKpiEntity).when(cacheWrapper).getComponentKPIDetails("e573f852-5057-11e9-8fd2-b37b61e52317", "Transaction", "123");
        doReturn(basicKpiEntity).when(cacheWrapper).getComponentKPIDetails("e573f852-5057-11e9-8fd2-b37b61e52317", "BatchProcess", "123");


        // When
        List<AnomalyEventProtos.AnomalyEvent> result =
                anomalyEventsProcess.processViolatedKpiData(violatedDataList, true);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("AE-123-456", result.get(0).getAnomalyId());

        // Verify method calls
        verify(openSearchRepo).insertRawKpiViolations(anyList());
        verify(openSearchRepo).insertRawTxnViolations(anyList());
        verify(openSearchRepo).insertRawBatchJobViolations(anyList());
        verify(persistenceSuppression).applyPersistenceSuppressionCreateUpdateAnomaly(any(ViolatedData.class));
        verify(perSuppProcessWatcherKPI).applyPersistenceSuppressionBasedOnType(
                any(ViolatedData.class), anyInt(), anyInt(), anyBoolean(), anyString(), anyInt());
        verify(perSuppBatchJob).applyPersistenceSupp(any(ViolatedData.class));
        verify(prepareAnomalyData).getAllAnomalies(anyList());
        verify(openSearchRepo).insertAnomalies(anyList());
        verify(forwarder).sendAnomalyOutputToActionQueue(anyList());
        verify(forwarder).sendAnomalyOutputToSignalQueue(any(AnomalyEventProtos.AnomalyEvent.class));
        verify(forwarder).sendAnomalyMessagesQueue(any(AnomalyEventProtos.AnomalyEvent.class));
    }
}
