package com.heal.event.detector.service;

import com.heal.configuration.pojos.ViolationDetails;
import com.heal.configuration.pojos.opensearch.Alerts;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.event.detector.config.RabbitMqConfig;
import com.heal.event.detector.core.AnomalyManagementService;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.repo.RedisUtilities;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ExtendWith(SpringExtension.class)
public class AnomalyServiceUpdateAnomalyTest {

    @Autowired
    private AnomalyManagementService anomalyService;

    @Autowired
    private RedisUtilities redisUtilities;

    @Autowired
    private RabbitTemplate rabbitTemplate; // or however you're publishing to RabbitMQ

    @Autowired
    private RabbitMqConfig config;

    @Test
    void testUpdateAnomaly_success() {
        // Prepare test input
        Map<String, String> metadata = new HashMap<>();
        metadata.put("serviceIdentifier", "CC_Service");
        AnomalyAccountPojo anomalyAccountPojo = AnomalyAccountPojo.builder()
                .accountIdentifier("demo")
                .anomalyDetails(Anomalies.builder()
                        .anomalyId("AE-3-75-2-C-S-64695-********")
                        .anomalyStartTime(System.currentTimeMillis())
                        .anomalyEndTime(System.currentTimeMillis() + 5)
                        .anomalyCreatedTime(System.currentTimeMillis())
                        .anomalyStatus(null)
                        .categoryId("123")
                        .identifiedTime(System.currentTimeMillis() - 5)
                        .entityType("INSTANCE") //Either Instance or Transaction
//                        .entityType("TRANSACTION")
                        .entityId("65b5d4b0-cf19-4233-9b2f-9d45ab6f545f")
                        .startSeverityId(431)
                        .lastSeverityId(432)
                        .lastAlertTime(System.currentTimeMillis())
                        .kpiAttribute("64695")
                        .kpiId(2)
                        .thresholdType("SOR")
                        .metadata(metadata)
                        .build())
                .build();

        Alerts alerts = Alerts.builder()
                .anomalyId("AE-3-75-2-C-S-64695-********")
                .alertTime(System.currentTimeMillis())
                .alertStatus(null)
                .categoryId("123")
                .identifiedTime(System.currentTimeMillis() - 5)
                .entityType("INSTANCE") //Either Instance or Transaction
//                        .entityType("TRANSACTION")
                .entityId("65b5d4b0-cf19-4233-9b2f-9d45ab6f545f")
                .severityId("431")
                .alertTime(System.currentTimeMillis())
                .kpiAttribute("64695")
                .kpiId(2)
                .thresholdType("SOR")
                .build();

        // Add a ViolationDetails instance (can be empty for this test)
        ViolationDetails violationDetails = ViolationDetails.builder().build();

        // Act
        AnomalyAccountPojo updatedAnomalyAccountPojo = anomalyService.updateAnomaly(anomalyAccountPojo, alerts, new ArrayList<>(), violationDetails);

        // Assert
        assertNotNull(updatedAnomalyAccountPojo);
    }
}
