package com.heal.event.detector.util;

import com.heal.event.detector.utility.TestClass;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

class TestClassTest {

    private TestClass.AnomalyProcessor anomalyProcessor;
    private LocalDateTime testStartTime; // Use a fixed start time for consistent timestamp generation

    // Common thresholds for testing
    private List<TestClass.Threshold> serviceThresholds;
    private List<TestClass.Threshold> instanceThresholds;
    private List<TestClass.Threshold> mixedThresholds; // Contains both SERVICE and INSTANCE

    // Common anomaly configuration (maxNoViolationCount, maxAllowedDataBreaks)
    private static final int MAX_NO_VIOLATION_COUNT = 5;
    private static final int MAX_ALLOWED_DATA_BREAKS = 3;
    private static final int COLLECTION_INTERVAL_SECONDS = 60;

    @BeforeEach
    void setUp() {
        anomalyProcessor = new TestClass.AnomalyProcessor();
        testStartTime = LocalDateTime.of(2023, 1, 1, 10, 0, 0); // Fixed start time

        // Define common thresholds
        // Persistence, Suppression
        serviceThresholds = List.of(
                new TestClass.Threshold("CPU High Service", TestClass.Operation.GREATER_THAN, TestClass.Severity.HIGH, 0, 90, TestClass.ViolationLevel.SERVICE, 3, 2),
                new TestClass.Threshold("CPU Critical Service", TestClass.Operation.GREATER_THAN, TestClass.Severity.CRITICAL, 0, 95, TestClass.ViolationLevel.SERVICE, 2, 1)
        );

        instanceThresholds = List.of(
                new TestClass.Threshold("CPU High Instance", TestClass.Operation.GREATER_THAN, TestClass.Severity.HIGH, 0, 85, TestClass.ViolationLevel.INSTANCE, 2, 1),
                new TestClass.Threshold("CPU Critical Instance", TestClass.Operation.GREATER_THAN, TestClass.Severity.CRITICAL, 0, 92, TestClass.ViolationLevel.INSTANCE, 1, 1)

        );

        mixedThresholds = List.of(
                new TestClass.Threshold("CPU High Service", TestClass.Operation.GREATER_THAN, TestClass.Severity.HIGH, 0, 90, TestClass.ViolationLevel.SERVICE, 3, 2),
                new TestClass.Threshold("CPU Critical Service", TestClass.Operation.GREATER_THAN, TestClass.Severity.CRITICAL, 0, 95, TestClass.ViolationLevel.SERVICE, 2, 1),
                new TestClass.Threshold("CPU High Instance", TestClass.Operation.GREATER_THAN, TestClass.Severity.HIGH, 0, 85, TestClass.ViolationLevel.INSTANCE, 2, 1),
                new TestClass.Threshold("CPU Critical Instance", TestClass.Operation.GREATER_THAN, TestClass.Severity.CRITICAL, 0, 92, TestClass.ViolationLevel.INSTANCE, 1, 1)
        );
    }

    /**
     * Helper method to simulate processing a KPI value.
     * Returns the AnomalyProcessingResult.
     */
    private TestClass.AnomalyProcessingResult processKpi(
            TestClass.ViolationState state,
            double kpiValue,
            int minutesOffset,
            boolean isDataPresent,
            List<TestClass.Threshold> currentThresholds) {
        LocalDateTime currentTimestamp = testStartTime.plusMinutes(minutesOffset);
        return anomalyProcessor.process(state, currentThresholds, kpiValue, currentTimestamp, isDataPresent);
    }

    @Nested
    @DisplayName("Anomaly Lifecycle Tests (Service Level Thresholds)")
    class AnomalyLifecycleTests {

        private TestClass.ViolationState state;

        @BeforeEach
        void setup() {
            state = new TestClass.ViolationState("Host", "prod-db-01", COLLECTION_INTERVAL_SECONDS,
                    MAX_NO_VIOLATION_COUNT, MAX_ALLOWED_DATA_BREAKS, serviceThresholds);
        }

        @Test
        @DisplayName("should not open an anomaly if persistence threshold is not met")
        void shouldNotOpenAnomalyWhenPersistenceIsNotMet() {
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 92.0, 0, true, serviceThresholds); // HIGH count = 1
            state = result1.getFinalState();
            assertNull(result1.getAlertDetails());
            assertNull(result1.getFinalState().getCurrentAnomaly());
            assertNull(result1.getClosedState());

            TestClass.AnomalyProcessingResult result2 = processKpi(state, 93.0, 1, true, serviceThresholds); // HIGH count = 2
            state = result2.getFinalState();
            assertNull(result2.getAlertDetails());
            assertNull(result2.getFinalState().getCurrentAnomaly());
            assertNull(result2.getClosedState());
        }

        @Test
        @DisplayName("should open an anomaly when persistence threshold is met")
        void shouldOpenAnomalyWhenPersistenceIsMet() {
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 92.0, 0, true, serviceThresholds); // HIGH count = 1
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult result2 = processKpi(state, 93.0, 1, true, serviceThresholds); // HIGH count = 2
            state = result2.getFinalState();
            TestClass.AnomalyProcessingResult result3 = processKpi(state, 91.0, 2, true, serviceThresholds); // HIGH count = 3 (Persistence met)
            state = result3.getFinalState();

            assertNotNull(result3.getAlertDetails());
            assertThat(result3.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.OPEN);
            assertThat(result3.getAlertDetails().getReason()).isEqualTo("Persistence met");
            assertThat(result3.getAlertDetails().getTriggeringState().getThresholdRule().getName()).isEqualTo("CPU High Service");
            assertThat(result3.getFinalState().getCurrentAnomaly()).isNotNull();
            assertThat(result3.getFinalState().getCurrentAnomaly().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.OPEN);
            assertThat(result3.getFinalState().getCurrentAnomaly().getFirstSeverity()).isEqualTo(TestClass.Severity.HIGH);
            assertNull(result3.getClosedState());
        }

        @Test
        @DisplayName("should update anomaly when suppression threshold is met")
        void shouldUpdateAnomalyWhenSuppressionIsMet() {
            // Open anomaly first (HIGH persistence = 3)
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 92.0, 0, true, serviceThresholds);
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult result2 = processKpi(state, 93.0, 1, true, serviceThresholds);
            state = result2.getFinalState();
            TestClass.AnomalyProcessingResult openResult = processKpi(state, 91.0, 2, true, serviceThresholds); // Anomaly OPENED (HIGH)
            state = openResult.getFinalState();

            // Continue violating, but not yet meeting suppression (suppression = 2)
            // This is the 4th violation overall, and the 1st after persistence.
            TestClass.AnomalyProcessingResult noAlertResult = processKpi(state, 92.0, 3, true, serviceThresholds);
            state = noAlertResult.getFinalState();
            assertNull(noAlertResult.getAlertDetails()); // No new alert
            assertNull(noAlertResult.getClosedState());

            // This is the 5th violation overall, and the 2nd after persistence, so suppression is met.
            TestClass.AnomalyProcessingResult updateResult = processKpi(state, 93.0, 4, true, serviceThresholds);
            state = updateResult.getFinalState();
            assertNotNull(updateResult.getAlertDetails());
            assertThat(updateResult.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.ONGOING);
            assertThat(updateResult.getAlertDetails().getReason()).isEqualTo("Suppression met / Escalation");
            assertThat(updateResult.getAlertDetails().getTriggeringState().getThresholdRule().getName()).isEqualTo("CPU High Service");
            assertThat(updateResult.getFinalState().getCurrentAnomaly().getNumberOfAlerts()).isEqualTo(2); // Initial + 1 update
            assertNull(updateResult.getClosedState());
        }

        @Test
        @DisplayName("should escalate anomaly severity when a higher severity threshold is met")
        void shouldEscalateAnomalySeverity() {
            // Open anomaly with HIGH severity (persistence = 3)
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 92.0, 0, true, serviceThresholds);
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult result2 = processKpi(state, 93.0, 1, true, serviceThresholds);
            state = result2.getFinalState();
            TestClass.AnomalyProcessingResult openResult = processKpi(state, 91.0, 2, true, serviceThresholds); // Anomaly OPENED (HIGH)
            state = openResult.getFinalState();
            assertThat(openResult.getFinalState().getCurrentAnomaly().getCurrentSeverity()).isEqualTo(TestClass.Severity.HIGH);

            // Now breach CRITICAL threshold (persistence = 2)
            TestClass.AnomalyProcessingResult result3 = processKpi(state, 96.0, 3, true, serviceThresholds); // CRITICAL count = 1
            state = result3.getFinalState();
            TestClass.AnomalyProcessingResult escalateResult = processKpi(state, 97.0, 4, true, serviceThresholds); // CRITICAL count = 2 (Persistence met)
            state = escalateResult.getFinalState();

            assertNotNull(escalateResult.getAlertDetails());
            assertThat(escalateResult.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.ONGOING);
            assertThat(escalateResult.getAlertDetails().getReason()).isEqualTo("Suppression met / Escalation"); // Reason for escalation
            assertThat(escalateResult.getAlertDetails().getTriggeringState().getThresholdRule().getName()).isEqualTo("CPU Critical Service");
            assertThat(escalateResult.getFinalState().getCurrentAnomaly().getCurrentSeverity()).isEqualTo(TestClass.Severity.CRITICAL);
            assertNull(escalateResult.getClosedState());
        }

        @Test
        @DisplayName("should close anomaly when max no violation count is met")
        void shouldCloseAnomalyWhenMaxNoViolationCountIsMet() {
            // Open anomaly first
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 92.0, 0, true, serviceThresholds);
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult result2 = processKpi(state, 93.0, 1, true, serviceThresholds);
            state = result2.getFinalState();
            TestClass.AnomalyProcessingResult openResult = processKpi(state, 91.0, 2, true, serviceThresholds); // Anomaly OPENED
            state = openResult.getFinalState();

            // Send non-violating data for MAX_NO_VIOLATION_COUNT - 1 times
            for (int i = 0; i < MAX_NO_VIOLATION_COUNT - 1; i++) {
                TestClass.AnomalyProcessingResult result = processKpi(state, 50.0, 3 + i, true, serviceThresholds); // No violation
                state = result.getFinalState();
                assertNull(result.getAlertDetails());
                assertThat(result.getFinalState().getCurrentAnomaly().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.OPEN); // Still open
                assertNull(result.getClosedState());
            }

            // Send one more non-violating data point to meet MAX_NO_VIOLATION_COUNT
            TestClass.AnomalyProcessingResult closeResult = processKpi(state, 50.0, 3 + MAX_NO_VIOLATION_COUNT - 1, true, serviceThresholds);
            state = closeResult.getFinalState(); // State should now have closed anomaly

            assertNotNull(closeResult.getAlertDetails());
            assertThat(closeResult.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.CLOSED);
            assertThat(closeResult.getAlertDetails().getReason()).contains("max no-violation count");
            assertThat(closeResult.getFinalState().getCurrentAnomaly().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.CLOSED);
            assertNull(closeResult.getAlertDetails().getTriggeringState()); // No specific threshold triggered closure
            assertNotNull(closeResult.getClosedState()); // Old state should be populated on closure
            assertThat(closeResult.getClosedState().getCurrentAnomaly().getAnomalyId()).isEqualTo(closeResult.getAlertDetails().getAnomalyId());
        }
    }

    @Nested
    @DisplayName("Data Break Tests")
    class DataBreakTests {
        private TestClass.ViolationState state;

        @BeforeEach
        void setup() {
            state = new TestClass.ViolationState("Host", "prod-db-03", COLLECTION_INTERVAL_SECONDS,
                    MAX_NO_VIOLATION_COUNT, MAX_ALLOWED_DATA_BREAKS, serviceThresholds);

        }

        @Test
        @DisplayName("should preserve violation count during allowed data breaks")
        void shouldPreserveViolationCountDuringAllowedDataBreaks() {
            // Breach twice
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 92.0, 0, true, serviceThresholds); // HIGH count = 1
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult result2 = processKpi(state, 93.0, 1, true, serviceThresholds); // HIGH count = 2
            state = result2.getFinalState();
            assertThat(state.getDetailsByThresholdName().get("CPU High Service").getViolationCount()).isEqualTo(2);

            // Simulate data breaks within allowed limit (MAX_ALLOWED_DATA_BREAKS = 3)
            TestClass.AnomalyProcessingResult result3 = processKpi(state, 0.0, 2, false, serviceThresholds); // Data break 1
            state = result3.getFinalState();
            TestClass.AnomalyProcessingResult result4 = processKpi(state, 0.0, 3, false, serviceThresholds); // Data break 2
            state = result4.getFinalState();
            assertThat(state.getMaxDataBreaks()).isEqualTo(2);
            // Count should be preserved (frozen), not reset.
            assertThat(state.getDetailsByThresholdName().get("CPU High Service").getViolationCount()).isEqualTo(2);
            assertNull(result3.getClosedState());
            assertNull(result4.getClosedState());


            // Resume violation after allowed data breaks. This should be the 3rd violation, opening the anomaly.
            TestClass.AnomalyProcessingResult resumeResult = processKpi(state, 94.0, 4, true, serviceThresholds); // HIGH count = 3 (continuous)
            state = resumeResult.getFinalState();
            assertNotNull(resumeResult.getAlertDetails());
            assertThat(resumeResult.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.OPEN);
            assertThat(resumeResult.getFinalState().getDetailsByThresholdName().get("CPU High Service").getViolationCount()).isEqualTo(3);
            assertThat(resumeResult.getFinalState().getMaxDataBreaks()).isEqualTo(0); // Data breaks reset
            assertNull(resumeResult.getClosedState());
        }

        @Test
        @DisplayName("should close anomaly when max data breaks exceeded")
        void shouldCloseAnomalyWhenMaxDataBreaksExceeded() {
            // Open anomaly
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 92.0, 0, true, serviceThresholds);
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult result2 = processKpi(state, 93.0, 1, true, serviceThresholds);
            state = result2.getFinalState();
            TestClass.AnomalyProcessingResult openResult = processKpi(state, 91.0, 2, true, serviceThresholds); // Anomaly OPENED
            state = openResult.getFinalState();

            // Simulate data breaks up to MAX_ALLOWED_DATA_BREAKS
            for (int i = 0; i < MAX_ALLOWED_DATA_BREAKS; i++) { // Loop runs 3 times for MAX_ALLOWED_DATA_BREAKS = 3
                TestClass.AnomalyProcessingResult result = processKpi(state, 0.0, 3 + i, false, serviceThresholds);
                state = result.getFinalState();
                assertNull(result.getAlertDetails()); // No alert yet
                assertThat(result.getFinalState().getCurrentAnomaly().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.OPEN);
                assertNull(result.getClosedState());
            }
            assertThat(state.getMaxDataBreaks()).isEqualTo(MAX_ALLOWED_DATA_BREAKS); // Max data breaks reached

            // Simulate one more data break to exceed MAX_ALLOWED_DATA_BREAKS (this is the 4th data break)
            TestClass.AnomalyProcessingResult closeResult = processKpi(state, 0.0, 3 + MAX_ALLOWED_DATA_BREAKS, false, serviceThresholds);
            state = closeResult.getFinalState(); // State should now have closed anomaly

            assertNotNull(closeResult.getAlertDetails());
            assertThat(closeResult.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.CLOSED);
            assertThat(closeResult.getAlertDetails().getReason()).isEqualTo("Max data breaks exceeded (4)"); // 3 allowed + 1 to exceed = 4
            assertThat(closeResult.getFinalState().getCurrentAnomaly().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.CLOSED);
            assertNotNull(closeResult.getClosedState()); // Old state should be populated on closure
            assertThat(closeResult.getClosedState().getCurrentAnomaly().getAnomalyId()).isEqualTo(closeResult.getAlertDetails().getAnomalyId());
        }

        @Test
        @DisplayName("should reset violation count after exceeding data breaks")
        void shouldResetViolationCountAfterExceedingDataBreaks() {
            // Open anomaly
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 92.0, 0, true, serviceThresholds);
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult result2 = processKpi(state, 93.0, 1, true, serviceThresholds);
            state = result2.getFinalState();
            TestClass.AnomalyProcessingResult openResult = processKpi(state, 91.0, 2, true, serviceThresholds); // Anomaly OPENED
            state = openResult.getFinalState();

            // Exceed max data breaks, anomaly closes
            for (int i = 0; i < MAX_ALLOWED_DATA_BREAKS + 1; i++) { // Loop runs 4 times to exceed
                TestClass.AnomalyProcessingResult result = processKpi(state, 0.0, 3 + i, false, serviceThresholds);
                state = result.getFinalState();
            }
            assertThat(state.getCurrentAnomaly().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.CLOSED);

            // Now, send violating data again. It should start a new sequence.
            TestClass.AnomalyProcessingResult newViolationResult = processKpi(state, 92.0, 3 + MAX_ALLOWED_DATA_BREAKS + 1, true, serviceThresholds);
            state = newViolationResult.getFinalState();
            assertThat(newViolationResult.getFinalState().getDetailsByThresholdName().get("CPU High Service").getViolationCount()).isEqualTo(1);
            assertNull(newViolationResult.getAlertDetails()); // Not yet persistent
            assertNull(newViolationResult.getFinalState().getCurrentAnomaly()); // Anomaly is closed and not re-opened yet
            assertNull(newViolationResult.getClosedState()); // No closure in this specific step
        }
    }


    @Nested
    @DisplayName("Configuration Change Tests")
    class ConfigurationChangeTests {
        private TestClass.ViolationState state;

        @BeforeEach
        void setup() {
            state = new TestClass.ViolationState("Host", "prod-db-04", COLLECTION_INTERVAL_SECONDS,
                    MAX_NO_VIOLATION_COUNT, MAX_ALLOWED_DATA_BREAKS, serviceThresholds);
        }

        @Test
        @DisplayName("should close anomaly and apply new config when threshold rule changes")
        void shouldCloseAnomalyAndApplyNewConfigWhenThresholdRuleChanges() {
            // Open anomaly with original thresholds (CPU High Service P=3)
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 92.0, 0, true, serviceThresholds);
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult result2 = processKpi(state, 93.0, 1, true, serviceThresholds);
            state = result2.getFinalState();
            TestClass.AnomalyProcessingResult openResult = processKpi(state, 91.0, 2, true, serviceThresholds);
            state = openResult.getFinalState();
            String originalAnomalyId = openResult.getFinalState().getCurrentAnomaly().getAnomalyId();

            // Create new thresholds with changed P&S (P=1)
            List<TestClass.Threshold> updatedServiceThresholds = List.of(
                    new TestClass.Threshold("CPU High Service", TestClass.Operation.GREATER_THAN, TestClass.Severity.HIGH, 0, 90, TestClass.ViolationLevel.SERVICE, 1, 0),
                    new TestClass.Threshold("CPU Critical Service", TestClass.Operation.GREATER_THAN, TestClass.Severity.CRITICAL, 0, 95, TestClass.ViolationLevel.SERVICE, 2, 1)
            );

            // Step 1 - Trigger config change with a NON-VIOLATING value to test closure
            TestClass.AnomalyProcessingResult configChangeResult = processKpi(state, 50.0, 3, true, updatedServiceThresholds);
            state = configChangeResult.getFinalState(); // state is now the NEWLY CREATED state

            // Verify anomaly closed
            assertNotNull(configChangeResult.getAlertDetails());
            assertThat(configChangeResult.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.CLOSED);
            assertThat(configChangeResult.getAlertDetails().getReason()).isEqualTo("Configuration change detected");
            assertThat(configChangeResult.getAlertDetails().getAnomalyId()).isEqualTo(originalAnomalyId);
            assertNotNull(configChangeResult.getClosedState()); // Old state should be populated
            assertThat(configChangeResult.getClosedState().getCurrentAnomaly().getAnomalyId()).isEqualTo(originalAnomalyId);


            // Verify state is reset and new config applied in the finalState
            assertNull(configChangeResult.getFinalState().getCurrentAnomaly()); // Anomaly is closed and state reset
            assertThat(configChangeResult.getFinalState().getDetailsByThresholdName().get("CPU High Service").getViolationCount()).isEqualTo(0); // Counts reset
            assertThat(configChangeResult.getFinalState().getDetailsByThresholdName().get("CPU High Service").getThresholdRule().getPersistenceThreshold()).isEqualTo(1); // New P&S applied

            // Step 2 - Send a VIOLATING value to test re-opening with new rules
            TestClass.AnomalyProcessingResult newOpenResult = processKpi(state, 92.0, 4, true, updatedServiceThresholds);
            state = newOpenResult.getFinalState();
            assertNotNull(newOpenResult.getAlertDetails());
            assertThat(newOpenResult.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.OPEN);
            assertThat(newOpenResult.getAlertDetails().getTriggeringState().getThresholdRule().getPersistenceThreshold()).isEqualTo(1);
            assertNull(newOpenResult.getClosedState()); // No closure in this step
        }

        @Test
        @DisplayName("should close anomaly and apply new config when a threshold rule is added")
        void shouldCloseAnomalyAndApplyNewConfigWhenThresholdRuleAdded() {
            // Open anomaly with original thresholds
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 92.0, 0, true, serviceThresholds);
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult result2 = processKpi(state, 93.0, 1, true, serviceThresholds);
            state = result2.getFinalState();
            TestClass.AnomalyProcessingResult openResult = processKpi(state, 91.0, 2, true, serviceThresholds); // Anomaly OPENED
            state = openResult.getFinalState();
            String originalAnomalyId = openResult.getFinalState().getCurrentAnomaly().getAnomalyId();

            // Add a new threshold rule
            List<TestClass.Threshold> thresholdsWithNewRule = List.of(
                    new TestClass.Threshold("CPU High Service", TestClass.Operation.GREATER_THAN, TestClass.Severity.HIGH, 0, 90, TestClass.ViolationLevel.SERVICE, 3, 2),
                    new TestClass.Threshold("CPU Critical Service", TestClass.Operation.GREATER_THAN, TestClass.Severity.CRITICAL, 0, 95, TestClass.ViolationLevel.SERVICE, 2, 1),
                    new TestClass.Threshold("Memory Low Service", TestClass.Operation.LESS_THAN, TestClass.Severity.LOW, 10, 0, TestClass.ViolationLevel.SERVICE, 1, 0) // New rule
            );

            // Process with new config (non-violating KPI)
            TestClass.AnomalyProcessingResult configChangeResult = processKpi(state, 50.0, 3, true, thresholdsWithNewRule);
            state = configChangeResult.getFinalState();

            // Verify anomaly closed
            assertNotNull(configChangeResult.getAlertDetails());
            assertThat(configChangeResult.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.CLOSED);
            assertThat(configChangeResult.getAlertDetails().getReason()).isEqualTo("Configuration change detected");
            assertThat(configChangeResult.getAlertDetails().getAnomalyId()).isEqualTo(originalAnomalyId);
            assertNotNull(configChangeResult.getClosedState());

            // Verify state is reset and new rule is present in finalState
            assertNull(configChangeResult.getFinalState().getCurrentAnomaly());
            assertThat(configChangeResult.getFinalState().getDetailsByThresholdName().get("CPU High Service").getViolationCount()).isEqualTo(0);
            assertThat(configChangeResult.getFinalState().getDetailsByThresholdName()).containsKey("Memory Low Service");
            assertThat(configChangeResult.getFinalState().getDetailsByThresholdName().get("Memory Low Service").getViolationCount()).isEqualTo(0);

            // Verify new violation sequence can start with new rules
            TestClass.AnomalyProcessingResult newOpenResult = processKpi(state, 5.0, 4, true, thresholdsWithNewRule); // Breaches Memory Low
            state = newOpenResult.getFinalState();
            assertNotNull(newOpenResult.getAlertDetails());
            assertThat(newOpenResult.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.OPEN);
            assertThat(newOpenResult.getAlertDetails().getTriggeringState().getThresholdRule().getName()).isEqualTo("Memory Low Service");
            assertNull(newOpenResult.getClosedState());
        }

        @Test
        @DisplayName("should close anomaly and apply new config when a threshold rule is removed")
        void shouldCloseAnomalyAndApplyNewConfigWhenThresholdRuleRemoved() {
            // Start with a state initialized with two thresholds
            List<TestClass.Threshold> initialThresholds = List.of(
                    new TestClass.Threshold("CPU High Service", TestClass.Operation.GREATER_THAN, TestClass.Severity.HIGH, 0, 90, TestClass.ViolationLevel.SERVICE, 3, 2),
                    new TestClass.Threshold("Memory Low Service", TestClass.Operation.LESS_THAN, TestClass.Severity.LOW, 10, 0, TestClass.ViolationLevel.SERVICE, 1, 0)
            );
            state = new TestClass.ViolationState("Host", "prod-db-05", COLLECTION_INTERVAL_SECONDS,
                    MAX_NO_VIOLATION_COUNT, MAX_ALLOWED_DATA_BREAKS, initialThresholds);

            // Open anomaly based on "CPU High Service"
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 92.0, 0, true, initialThresholds);
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult result2 = processKpi(state, 93.0, 1, true, initialThresholds);
            state = result2.getFinalState();
            TestClass.AnomalyProcessingResult openResult = processKpi(state, 91.0, 2, true, initialThresholds); // Anomaly OPENED
            state = openResult.getFinalState();
            String originalAnomalyId = openResult.getFinalState().getCurrentAnomaly().getAnomalyId();
            assertThat(openResult.getFinalState().getDetailsByThresholdName()).containsKey("Memory Low Service");

            // Remove "Memory Low Service" threshold
            List<TestClass.Threshold> thresholdsWithoutOneRule = List.of(
                    new TestClass.Threshold("CPU High Service", TestClass.Operation.GREATER_THAN, TestClass.Severity.HIGH, 0, 90, TestClass.ViolationLevel.SERVICE, 3, 2)
            );

            // Process with new config (non-violating KPI)
            TestClass.AnomalyProcessingResult configChangeResult = processKpi(state, 50.0, 3, true, thresholdsWithoutOneRule);
            state = configChangeResult.getFinalState();

            // Verify anomaly closed
            assertNotNull(configChangeResult.getAlertDetails());
            assertThat(configChangeResult.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.CLOSED);
            assertThat(configChangeResult.getAlertDetails().getReason()).isEqualTo("Configuration change detected");
            assertThat(configChangeResult.getAlertDetails().getAnomalyId()).isEqualTo(originalAnomalyId);
            assertNotNull(configChangeResult.getClosedState());

            // Verify state is reset and removed rule is gone from finalState
            assertNull(configChangeResult.getFinalState().getCurrentAnomaly());
            assertThat(configChangeResult.getFinalState().getDetailsByThresholdName()).doesNotContainKey("Memory Low Service");
            assertThat(configChangeResult.getFinalState().getDetailsByThresholdName()).containsKey("CPU High Service");

            // Verify new violation sequence can start with remaining rules
            TestClass.AnomalyProcessingResult newOpenResult = processKpi(state, 92.0, 4, true, thresholdsWithoutOneRule);
            state = newOpenResult.getFinalState();
            assertNull(newOpenResult.getAlertDetails()); // Not persistent yet
            assertNull(newOpenResult.getClosedState());
        }

        @Test
        @DisplayName("should not close anomaly if config is identical")
        void shouldNotCloseAnomalyIfConfigIsIdentical() {
            // Open anomaly
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 92.0, 0, true, serviceThresholds);
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult result2 = processKpi(state, 93.0, 1, true, serviceThresholds);
            state = result2.getFinalState();
            TestClass.AnomalyProcessingResult openResult = processKpi(state, 91.0, 2, true, serviceThresholds); // Anomaly OPENED
            state = openResult.getFinalState();
            String originalAnomalyId = openResult.getFinalState().getCurrentAnomaly().getAnomalyId();

            // Process with identical config
            TestClass.AnomalyProcessingResult noChangeResult = processKpi(state, 92.0, 3, true, serviceThresholds);
            state = noChangeResult.getFinalState();

            // Verify anomaly is still open and no new alert generated
            assertNull(noChangeResult.getAlertDetails());
            assertThat(noChangeResult.getFinalState().getCurrentAnomaly().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.OPEN);
            assertThat(noChangeResult.getFinalState().getCurrentAnomaly().getAnomalyId()).isEqualTo(originalAnomalyId);
            assertNull(noChangeResult.getClosedState());
        }
    }

    @Nested
    @DisplayName("Violation Level Precedence Tests")
    class ViolationLevelPrecedenceTests {
        private TestClass.ViolationState state;

        @BeforeEach
        void setup() {
            state = new TestClass.ViolationState("Host", "prod-db-06", COLLECTION_INTERVAL_SECONDS,
                    MAX_NO_VIOLATION_COUNT, MAX_ALLOWED_DATA_BREAKS, mixedThresholds); // Start with mixed
        }

        @Test
        @DisplayName("should use instance thresholds when present")
        void shouldUseInstanceThresholdsWhenPresent() {
            // KPI value breaches Instance HIGH (85) but not Service HIGH (90)
            // Instance HIGH P=2
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 86.0, 0, true, mixedThresholds); // Instance HIGH count = 1
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult result2 = processKpi(state, 87.0, 1, true, mixedThresholds); // Instance HIGH count = 2 (Persistence met)
            state = result2.getFinalState();

            assertNotNull(result2.getAlertDetails());
            assertThat(result2.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.OPEN);
            assertThat(result2.getAlertDetails().getTriggeringState().getThresholdRule().getName()).isEqualTo("CPU High Instance");
            assertThat(result2.getFinalState().getCurrentViolationLevel()).isEqualTo(TestClass.ViolationLevel.INSTANCE);
            assertNull(result2.getClosedState());
        }

        @Test
        @DisplayName("should fall back to service thresholds when instance thresholds are not present")
        void shouldFallBackToServiceThresholdsWhenInstanceNotPresent() {
            // Initialize state with only service thresholds
            state = new TestClass.ViolationState("Host", "prod-db-07", COLLECTION_INTERVAL_SECONDS,
                    MAX_NO_VIOLATION_COUNT, MAX_ALLOWED_DATA_BREAKS, serviceThresholds);

            // KPI value breaches Service HIGH (90)
            // Service HIGH P=3
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 91.0, 0, true, serviceThresholds); // Service HIGH count = 1
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult result2 = processKpi(state, 92.0, 1, true, serviceThresholds); // Service HIGH count = 2
            state = result2.getFinalState();
            TestClass.AnomalyProcessingResult result3 = processKpi(state, 93.0, 2, true, serviceThresholds); // Service HIGH count = 3 (Persistence met)
            state = result3.getFinalState();

            assertNotNull(result3.getAlertDetails());
            assertThat(result3.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.OPEN);
            assertThat(result3.getAlertDetails().getTriggeringState().getThresholdRule().getName()).isEqualTo("CPU High Service");
            assertThat(result3.getFinalState().getCurrentViolationLevel()).isEqualTo(TestClass.ViolationLevel.SERVICE);
            assertNull(result3.getClosedState());
        }

        @Test
        @DisplayName("should close anomaly and reset state when violation level changes")
        void shouldCloseAnomalyAndResetStateWhenViolationLevelChanges() {
            // Open anomaly using INSTANCE thresholds (P=2)
            TestClass.AnomalyProcessingResult result1 = processKpi(state, 86.0, 0, true, mixedThresholds);
            state = result1.getFinalState();
            TestClass.AnomalyProcessingResult openResult = processKpi(state, 87.0, 1, true, mixedThresholds);
            state = openResult.getFinalState();
            String originalAnomalyId = openResult.getFinalState().getCurrentAnomaly().getAnomalyId();
            assertThat(openResult.getFinalState().getCurrentViolationLevel()).isEqualTo(TestClass.ViolationLevel.INSTANCE);

            // Step 1 - Trigger level change with a NON-VIOLATING value
            // Now, process with only SERVICE thresholds (simulating instance thresholds being removed)
            TestClass.AnomalyProcessingResult levelChangeResult = processKpi(state, 50.0, 2, true, serviceThresholds);
            state = levelChangeResult.getFinalState(); // state is now the NEWLY CREATED state

            // Verify anomaly closed due to level change
            assertNotNull(levelChangeResult.getAlertDetails());
            assertThat(levelChangeResult.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.CLOSED);
            assertThat(levelChangeResult.getAlertDetails().getReason()).isEqualTo("Violation level changed");
            assertThat(levelChangeResult.getAlertDetails().getAnomalyId()).isEqualTo(originalAnomalyId);
            assertNotNull(levelChangeResult.getClosedState()); // Old state should be populated
            assertThat(levelChangeResult.getClosedState().getCurrentAnomaly().getAnomalyId()).isEqualTo(originalAnomalyId);

            // Verify state is reset and new level is applied in finalState
            assertNull(levelChangeResult.getFinalState().getCurrentAnomaly());
            assertThat(levelChangeResult.getFinalState().getDetailsByThresholdName().get("CPU High Service").getViolationCount()).isEqualTo(0); // Counts reset
            assertThat(levelChangeResult.getFinalState().getCurrentViolationLevel()).isEqualTo(TestClass.ViolationLevel.SERVICE);

            // Step 2 - Send VIOLATING value to test re-opening with SERVICE rules
            // Service HIGH P=3
            TestClass.AnomalyProcessingResult result2_1 = processKpi(state, 91.0, 3, true, serviceThresholds);
            state = result2_1.getFinalState();
            TestClass.AnomalyProcessingResult result2_2 = processKpi(state, 92.0, 4, true, serviceThresholds);
            state = result2_2.getFinalState();
            TestClass.AnomalyProcessingResult newOpenResult = processKpi(state, 93.0, 5, true, serviceThresholds);
            state = newOpenResult.getFinalState();
            assertNotNull(newOpenResult.getAlertDetails());
            assertThat(newOpenResult.getAlertDetails().getStatus()).isEqualTo(TestClass.AnomalySummary.AnomalyState.OPEN);
            assertThat(newOpenResult.getAlertDetails().getTriggeringState().getThresholdRule().getName()).isEqualTo("CPU High Service");
            assertNull(newOpenResult.getClosedState()); // No closure in this step
        }
    }

    @Nested
    @DisplayName("Edge Case Tests")
    class EdgeCaseTests {
        private TestClass.ViolationState state;

        @BeforeEach
        void setup() {
            state = new TestClass.ViolationState("Host", "prod-db-08", COLLECTION_INTERVAL_SECONDS,
                    MAX_NO_VIOLATION_COUNT, MAX_ALLOWED_DATA_BREAKS, serviceThresholds);
        }

        @Test
        @DisplayName("should handle initial state with no violations")
        void shouldHandleInitialStateWithNoViolations() {
            TestClass.AnomalyProcessingResult result = processKpi(state, 50.0, 0, true, serviceThresholds);
            state = result.getFinalState();
            assertNull(result.getAlertDetails());
            assertNull(result.getFinalState().getCurrentAnomaly());
            assertThat(result.getFinalState().getNoViolationCount()).isEqualTo(1);
            assertThat(result.getFinalState().getDetailsByThresholdName().get("CPU High Service").getNoViolationCount()).isEqualTo(1);
            assertNull(result.getClosedState());
        }

        @Test
        @DisplayName("should handle no thresholds configured")
        void shouldHandleNoThresholdsConfigured() {
            TestClass.ViolationState emptyThresholdState = new TestClass.ViolationState("Host", "prod-db-09", COLLECTION_INTERVAL_SECONDS,
                    MAX_NO_VIOLATION_COUNT, MAX_ALLOWED_DATA_BREAKS, Collections.emptyList());

            TestClass.AnomalyProcessingResult result = processKpi(emptyThresholdState, 50.0, 0, true, Collections.emptyList());
            state = result.getFinalState();
            assertNull(result.getAlertDetails());
            assertNull(result.getFinalState().getCurrentAnomaly());
            assertThat(result.getFinalState().getDetailsByThresholdName()).isEmpty();
            assertThat(result.getFinalState().getNoViolationCount()).isEqualTo(1);
            assertNull(result.getClosedState());
        }

        @Test
        @DisplayName("should handle KPI value not breaching any threshold")
        void shouldHandleKpiNotBreachingAnyThreshold() {

            // Send multiple non-breaching values
            for (int i = 0; i < MAX_NO_VIOLATION_COUNT + 2; i++) {
                TestClass.AnomalyProcessingResult result = processKpi(state, 50.0, i, true, serviceThresholds);
                state = result.getFinalState();
                assertNull(result.getAlertDetails());
                assertNull(result.getFinalState().getCurrentAnomaly());
                assertThat(result.getFinalState().getNoViolationCount()).isEqualTo(i + 1);
                assertNull(result.getClosedState());
            }
        }
    }
}