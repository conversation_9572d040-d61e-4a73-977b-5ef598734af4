package com.heal.event.detector.core;

import com.appnomic.appsone.common.protbuf.KPIAgentMessageProtos;
import com.appnomic.appsone.common.protbuf.ViolatedEventProtos;
import com.heal.configuration.pojos.*;
import com.heal.event.detector.pojos.AnomalyAccountPojo;
import com.heal.event.detector.pojos.ViolatedData;
import com.heal.event.detector.pojos.enums.PersistenceSuppressionStatus;
import com.heal.event.detector.pojos.enums.ViolationEventType;
import com.heal.event.detector.repo.RedisUtilities;
import com.heal.event.detector.util.ProtoCreator;
import com.heal.event.detector.utility.Constants;
import com.heal.event.detector.utility.cache.CacheWrapper;
import com.heal.configuration.enums.OperationType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
public class GetViolatedDataTest {

    @Autowired
    ProtoCreator protoCreator;

    @Mock
    RedisUtilities redisUtilitiesMock;
    @Mock
    CacheWrapper cacheWrapperMock;
    @Mock
    AnomalyManagementService anomalyManagementService;
    @Mock
    PersistenceSuppression persistenceSuppression;
    @Mock
    AnomalyEventsProcess anomalyEventsProcess;

    GetViolatedData getViolatedData;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        Mockito.reset(cacheWrapperMock);
        getViolatedData = Mockito.spy(new GetViolatedData());
        getViolatedData.redisUtilities = redisUtilitiesMock;
        getViolatedData.cacheWrapper = cacheWrapperMock;
        getViolatedData.anomalyManagementService = anomalyManagementService;
        getViolatedData.persistenceSuppression = persistenceSuppression;
        getViolatedData.anomalyEventsProcess = anomalyEventsProcess;

        // Manually set @Value fields
        getViolatedData.highSeverityIdSignal = "433";
        getViolatedData.mediumSeverityIdSignal = "432";
        getViolatedData.lowSeverityIdSignal = "431";
        getViolatedData.entityTypeInstance = "INSTANCE";
        getViolatedData.entityTypeTransaction = "TRANSACTION";
        getViolatedData.entityTypeService = "SERVICE";
    }

    public CompInstKpiEntity getInstanceDetails(int instanceId, String attributeName, int kpiId, Map<String, Double> thresholdMap,
                                                String operation, int severity, int generateAnomaly, int excludeMaintenance) {
        List<KpiViolationConfig> kpiViolationConfigList = new ArrayList<>();
        kpiViolationConfigList.add(KpiViolationConfig.builder()
                .operation(operation)
                .minThreshold(thresholdMap.get("Lower"))
                .maxThreshold(thresholdMap.get("Upper"))
                .severity(severity)
                .generateAnomaly(generateAnomaly)
                .excludeMaintenance(excludeMaintenance)
                .kpiId(kpiId)
                .compInstanceId(instanceId)
                .build());
        Map<String, List<KpiViolationConfig>> kpiViolationConfigMap = new HashMap<>();
        kpiViolationConfigMap.put(attributeName, kpiViolationConfigList);

        return CompInstKpiEntity.builder()
                .kpiViolationConfig(kpiViolationConfigMap)
                .build();
    }

    public CompInstClusterDetails getCompInstClusterDetails(int instanceId) {
        return CompInstClusterDetails.builder()
                .id(instanceId)
                .build();
    }

    private KpiDetails getServiceKpiDetails(int compInstanceId, int kpiId, Map<String, Double> thresholdMap,
                                            String operation, int severity, int generateAnomaly, int excludeMaintenance, String attribute) {
        List<KpiViolationConfig> kpiViolationConfigList = new ArrayList<>();
        kpiViolationConfigList.add(KpiViolationConfig.builder()
                .operation(operation)
                .minThreshold(thresholdMap.get("Lower"))
                .maxThreshold(thresholdMap.get("Upper"))
                .severity(severity)
                .generateAnomaly(generateAnomaly)
                .excludeMaintenance(excludeMaintenance)
                .kpiId(kpiId)
                .applicableTo("clusters")
                .definedBy("USER")
                .compInstanceId(compInstanceId)
                .attributeValue(attribute)
                .build());
        Map<String, List<KpiViolationConfig>> kpiViolationConfigMap = new HashMap<>();
        kpiViolationConfigMap.put("ALL", kpiViolationConfigList);

        return KpiDetails.builder()
                .kpiViolationConfig(kpiViolationConfigMap)
                .build();

    }

//    @Test
//    void validateGenerateAnomalyMethodStaticThresholdType_genAnomalyFlagTrue() {
//
//        String accountIdentifier = "heal_health";
//        String kpiId = "16";
//        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
//        int instanceId = 9;
//        String attributeName = "ALL";
//        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
//        String operation = "greater than";
//        String severity = "Severe";
//
//        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
//                .createViolatedEventKpiInfoType(accountIdentifier,
//                        "Heal_Health_App", "service123", instanceIdentifier,
//                        kpiId, attributeName,
//                        "60.0", 19800, "Static",
//                        operation, severity, thresholdMap);
//
//
//        CompInstKpiEntity instanceKpiDetails = getInstanceDetails(instanceId, attributeName, Integer.parseInt(kpiId),
//                thresholdMap, operation, 1, 1, 0);
//
//        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));
//
//    }
//
//    @Test
//    void validateGenerateAnomalyMethodStaticThresholdType_genAnomalyFlagFalse() {
//
//        String accountIdentifier = "heal_health";
//        String kpiId = "16";
//        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
//        int instanceId = 9;
//        String attributeName = "ALL";
//        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
//        String operation = "greater than";
//        String severity = "Severe";
//
//        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
//                .createViolatedEventKpiInfoType(accountIdentifier,
//                        "Heal_Health_App", "service123", instanceIdentifier,
//                        kpiId, attributeName,
//                        "60.0", 19800, "Static",
//                        operation, severity, thresholdMap);
//
//
//        CompInstKpiEntity instanceKpiDetails = getInstanceDetails(instanceId, attributeName, Integer.parseInt(kpiId),
//                thresholdMap, operation, 1, 0, 0);
//
//        assert !getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));
//
//    }
//
//    @Test
//    void validateGenerateAnomalyMethodRealtimeThresholdType_genAnomalyFlagTrue() {
//
//        String accountIdentifier = "heal_health";
//        String kpiId = "16";
//        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
//        int instanceId = 9;
//        String attributeName = "ALL";
//        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
//        String operation = "greater than";
//        String severity = "Severe";
//
//        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
//                .createViolatedEventKpiInfoType(accountIdentifier,
//                        "Heal_Health_App", "service123", instanceIdentifier,
//                        kpiId, attributeName,
//                        "60.0", 19800, "RealTime",
//                        operation, severity, thresholdMap);
//
//
//        CompInstKpiEntity instanceKpiDetails = getInstanceDetails(instanceId, attributeName, Integer.parseInt(kpiId),
//                thresholdMap, operation, 1, 1, 0);
//
//        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));
//
//    }
//
//    @Test
//    void validateGenerateAnomalyMethodRealtimeThresholdType_genAnomalyFlagFalse() {
//
//        String accountIdentifier = "heal_health";
//        String kpiId = "16";
//        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
//        int instanceId = 9;
//        String attributeName = "ALL";
//        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
//        String operation = "greater than";
//        String severity = "Severe";
//
//        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
//                .createViolatedEventKpiInfoType(accountIdentifier,
//                        "Heal_Health_App", "service123", instanceIdentifier,
//                        kpiId, attributeName,
//                        "60.0", 19800, "RealTime",
//                        operation, severity, thresholdMap);
//
//
//        CompInstKpiEntity instanceKpiDetails = getInstanceDetails(instanceId, attributeName, Integer.parseInt(kpiId),
//                thresholdMap, operation, 1, 0, 0);
//
//        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));
//
//    }
//
//    @Test
//    void validateGenerateAnomalyMethodStaticThresholdType_instanceViolationConfigNull_serviceViolationNotNull_genAnomalyTrue() {
//
//        getViolatedData.redisUtilities = redisUtilitiesMock;
//
//        String accountIdentifier = "heal_health";
//        String kpiId = "16";
//        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
//        int instanceId = 9;
//        String attributeName = "ALL";
//        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
//        String operation = "greater than";
//        String severity = "Severe";
//        String serviceIdentifier = "service123";
//
//        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
//                .createViolatedEventKpiInfoType(accountIdentifier,
//                        "Heal_Health_App", serviceIdentifier, instanceIdentifier,
//                        kpiId, attributeName,
//                        "60.0", 19800, "Static",
//                        operation, severity, thresholdMap);
//
//
//        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();
//
//        getViolatedData.redisUtilities = redisUtilitiesMock;
//        Mockito.when(getViolatedData.redisUtilities.getServiceKPIDetails(accountIdentifier, serviceIdentifier, Integer.parseInt(kpiId)))
//                .thenReturn(getServiceKpiDetails(instanceId, Integer.parseInt(kpiId),
//                        thresholdMap, operation, 1, 1, 0, "ALL"));
//
//        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));
//
//    }
//
//    @Test
//    void validateGenerateAnomalyMethodStaticThresholdType_instanceViolationConfigNull_serviceViolationNotNull_genAnomalyFalse() {
//
//        getViolatedData.redisUtilities = redisUtilitiesMock;
//
//        String accountIdentifier = "heal_health";
//        String kpiId = "16";
//        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
//        int instanceId = 9;
//        String attributeName = "ALL";
//        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
//        String operation = "greater than";
//        String severity = "Severe";
//        String serviceIdentifier = "service123";
//
//        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
//                .createViolatedEventKpiInfoType(accountIdentifier,
//                        "Heal_Health_App", serviceIdentifier, instanceIdentifier,
//                        kpiId, attributeName,
//                        "60.0", 19800, "Static",
//                        operation, severity, thresholdMap);
//
//
//        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();
//
//        getViolatedData.redisUtilities = redisUtilitiesMock;
//        Mockito.when(getViolatedData.redisUtilities.getServiceKPIDetails(accountIdentifier, serviceIdentifier, Integer.parseInt(kpiId)))
//                .thenReturn(getServiceKpiDetails(instanceId, Integer.parseInt(kpiId),
//                        thresholdMap, operation, 1, 0, 0, "ALL"));
//
//        assert !getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));
//
//    }
//
//    @Test
//    void validateGenerateAnomalyMethodStaticThresholdType_instanceViolationConfigNull_serviceViolationNotNull_DifferentAttribute_genAnomalyFalse() {
//
//        getViolatedData.redisUtilities = redisUtilitiesMock;
//
//        String accountIdentifier = "heal_health";
//        String kpiId = "16";
//        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
//        int instanceId = 9;
//        String attributeName = "ALL";
//        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
//        String operation = "greater than";
//        String severity = "Severe";
//        String serviceIdentifier = "service123";
//
//        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
//                .createViolatedEventKpiInfoType(accountIdentifier,
//                        "Heal_Health_App", serviceIdentifier, instanceIdentifier,
//                        kpiId, attributeName,
//                        "60.0", 19800, "Static",
//                        operation, severity, thresholdMap);
//
//
//        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();
//
//        getViolatedData.redisUtilities = redisUtilitiesMock;
//        Mockito.when(getViolatedData.redisUtilities.getServiceKPIDetails(accountIdentifier, serviceIdentifier, Integer.parseInt(kpiId)))
//                .thenReturn(getServiceKpiDetails(instanceId, Integer.parseInt(kpiId),
//                        thresholdMap, operation, 1, 1, 0, "DUMMY"));
//
//        assert !getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));
//
//    }
//
//    @Test
//    void validateGenerateAnomalyMethodRealTimeThresholdType_instanceViolationConfigNull_serviceViolationNotNull_genAnomalyTrue() {
//
//        getViolatedData.redisUtilities = redisUtilitiesMock;
//
//        String accountIdentifier = "heal_health";
//        String kpiId = "16";
//        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
//        int instanceId = 9;
//        String attributeName = "ALL";
//        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
//        String operation = "greater than";
//        String severity = "Severe";
//        String serviceIdentifier = "service123";
//
//        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
//                .createViolatedEventKpiInfoType(accountIdentifier,
//                        "Heal_Health_App", serviceIdentifier, instanceIdentifier,
//                        kpiId, attributeName,
//                        "60.0", 19800, "RealTime",
//                        operation, severity, thresholdMap);
//
//
//        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();
//
//        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));
//
//    }
//
//    @Test
//    void validateGenerateAnomalyMethodRealTimeThresholdType_instanceViolationConfigNull_serviceViolationNotNull_genAnomalyFalse() {
//
//        getViolatedData.redisUtilities = redisUtilitiesMock;
//
//        String accountIdentifier = "heal_health";
//        String kpiId = "16";
//        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
//        int instanceId = 9;
//        String attributeName = "ALL";
//        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
//        String operation = "greater than";
//        String severity = "Severe";
//        String serviceIdentifier = "service123";
//
//        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
//                .createViolatedEventKpiInfoType(accountIdentifier,
//                        "Heal_Health_App", serviceIdentifier, instanceIdentifier,
//                        kpiId, attributeName,
//                        "60.0", 19800, "RealTime",
//                        operation, severity, thresholdMap);
//
//
//        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();
//
//        getViolatedData.redisUtilities = redisUtilitiesMock;
//        Mockito.when(getViolatedData.redisUtilities.getServiceKPIDetails(accountIdentifier, serviceIdentifier, Integer.parseInt(kpiId)))
//                .thenReturn(getServiceKpiDetails(instanceId, Integer.parseInt(kpiId),
//                        thresholdMap, operation, 1, 0, 0, "ALL"));
//
//        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));
//
//    }
//
//    @Test
//    void validateGenerateAnomalyMethodStaticThresholdType_instanceViolationConfigNull_serviceViolationConfigMocked() {
//
//        getViolatedData.redisUtilities = redisUtilitiesMock;
//
//        String accountIdentifier = "heal_health";
//        String kpiId = "16";
//        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
//        int instanceId = 9;
//        String attributeName = "ALL";
//        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
//        String operation = "greater than";
//        String severity = "Severe";
//
//        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
//                .createViolatedEventKpiInfoType(accountIdentifier,
//                        "Heal_Health_App", "service123", instanceIdentifier,
//                        kpiId, attributeName,
//                        "60.0", 19800, "Static",
//                        operation, severity, thresholdMap);
//
//
//        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();
//
//        assert !getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));
//
//    }
//
//    @Test
//    void validateGenerateAnomalyMethodRealtimeThresholdType_instanceViolationConfigNull_serviceViolationConfigMocked() {
//
//        getViolatedData.redisUtilities = redisUtilitiesMock;
//
//        String accountIdentifier = "heal_health";
//        String kpiId = "16";
//        String instanceIdentifier = "29a771b7-3c01-4cca-a903-d7eb78aba8f5";
//        int instanceId = 9;
//        String attributeName = "ALL";
//        Map<String, Double> thresholdMap = protoCreator.thresholdMapProvider();
//        String operation = "greater than";
//        String severity = "Severe";
//
//        ViolatedEventProtos.ViolatedEvent violatedEvent = protoCreator
//                .createViolatedEventKpiInfoType(accountIdentifier,
//                        "Heal_Health_App", "service123", instanceIdentifier,
//                        kpiId, attributeName,
//                        "60.0", 19800, "RealTime",
//                        operation, severity, thresholdMap);
//
//
//        CompInstKpiEntity instanceKpiDetails = CompInstKpiEntity.builder().kpiViolationConfig(null).build();
//
//        assert getViolatedData.getGenerateAnomalyStatus(violatedEvent, instanceKpiDetails, violatedEvent.getKpis(0));
//
//    }

    @Test
    void testHandleViolationDetails_existingDetailsMatchingConfig_updatesCount() {
        ViolationDetails details = ViolationDetails.builder().build();
        ViolationStatus violationStatus = ViolationStatus.builder()
                .violationCount(9)
                .persistence(5)
                .suppression(3)
                .thresholds(Map.of("Lower", 70.0, "Upper", 0.0))
                .operationName("greater than")
                .build();
        Map<String, ViolationStatus> statusMap = new HashMap<>();
        statusMap.put("431", violationStatus);
        details.setViolationStatusMap(statusMap);
        details.setAttributeName("ALL");
        // Ensure the config matches the ViolationStatus
        KpiViolationConfig config = KpiViolationConfig.builder().thresholdSeverityId(431).persistence(5).suppression(3).operation("greater than").build();
        Map<String, Double> thresholds = Map.of("Lower", 70.0, "Upper", 0.0);
        ViolationDetails result = getViolatedData.handleViolationDetails("acc", "inst", "1", List.of("svc"), config, thresholds, "svc", "INSTANCE", OperationType.GREATER_THAN, "ALL", "ALL", 123L, "SOR", List.of("appId"), "INSTANCE", details);
        assertEquals(10, result.getViolationStatusMap().get("431").getViolationCount());
    }

    @Test
    void testHandleViolationDetails_existingDetailsConfigMismatch_closesAnomalyAndResets() {
        ViolationDetails details = mock(ViolationDetails.class);
        Map<String, ViolationStatus> statusMap = new HashMap<>();
        statusMap.put("431", ViolationStatus.builder().build());
        when(details.getViolationStatusMap()).thenReturn(statusMap);
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(details);
        when(details.checkPersistenceSuppressionMatchForSeverity(any(), anyInt(), anyInt())).thenReturn(false);
        when(details.getAnomalyEventStatus()).thenReturn(mock(AnomalyEventStatus.class));
        when(details.getAnomalyEventStatus().getAnomalyEventId()).thenReturn("anomalyId");
        AnomalyAccountPojo anomalyAccountPojo = mock(AnomalyAccountPojo.class);
        when(anomalyManagementService.closeAnomaly(any())).thenReturn(anomalyAccountPojo);
        doNothing().when(redisUtilitiesMock).deleteViolationDetails(any(), any(), any(), any(), any(), any());
        doNothing().when(details).resetViolationCounts();
        KpiViolationConfig config = KpiViolationConfig.builder().thresholdSeverityId(431).persistence(2).suppression(2).operation("greater than").build();
        Map<String, Double> thresholds = Map.of("Lower", 10.0, "Upper", 100.0);
        getViolatedData.handleViolationDetails("acc", "inst", "1", List.of("svc"), config, thresholds, "svc", "INSTANCE", OperationType.GREATER_THAN, "ALL", "ALL", 123L, "SOR", List.of("appId"), "INSTANCE", details);
        verify(redisUtilitiesMock, atLeastOnce()).deleteViolationDetails(any(), any(), any(), any(), any(), any());
        verify(anomalyManagementService, atLeastOnce()).closeAnomaly(any());
    }

    @Test
    void testHandleViolationDetails_noExistingDetails_createsAndUpdates() {
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(null);
        doNothing().when(redisUtilitiesMock).putViolationDetails(any(), any(), any(), any(), any(), any(), any());
        KpiViolationConfig config = KpiViolationConfig.builder().thresholdSeverityId(431).persistence(2).suppression(2).operation("greater than").build();
        Map<String, Double> thresholds = Map.of("Lower", 10.0, "Upper", 100.0);
        ViolationDetails result = getViolatedData.handleViolationDetails("acc", "inst", "1", List.of("svc"), config, thresholds, "svc", "INSTANCE", OperationType.GREATER_THAN, "ALL", "ALL", 123L, "SOR", List.of("appId"), "INSTANCE", null);
        assertEquals(1, result.getViolationStatusMap().get("431").getViolationCount());
    }

    @Test
    void testHandleViolationDetails_noExistingDetails_noInstancePersistenceSuppression_createsAndUpdates() {
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(null);
        Map<String, Integer> persSupp = Map.of("persistence", 3, "suppression", 4, "collectionInterval", 60);
        when(persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(any(), any(), any(), any(), any(), any())).thenReturn(persSupp);
        doNothing().when(redisUtilitiesMock).putViolationDetails(any(), any(), any(), any(), any(), any(), any());
        KpiViolationConfig config = KpiViolationConfig.builder().thresholdSeverityId(431).persistence(0).suppression(0).operation("greater than").build();
        Map<String, Double> thresholds = Map.of("Lower", 10.0, "Upper", 100.0);
        ViolationDetails result = getViolatedData.handleViolationDetails("acc", "inst", "1", List.of("svc"), config, thresholds, "svc", "INSTANCE", OperationType.GREATER_THAN, "ALL", "ALL", 123L, "SOR", List.of("appId"), "INSTANCE", null);
        assertEquals(1, result.getViolationStatusMap().get("431").getViolationCount());
    }

    @Test
    void testHandleViolationDetails_opTypeNull_resetsCount() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        ViolationDetails violationDetailsMock = mock(ViolationDetails.class);
        ViolationStatus violationStatus = ViolationStatus.builder()
                .violationCount(9)
                .persistence(2)
                .suppression(2)
                .thresholds(Map.of("Lower", 70.0, "Upper", 0.0))
                .operationName("greater than")
                .build();
        Map<String, ViolationStatus> statusMap = new HashMap<>();
        statusMap.put("431", violationStatus);
        when(violationDetailsMock.getViolationStatusMap()).thenReturn(statusMap);
        when(violationDetailsMock.getAnomalyEventStatus()).thenReturn(mock(AnomalyEventStatus.class));
        when(violationDetailsMock.getAnomalyEventStatus().getAnomalyEventId()).thenReturn("anomalyId");

        ViolationDetails violationDetails = ViolationDetails.builder().build();
        violationDetails.setViolationStatusMap(statusMap);

        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(violationDetails);
        doNothing().when(redisUtilitiesMock).putViolationDetails(any(), any(), any(), any(), any(), any(), any());
        KpiViolationConfig config = KpiViolationConfig.builder().thresholdSeverityId(431).persistence(2).suppression(2).operation("greater than").build();
        Map<String, Double> thresholds = Map.of("Lower", 70.0, "Upper", 0.0);
        when(violationDetailsMock.checkThresholdsOperationNameMatchForSeverity(any(), any(), any())).thenReturn(true);
        AnomalyAccountPojo anomalyAccountPojo = mock(AnomalyAccountPojo.class);
        when(anomalyManagementService.closeAnomaly(any())).thenReturn(anomalyAccountPojo);
        ViolationDetails result = getViolatedData.handleViolationDetails("acc", "inst", "1", List.of("svc"), config, thresholds, "svc", "INSTANCE", null, "ALL", "ALL", 123L, "SOR", List.of("appId"), "INSTANCE", violationDetails);
        assertEquals(0, result.getViolationStatusMap().get("431").getViolationCount());
    }

    @Test
    void testIsPersistenceSuppressionValid_KpiLevelConfig() throws Exception {
        ViolationDetails details = mock(ViolationDetails.class);
        when(details.checkPersistenceSuppressionMatchForSeverity(eq("431"), eq(2), eq(3))).thenReturn(true);

        KpiViolationConfig config = KpiViolationConfig.builder().thresholdSeverityId(431).persistence(2).suppression(3).build();

        Method method = GetViolatedData.class.getDeclaredMethod(
                "isPersistenceSuppressionValid",
                ViolationDetails.class, KpiViolationConfig.class, String.class, String.class, String.class, String.class, List.class, String.class, String.class
        );
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) method.invoke(getViolatedData, details, config, "INSTANCE", "acc", "inst", "1", List.of("svc"), "svc", "431");
        assertTrue((Boolean) result.get("isValid"));
        assertEquals(2, result.get("persistence"));
        assertEquals(3, result.get("suppression"));
        assertEquals("INSTANCE", result.get("persSuppLevel"));
    }

    @Test
    void testIsPersistenceSuppressionValid_ServiceConfigNull() throws Exception {
        ViolationDetails details = mock(ViolationDetails.class);
        when(details.getHighestViolatedSeverity()).thenReturn("431");
        KpiViolationConfig config = KpiViolationConfig.builder().thresholdSeverityId(431).persistence(0).suppression(0).build();

        when(persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(any(), any(), any(), any(), any(), any())).thenReturn(null);

        Method method = GetViolatedData.class.getDeclaredMethod(
                "isPersistenceSuppressionValid",
                ViolationDetails.class, KpiViolationConfig.class, String.class, String.class, String.class, String.class, List.class, String.class, String.class
        );
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) method.invoke(getViolatedData, details, config, "INSTANCE", "acc", "inst", "1", List.of("svc"), "svc", "431");
        assertFalse((Boolean) result.get("isValid"));
        assertEquals(0, result.get("persistence"));
        assertEquals(0, result.get("suppression"));
        assertEquals("SERVICE", result.get("persSuppLevel"));
    }

    @Test
    void testIsPersistenceSuppressionValid_ServiceConfigMissingKeys() throws Exception {
        ViolationDetails details = mock(ViolationDetails.class);
        when(details.getHighestViolatedSeverity()).thenReturn("431");
        KpiViolationConfig config = KpiViolationConfig.builder().thresholdSeverityId(431).persistence(0).suppression(0).build();

        Map<String, Integer> persSupp = new HashMap<>(); // missing keys
        when(persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(any(), any(), any(), any(), any(), any())).thenReturn(persSupp);

        Method method = GetViolatedData.class.getDeclaredMethod(
                "isPersistenceSuppressionValid",
                ViolationDetails.class, KpiViolationConfig.class, String.class, String.class, String.class, String.class, List.class, String.class, String.class
        );
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) method.invoke(getViolatedData, details, config, "INSTANCE", "acc", "inst", "1", List.of("svc"), "svc", "431");
        assertFalse((Boolean) result.get("isValid"));
        assertEquals(0, result.get("persistence"));
        assertEquals(0, result.get("suppression"));
        assertEquals("SERVICE", result.get("persSuppLevel"));
    }

    @Test
    void testIsPersistenceSuppressionValid_ServiceConfigValid() throws Exception {
        ViolationDetails details = mock(ViolationDetails.class);
        when(details.getHighestViolatedSeverity()).thenReturn("431");
        when(details.checkPersistenceSuppressionMatchForSeverity(eq("431"), eq(2), eq(3))).thenReturn(true);

        KpiViolationConfig config = KpiViolationConfig.builder().thresholdSeverityId(431).persistence(0).suppression(0).build();

        Map<String, Integer> persSupp = new HashMap<>();
        persSupp.put("persistence", 2);
        persSupp.put("suppression", 3);
        when(persistenceSuppression.getPersSuppAndCollectionIntervalAtServiceConf(any(), any(), any(), any(), any(), any())).thenReturn(persSupp);

        Method method = GetViolatedData.class.getDeclaredMethod(
                "isPersistenceSuppressionValid",
                ViolationDetails.class, KpiViolationConfig.class, String.class, String.class, String.class, String.class, List.class, String.class, String.class
        );
        method.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) method.invoke(getViolatedData, details, config, "INSTANCE", "acc", "inst", "1", List.of("svc"), "svc", "431");
        assertTrue((Boolean) result.get("isValid"));
        assertEquals(2, result.get("persistence"));
        assertEquals(3, result.get("suppression"));
        assertEquals("SERVICE", result.get("persSuppLevel"));
    }

    @Test
    void testGetViolatedDataForHighestSeverity_withViolations_statusContinue_filtersHighest() {
        ViolationDetails violationDetails = ViolationDetails.builder().build();
        ViolationStatus violationStatus = ViolationStatus.builder()
                .violationCount(2)
                .persistence(1)
                .suppression(2)
                .thresholds(Map.of("Lower", 90.0, "Upper", 0.0))
                .operationName("greater than")
                .build();
        Map<String, ViolationStatus> statusMap = new HashMap<>();
        statusMap.put("433", violationStatus);
        violationDetails.setViolationStatusMap(statusMap);
        doNothing().when(redisUtilitiesMock).putViolationDetails(any(), any(), any(), any(), any(), any(), any());
        List<ViolatedData> vdList = new ArrayList<>();
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setThresholdSeverity("433");
        vdList.add(vd);
        when(persistenceSuppression.persistenceSuppressionMetForSeverity(any(), any())).thenReturn(PersistenceSuppressionStatus.CONTINUE);
        ViolatedData result = getViolatedData.getViolatedDataForHighestSeverity("acc", "inst", "1", List.of("svc"), vdList, "ALL", "svc", 123L, "SOR", List.of("appId"), "INSTANCE", violationDetails);
        assertNotNull(result);
        // Assert violationDetails is set
        assertNotNull(result.getViolationDetails());
    }

    @Test
    void testGetViolatedDataForHighestSeverity_withViolations_statusPersistence_filtersHighest() {
//        ViolationDetails details = mock(ViolationDetails.class);
        ViolationDetails violationDetails = ViolationDetails.builder().build();
        ViolationStatus violationStatus = ViolationStatus.builder()
                .violationCount(2)
                .persistence(1)
                .suppression(2)
                .thresholds(Map.of("Lower", 90.0, "Upper", 0.0))
                .operationName("greater than")
                .build();
        Map<String, ViolationStatus> statusMap = new HashMap<>();
        statusMap.put("433", violationStatus);
        violationDetails.setViolationStatusMap(statusMap);
//        when(violationDetails.getViolationStatusMap()).thenReturn(statusMap);
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(violationDetails);
        doNothing().when(redisUtilitiesMock).putViolationDetails(any(), any(), any(), any(), any(), any(), any());
        List<ViolatedData> vdList = new ArrayList<>();
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setThresholdSeverity("433");
        vdList.add(vd);
        when(persistenceSuppression.persistenceSuppressionMetForSeverity(any(), any())).thenReturn(PersistenceSuppressionStatus.PERSISTENCE);
        ViolatedData result = getViolatedData.getViolatedDataForHighestSeverity("acc", "inst", "1", List.of("svc"), vdList, "ALL", "svc", 123L, "SOR", List.of("appId"), "INSTANCE", violationDetails);
        assertNull(result);
    }

    @Test
    void testGetViolatedDataForHighestSeverity_withViolations_statusSuppression_filtersHighest() {
        ViolationDetails violationDetails = ViolationDetails.builder().build();
        ViolationStatus violationStatus = ViolationStatus.builder()
                .violationCount(2)
                .persistence(1)
                .suppression(2)
                .thresholds(Map.of("Lower", 90.0, "Upper", 0.0))
                .operationName("greater than")
                .build();
        Map<String, ViolationStatus> statusMap = new HashMap<>();
        statusMap.put("433", violationStatus);
        violationDetails.setViolationStatusMap(statusMap);
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(violationDetails);
        doNothing().when(redisUtilitiesMock).putViolationDetails(any(), any(), any(), any(), any(), any(), any());
        List<ViolatedData> vdList = new ArrayList<>();
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setThresholdSeverity("433");
        vdList.add(vd);
        when(persistenceSuppression.persistenceSuppressionMetForSeverity(any(), any())).thenReturn(PersistenceSuppressionStatus.SUPPRESSION);
        ViolatedData result = getViolatedData.getViolatedDataForHighestSeverity("acc", "inst", "1", List.of("svc"), vdList, "ALL", "svc", 123L, "SOR", List.of("appId"), "INSTANCE", null);
        assertNull(result);
    }

    @Test
    void testGetViolatedDataForHighestSeverity_noViolations_closingWindowNotMet() {
        String accountId = "acc";
        String serviceId = "svc";
        ViolationDetails details = ViolationDetails.builder().build();
        details.setAnomalyEventStatus(AnomalyEventStatus.builder().anomalyEventId("anomalyId").build());
        details.setClosingWindowCount(1);
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(details);
        doNothing().when(redisUtilitiesMock).putViolationDetails(any(), any(), any(), any(), any(), any(), any());

        ServiceConfiguration serviceConfiguration = mock(ServiceConfiguration.class);
        when(persistenceSuppression.getServiceConfiguration(accountId, serviceId)).thenReturn(serviceConfiguration);
        when(serviceConfiguration.getClosingWindow()).thenReturn(5);

        List<ViolatedData> vdList = new ArrayList<>();
        ViolatedData result = getViolatedData.getViolatedDataForHighestSeverity(accountId, "inst", "1", List.of("svc"), vdList, "ALL", serviceId, 123L, "SOR", List.of("appId"), "INSTANCE", details);
        assertNull(result);
    }

    @Test
    void testGetViolatedDataForHighestSeverity_noViolations_closingWindowMet() {
        String accountId = "acc";
        String serviceId = "svc";
        ViolationDetails details = ViolationDetails.builder().build();
        details.setAnomalyEventStatus(AnomalyEventStatus.builder().anomalyEventId("anomalyId").build());
        details.setClosingWindowCount(4);
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(details);
        doNothing().when(redisUtilitiesMock).deleteViolationDetails(any(), any(), any(), any(), any(), any());

        ServiceConfiguration serviceConfiguration = mock(ServiceConfiguration.class);
        when(persistenceSuppression.getServiceConfiguration(accountId, serviceId)).thenReturn(serviceConfiguration);
        when(serviceConfiguration.getClosingWindow()).thenReturn(5);

        AnomalyAccountPojo anomalyAccountPojo = mock(AnomalyAccountPojo.class);
        when(anomalyManagementService.closeAnomaly(any())).thenReturn(anomalyAccountPojo);

        List<ViolatedData> vdList = new ArrayList<>();
        ViolatedData result = getViolatedData.getViolatedDataForHighestSeverity(accountId, "inst", "1", List.of("svc"), vdList, "ALL", serviceId, 123L, "SOR", List.of("appId"), "INSTANCE", details);
        assertNull(result);
        verify(redisUtilitiesMock, atLeastOnce()).deleteViolationDetails(any(), any(), any(), any(), any(), any());
        verify(anomalyManagementService, atLeastOnce()).closeAnomaly(any());
    }

    @Test
    void testGetViolatedDataForHighestSeverity_noViolations_serviceConfigurationNull() {
        String accountId = "acc";
        String serviceId = "svc";
        ViolationDetails details = ViolationDetails.builder().build();
        details.setAnomalyEventStatus(AnomalyEventStatus.builder().anomalyEventId("anomalyId").build());
        details.setClosingWindowCount(1);
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(details);
        doNothing().when(redisUtilitiesMock).putViolationDetails(any(), any(), any(), any(), any(), any(), any());
        doNothing().when(redisUtilitiesMock).putViolationDetails(any(), any(), any(), any(), any(), any(), any());

        when(persistenceSuppression.getServiceConfiguration(accountId, serviceId)).thenReturn(null);

        List<ViolatedData> vdList = new ArrayList<>();
        ViolatedData result = getViolatedData.getViolatedDataForHighestSeverity(accountId, "inst", "1", List.of("svc"), vdList, "ALL", serviceId, 123L, "SOR", List.of("appId"), "INSTANCE", details);
        assertNull(result);
    }

    @Test
    void testGetViolatedDataForHighestSeverity_noViolations_noAnomaly() {
        String accountId = "acc";
        String serviceId = "svc";
        ViolationDetails details = ViolationDetails.builder().build();
        details.setAnomalyEventStatus(null);
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(details);
        doNothing().when(redisUtilitiesMock).deleteViolationDetails(any(), any(), any(), any(), any(), any());

        when(persistenceSuppression.getServiceConfiguration(accountId, serviceId)).thenReturn(null);

        List<ViolatedData> vdList = new ArrayList<>();
        ViolatedData result = getViolatedData.getViolatedDataForHighestSeverity(accountId, "inst", "1", List.of("svc"), vdList, "ALL", serviceId, 123L, "SOR", List.of("appId"), "INSTANCE", details);
        assertNull(result);
        verify(redisUtilitiesMock, atLeastOnce()).deleteViolationDetails(any(), any(), any(), any(), any(), any());
    }

//    @Test
//    void testGetViolatedDataForHighestSeverity_NoViolations_ClosesAnomaly() {
//        // Arrange
//        String accountId = "acc";
//        String instanceId = "inst";
//        String kpiId = "kpi";
//        List<String> serviceIds = Collections.singletonList("svc");
//        List<ViolatedData> violatedDataList = new ArrayList<>();
//        String kpiAttributeName = "ALL";
//        String serviceIdentifier = null;
//        long eventTime = System.currentTimeMillis() / 1000;
//        String violationFor = "SOR";
//        String entityType = "INSTANCE";
//
//        ViolationDetails violationDetails = ViolationDetails.builder().build();
//        violationDetails.setAnomalyEventStatus(AnomalyEventStatus.builder().anomalyEventId("anomalyId").build());
//        violationDetails.setClosingWindowCount(1);
//        ServiceConfiguration serviceConfig = mock(ServiceConfiguration.class);
//
//        // Configure mocks
//        Mockito.when(redisUtilitiesMock.getViolationDetails(accountId, instanceId, kpiId, kpiAttributeName, violationFor, "INSTANCE"))
//                .thenReturn(violationDetails);
//        Mockito.when(persistenceSuppression.getServiceConfiguration(accountId, serviceIds.get(0)))
//                .thenReturn(serviceConfig);
//        Mockito.when(serviceConfig.getClosingWindow()).thenReturn(2); // Match the closing window count
//
//        AnomalyAccountPojo anomalyAccountPojo = mock(AnomalyAccountPojo.class);
//        Mockito.when(anomalyManagementService.closeAnomaly(any())).thenReturn(anomalyAccountPojo);
//        Mockito.when(anomalyEventsProcess.insertAnomaliesAndAlertsOpenSearchRMQ(Mockito.anyList(), Mockito.eq(false)))
//                .thenReturn(null);
//
//        // Act
//        ViolatedData result = getViolatedData.getViolatedDataForHighestSeverity(
//                accountId, instanceId, kpiId, serviceIds, violatedDataList,
//                kpiAttributeName, serviceIdentifier, eventTime, violationFor, List.of("appId"), entityType, violationDetails);
//
//        // Assert
//        assertNull(result);
//
//        // Verify anomaly was closed
//        Mockito.verify(anomalyManagementService, Mockito.times(1)).closeAnomaly(Mockito.any());
//        // Verify downstream event publishing
//        Mockito.verify(anomalyEventsProcess, Mockito.times(1)).insertAnomaliesAndAlertsOpenSearchRMQ(Mockito.anyList(), Mockito.eq(false));
//        // Verify violation details were deleted
//        Mockito.verify(redisUtilitiesMock, Mockito.times(1))
//                .deleteViolationDetails(accountId, instanceId, kpiId, kpiAttributeName, violationFor, "INSTANCE");
//    }

    @Test
    void testCheckViolationsGetHighestSeverity_KpiTypeCore_validConfig() {
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setEventType(ViolationEventType.KPI_VIOLATION);
        vd.setMetaData(Map.of("violationLevel", "INSTANCE"));
        vd.setKpiId("1");
        vd.setInstanceId("inst");
        vd.setKpiAttribute("ALL");
        vd.setValue("50");
        vd.setServiceList(List.of("svc"));
        vd.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        vd.setThresholdSeverity("431"); // Ensure this matches the highestViolatedSeverity
        CompInstKpiEntity kpiEntity = mock(CompInstKpiEntity.class);
        KpiViolationConfig config = KpiViolationConfig.builder().status(1).minThreshold(10.0).maxThreshold(100.0).operation("greater than").thresholdSeverityId(431).build();
        when(kpiEntity.getKpiViolationConfig()).thenReturn(Map.of("ALL", List.of(config)));
        when(cacheWrapperMock.getInstanceKPIDetails(any(), any(), anyInt())).thenReturn(kpiEntity);
        getViolatedData.cacheWrapper = cacheWrapperMock;
        // Add ViolationDetails mock for redisUtilities
        ViolationDetails violationDetails = mock(ViolationDetails.class);
        when(violationDetails.getViolationStatusMap()).thenReturn(new HashMap<>(Map.of("431", mock(ViolationStatus.class))));
        when(violationDetails.getHighestViolatedSeverity()).thenReturn("431");
        when(violationDetails.getAnomalyEventStatus()).thenReturn(null);
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(violationDetails);
        ViolatedData result = getViolatedData.checkViolationsGetHighestSeverity(vd, violationDetails, "INSTANCE");
        assertNotNull(result);
        // Assert violationDetails is set
        assertNotNull(result.getViolationDetails());
    }

    @Test
    void testCheckViolationsGetHighestSeverity_KpiTypeAvailability_validConfig() {
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setEventType(ViolationEventType.KPI_VIOLATION);
        vd.setMetaData(Map.of("violationLevel", "INSTANCE"));
        vd.setKpiId("1");
        vd.setInstanceId("inst");
        vd.setKpiAttribute("ALL");
        vd.setValue("50");
        vd.setServiceList(List.of("svc"));
        vd.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Availability);
        vd.setThresholdSeverity("431");
        CompInstKpiEntity kpiEntity = mock(CompInstKpiEntity.class);
        KpiViolationConfig config = KpiViolationConfig.builder().status(1).minThreshold(10.0).maxThreshold(100.0).operation("greater than").thresholdSeverityId(431).build();
        when(kpiEntity.getKpiViolationConfig()).thenReturn(Map.of("ALL", List.of(config)));
        when(cacheWrapperMock.getInstanceKPIDetails(any(), any(), anyInt())).thenReturn(kpiEntity);
        getViolatedData.cacheWrapper = cacheWrapperMock;
        // Add ViolationDetails mock for redisUtilities
        ViolationDetails violationDetails = mock(ViolationDetails.class);
        when(violationDetails.getViolationStatusMap()).thenReturn(new HashMap<>(Map.of("431", mock(ViolationStatus.class))));
        when(violationDetails.getHighestViolatedSeverity()).thenReturn("431");
        when(violationDetails.getAnomalyEventStatus()).thenReturn(null);
        when(redisUtilitiesMock.getViolationDetails(any(), any(), any(), any(), any(), any())).thenReturn(violationDetails);
        ViolatedData result = getViolatedData.checkViolationsGetHighestSeverity(vd, violationDetails, "INSTANCE");
        assertNotNull(result);
        // Assert violationDetails is set
        assertNotNull(result.getViolationDetails());
    }

    @Test
    void testCheckViolationsGetHighestSeverity_valueNull() {
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setEventType(ViolationEventType.KPI_VIOLATION);
        vd.setMetaData(Map.of("violationLevel", "INSTANCE"));
        vd.setKpiId("1");
        vd.setInstanceId("inst");
        vd.setKpiAttribute("ALL");
        vd.setServiceList(List.of("svc"));
        vd.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        CompInstKpiEntity kpiEntity = mock(CompInstKpiEntity.class);
        KpiViolationConfig config = KpiViolationConfig.builder().status(1).minThreshold(10.0).maxThreshold(100.0).operation("greater than").thresholdSeverityId(431).build();
        when(kpiEntity.getKpiViolationConfig()).thenReturn(Map.of("ALL", List.of(config)));
        when(cacheWrapperMock.getInstanceKPIDetails(any(), any(), anyInt())).thenReturn(kpiEntity);
        getViolatedData.cacheWrapper = cacheWrapperMock;
        ViolatedData result = getViolatedData.checkViolationsGetHighestSeverity(vd, null, "INSTANCE");
        assertNull(result);
    }

    @Test
    void testCheckViolationsGetHighestSeverity_opTypeNull_notViolated() {
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setEventType(ViolationEventType.KPI_VIOLATION);
        vd.setMetaData(Map.of("violationLevel", "INSTANCE"));
        vd.setKpiId("1");
        vd.setInstanceId("inst");
        vd.setKpiAttribute("ALL");
        vd.setServiceList(List.of("svc"));
        vd.setValue("50");
        vd.setKpiType(KPIAgentMessageProtos.KPIAgentMessage.KpiData.KpiType.Core);
        CompInstKpiEntity kpiEntity = mock(CompInstKpiEntity.class);
        KpiViolationConfig config = KpiViolationConfig.builder().status(1).minThreshold(60.0).maxThreshold(0.0).operation("greater than").thresholdSeverityId(431).build();
        when(kpiEntity.getKpiViolationConfig()).thenReturn(Map.of("ALL", List.of(config)));
        when(cacheWrapperMock.getInstanceKPIDetails(any(), any(), anyInt())).thenReturn(kpiEntity);
        getViolatedData.cacheWrapper = cacheWrapperMock;
        ViolatedData result = getViolatedData.checkViolationsGetHighestSeverity(vd, null, "INSTANCE");
        assertNull(result);
    }

    @Test
    void testCheckViolationsGetHighestSeverity_kpiViolation_noConfig() {
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setEventType(ViolationEventType.KPI_VIOLATION);
        vd.setMetaData(Map.of("violationLevel", "INSTANCE"));
        vd.setKpiId("1");
        vd.setInstanceId("inst");
        vd.setKpiAttribute("ALL");
        vd.setValue("50");
        CompInstKpiEntity kpiEntity = mock(CompInstKpiEntity.class);
        when(kpiEntity.getKpiViolationConfig()).thenReturn(Map.of("ALL", new ArrayList<>()));
        when(cacheWrapperMock.getInstanceKPIDetails(any(), any(), anyInt())).thenReturn(kpiEntity);
        getViolatedData.cacheWrapper = cacheWrapperMock;
        ViolatedData result = getViolatedData.checkViolationsGetHighestSeverity(vd, null, "INSTANCE");
        assertNull(result);
    }

//    @Test
//    void testHandleViolationDetails_NullViolationDetails_CreatesNew() {
//        // Arrange
//        String accountId = "acc";
//        String instanceId = "inst";
//        String kpiId = "kpi";
//        List<String> serviceIds = Collections.singletonList("svc");
//        KpiViolationConfig kv = KpiViolationConfig.builder().thresholdSeverityId(431).persistence(2).suppression(2).build();
//        Map<String, Double> thresholds = new HashMap<>();
//        String serviceIdentifier = null;
//        String violationLevel = "INSTANCE";
//        OperationType opType = OperationType.GREATER_THAN;
//        String kpiAttributeName = "ALL";
//        long eventTime = System.currentTimeMillis() / 1000;
//        String violationFor = "SOR";
//        String entityType = "INSTANCE";
//
//        Mockito.when(redisUtilitiesMock.getViolationDetails(accountId, instanceId, kpiId, kpiAttributeName, violationFor, violationLevel)).thenReturn(null);
//
//        // Act
//        getViolatedData.handleViolationDetails(accountId, instanceId, kpiId, serviceIds, kv, thresholds, serviceIdentifier, violationLevel, opType, kpiAttributeName, eventTime, violationFor, List.of("appId"), entityType, null);
//
//        // Assert
//        Mockito.verify(redisUtilitiesMock).getViolationDetails(accountId, instanceId, kpiId, kpiAttributeName, violationFor, violationLevel);
//    }

    @Test
    void testGetViolationConfigsForViolatedData_instanceLevel_returnsConfig() throws Exception {
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setEventType(ViolationEventType.KPI_VIOLATION);
        vd.setInstanceId("inst");
        vd.setKpiId("1");
        vd.setKpiAttribute(Constants.COMMON_ATTRIBUTE);
        Map<String, String> meta = new HashMap<>();
        meta.put("violationLevel", "INSTANCE");
        vd.setMetaData(meta);
        CompInstKpiEntity kpiEntity = mock(CompInstKpiEntity.class);
        List<KpiViolationConfig> configList = List.of(KpiViolationConfig.builder().status(1).build());
        Map<String, List<KpiViolationConfig>> configMap = Map.of(Constants.COMMON_ATTRIBUTE, configList);
        when(kpiEntity.getKpiViolationConfig()).thenReturn(configMap);
        when(cacheWrapperMock.getInstanceKPIDetails(any(), any(), anyInt())).thenReturn(kpiEntity);
        getViolatedData.cacheWrapper = cacheWrapperMock;
        Method m = GetViolatedData.class.getDeclaredMethod("getViolationConfigsForViolatedData", ViolatedData.class);
        m.setAccessible(true);
        @SuppressWarnings("unchecked")
        Map.Entry<String, List<KpiViolationConfig>> result = (Map.Entry<String, List<KpiViolationConfig>>) m.invoke(getViolatedData, vd);
        assertNotNull(result);
        assertEquals(Constants.COMMON_ATTRIBUTE, result.getKey());
        assertEquals(1, result.getValue().size());
    }

    @Test
    void testGetViolationConfigsForViolatedData_serviceLevel_returnsConfig() throws Exception {
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setEventType(ViolationEventType.KPI_VIOLATION);
        vd.setInstanceId("inst");
        vd.setKpiId("1");
        vd.setKpiAttribute("ALL");
        Map<String, String> meta = new HashMap<>();
        meta.put("violationLevel", "SERVICE");
        meta.put("serviceIdentifier", "svc");
        vd.setMetaData(meta);
        KpiDetails kpiDetails = mock(KpiDetails.class);
        List<KpiViolationConfig> configList = List.of(KpiViolationConfig.builder().status(1).build());
        Map<String, List<KpiViolationConfig>> configMap = Map.of("ALL", configList);
        when(kpiDetails.getKpiViolationConfig()).thenReturn(configMap);
        when(cacheWrapperMock.getServiceKPIDetails(any(), any(), anyInt())).thenReturn(kpiDetails);
        getViolatedData.cacheWrapper = cacheWrapperMock;
        Method m = GetViolatedData.class.getDeclaredMethod("getViolationConfigsForViolatedData", ViolatedData.class);
        m.setAccessible(true);
        @SuppressWarnings("unchecked")
        Map.Entry<String, List<KpiViolationConfig>> result = (Map.Entry<String, List<KpiViolationConfig>>) m.invoke(getViolatedData, vd);
        assertNotNull(result);
        assertEquals("ALL", result.getKey());
        assertEquals(1, result.getValue().size());
    }

    @Test
    void testGetViolationConfigsForViolatedData_txnViolation_returnsConfig() throws Exception {
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setEventType(ViolationEventType.TXN_VIOLATION);
        vd.setKpiId("1");
        vd.setTransactionId("txn");
        ComponentKpiEntity componentKpiEntity = mock(ComponentKpiEntity.class);
        TxnKPIViolationConfig txnConfig = mock(TxnKPIViolationConfig.class);
        KpiViolationConfig kvc = KpiViolationConfig.builder().kpiId(1).status(1).build();
        when(txnConfig.getKpiViolationConfig()).thenReturn(List.of(kvc));
        when(cacheWrapperMock.getComponentKPIDetails(any(), any(), any())).thenReturn(componentKpiEntity);
        when(cacheWrapperMock.getTransactionViolationConfigDetails(any(), any())).thenReturn(List.of(txnConfig));
        getViolatedData.cacheWrapper = cacheWrapperMock;
        Method m = GetViolatedData.class.getDeclaredMethod("getViolationConfigsForViolatedData", ViolatedData.class);
        m.setAccessible(true);
        @SuppressWarnings("unchecked")
        Map.Entry<String, List<KpiViolationConfig>> result = (Map.Entry<String, List<KpiViolationConfig>>) m.invoke(getViolatedData, vd);
        assertNotNull(result);
        assertEquals(Constants.COMMON_ATTRIBUTE, result.getKey());
        assertEquals(1, result.getValue().size());
    }

    @Test
    void testGetViolationConfigsForViolatedData_instanceLevel_nullEntity_returnsNull() throws Exception {
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setEventType(ViolationEventType.KPI_VIOLATION);
        vd.setInstanceId("inst");
        vd.setKpiId("1");
        vd.setKpiAttribute(Constants.COMMON_ATTRIBUTE);
        Map<String, String> meta = new HashMap<>();
        meta.put("violationLevel", "INSTANCE");
        vd.setMetaData(meta);
        when(cacheWrapperMock.getInstanceKPIDetails(any(), any(), anyInt())).thenReturn(null);
        getViolatedData.cacheWrapper = cacheWrapperMock;
        Method m = GetViolatedData.class.getDeclaredMethod("getViolationConfigsForViolatedData", ViolatedData.class);
        m.setAccessible(true);
        Object result = m.invoke(getViolatedData, vd);
        assertNull(result);
    }

    @Test
    void testGetViolationConfigsForViolatedData_serviceLevel_nullDetails_returnsNull() throws Exception {
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setEventType(ViolationEventType.KPI_VIOLATION);
        vd.setInstanceId("inst");
        vd.setKpiId("1");
        vd.setKpiAttribute("ALL");
        Map<String, String> meta = new HashMap<>();
        meta.put("violationLevel", "SERVICE");
        meta.put("serviceIdentifier", "svc");
        vd.setMetaData(meta);
        when(cacheWrapperMock.getServiceKPIDetails(any(), any(), anyInt())).thenReturn(null);
        getViolatedData.cacheWrapper = cacheWrapperMock;
        Method m = GetViolatedData.class.getDeclaredMethod("getViolationConfigsForViolatedData", ViolatedData.class);
        m.setAccessible(true);
        Object result = m.invoke(getViolatedData, vd);
        assertNull(result);
    }

    @Test
    void testGetViolationConfigsForViolatedData_txnViolation_nullComponent_returnsNull() throws Exception {
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setEventType(ViolationEventType.TXN_VIOLATION);
        vd.setKpiId("1");
        vd.setTransactionId("txn");
        when(cacheWrapperMock.getComponentKPIDetails(any(), any(), any())).thenReturn(null);
        getViolatedData.cacheWrapper = cacheWrapperMock;
        Method m = GetViolatedData.class.getDeclaredMethod("getViolationConfigsForViolatedData", ViolatedData.class);
        m.setAccessible(true);
        Object result = m.invoke(getViolatedData, vd);
        assertNull(result);
    }

    @Test
    void testGetViolationConfigsForViolatedData_exception_returnsNull() throws Exception {
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        vd.setEventType(ViolationEventType.KPI_VIOLATION);
        vd.setInstanceId("inst");
        vd.setKpiId("1");
        vd.setKpiAttribute(Constants.COMMON_ATTRIBUTE);
        Map<String, String> meta = new HashMap<>();
        meta.put("violationLevel", "INSTANCE");
        vd.setMetaData(meta);
        when(cacheWrapperMock.getInstanceKPIDetails(any(), any(), anyInt())).thenThrow(new RuntimeException("fail"));
        getViolatedData.cacheWrapper = cacheWrapperMock;
        Method m = GetViolatedData.class.getDeclaredMethod("getViolationConfigsForViolatedData", ViolatedData.class);
        m.setAccessible(true);
        Object result = m.invoke(getViolatedData, vd);
        assertNull(result);
    }

    @Test
    void testIsValidViolationConfig_statusZero_returnsFalse() throws Exception {
        KpiViolationConfig config = KpiViolationConfig.builder()
                .status(0)
                .minThreshold(10.0)
                .maxThreshold(100.0)
                .operation("greater than")
                .build();
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        Method m = GetViolatedData.class.getDeclaredMethod("isValidViolationConfig", KpiViolationConfig.class, ViolatedData.class);
        m.setAccessible(true);
        boolean result = (boolean) m.invoke(getViolatedData, config, vd);
        assertFalse(result);
    }

    @Test
    void testIsValidViolationConfig_nullMaxThreshold_returnsFalse() throws Exception {
        KpiViolationConfig config = KpiViolationConfig.builder()
                .status(1)
                .minThreshold(10.0)
                .maxThreshold(null)
                .operation("greater than")
                .build();
        ViolatedData vd = new ViolatedData("acc", List.of("app"));
        Method m = GetViolatedData.class.getDeclaredMethod("isValidViolationConfig", KpiViolationConfig.class, ViolatedData.class);
        m.setAccessible(true);
        boolean result = (boolean) m.invoke(getViolatedData, config, vd);
        assertFalse(result);
    }
}
