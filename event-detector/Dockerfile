FROM healadmin/openjdk:17.0.13

RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get -y install ca-certificates make openssl bash && \
    apt-get clean

ENV PATH "$PATH:/usr/local/ssl/bin"

ADD http://192.168.13.69:8081/nexus/repository/third-party/consul-template /usr/local/bin/consul-template

ADD ./conf /etc/consul-template/conf
ADD ./templates /etc/consul-template/templates

COPY ./heal-event-detector /opt/heal-event-detector/
COPY ./entrypoint.sh /opt/heal-event-detector/entrypoint.sh

RUN chmod +x /opt/heal-event-detector/entrypoint.sh /usr/local/bin/consul-template && \
    mkdir -p /tmp/logs

EXPOSE 8989

ENTRYPOINT ["/opt/heal-event-detector/entrypoint.sh"]