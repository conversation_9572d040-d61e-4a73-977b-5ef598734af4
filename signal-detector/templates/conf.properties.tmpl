# ==========================================================
# RabbitMQ server configuration
# Basic configuration needed to communicate with RabbitMQ.
# HTTP mode of communication will be utilized in this case.
# ==========================================================
spring.rabbitmq.addresses={{ key "service/rabbitmq/addresses" }}
spring.rabbitmq.username={{ key "service/rabbitmq/username" }}
spring.rabbitmq.password={{ key "service/rabbitmq/password/encrypted" }}
spring.rabbitmq.ssl.enabled={{ key "service/rabbitmq/sslenable" }}
spring.rabbitmq.ssl.algorithm={{ key "service/rabbitmq/sslprotocol" }}
spring.rabbitmq.anomalySignalMessages.prefetchCount={{ key "service/signaldetector/rabbitmq/anomalySignalMessages/prefetchCount" }}
spring.rabbitmq.anomalySignalMessages.acknowledgementMode={{ key "service/signaldetector/rabbitmq/anomalySignalMessages/ackMode" }}
spring.rabbitmq.anomalySignalMessages.concurrentConsumerSize={{ key "service/signaldetector/rabbitmq/anomalySignalMessages/consumerSize" }}

anomaly.messages.queue.name={{ key "service/rabbitmq/anomalysignalsqueue" }}
signal.messages.queue.name={{ key "service/rabbitmq/signalmessagequeue" }}
mle.signal.messages.queue.name={{ key "service/rabbitmq/mlesignalmessagequeue" }}
aiops.signal.messages.queue.name={{ key "service/rabbitmq/signalmessageaiopsqueue" }}

# ==========================================================
#Redis Server Configuration
# ==========================================================
spring.redis.cluster.nodes={{ key "service/redis/nodes" }}
spring.redis.ssl={{ key "service/redis/sslenable" }}
spring.redis.password={{ key "service/redis/password/encrypted" }}
spring.redis.username={{ key "service/redis/username" }}
spring.redis.cluster.mode= {{ key "service/redis/cluster/mode" }}

# ==========================================================
# OpenSearch Details
# ==========================================================
opensearch.batch.size={{key "service/signaldetector/opensearch/batch/size" }}
opensearch.batch.queue.max.size={{key "service/signaldetector/opensearch/batch/queue/max/size" }}
opensearch.data.push.schedule.interval={{key "service/signaldetector/opensearch/data/push/schedule/internal/secs" }}
opensearch.data.push.schedule.initial.delay={{key "service/signaldetector/opensearch/data/push/schedule/initial/delay/secs" }}
opensearch.connection.io.reactor.size={{ key "service/signaldetector/opensearch/connection/io/reactor/size" }}

opensearch.anomalies.index={{key "service/eventdetector/opensearch/index/anomalies" }}
opensearch.signals.index={{key "service/signaldetector/opensearch/index/signals" }}
# ==========================================================
# Thread Pool Configuration
# ==========================================================
thread.pool.core.size={{key "service/signaldetector/threadpoolcoresize"}}
thread.pool.max.size={{key "service/signaldetector/threadpoolmaxsize"}}
thread.pool.max.queue.size={{key "service/signaldetector/threadpoolmaxqueuesize"}}

# ==========================================================
# Signal Severity for Aggregated KPI data
# ==========================================================
signal.severity.high={{key "service/signaldetector/signalseverityhigh"}}
signal.severity.low={{key "service/signaldetector/signalseveritylow"}}


anomaly.delay.minutes={{key "service/signaldetector/anomaly/delay/minutes"}}
signal.cleaner.scheduler.milliseconds={{key "service/signaldetector/clean/scheduler/milliseconds"}}
signal.close.window.time.minutes={{key "service/signaldetector/signal/idle/minutes"}}
info.signal.close.window.time.minutes={{key "service/signaldetector/info/signal/idle/minutes"}}
batch.job.signal.close.window.time.minutes={{key "service/signaldetector/batch/signal/idle/minutes"}}
problem.signal.kpis={{key "service/signaldetector/problem/signal/kpis"}}
offset.from.gmt={{key "service/signaldetector/offset/from/gmt"}}
open.signal.from.time.offset.days={{key "service/signaldetector/open/signal/from/time/offset/days"}}
early.warning.processing.include.outbounds={{key "service/signaldetector/early/warning/processing/include/outbounds"}}

# ==========================================================
# Health Metrics Details
# ==========================================================
health.metrics.update.interval.milliseconds = {{key "service/signaldetector/health/metrics/updateinterval/milliseconds" }}
health.metrics.log.interval.milliseconds={{key "service/signaldetector/health/metrics/loginterval/milliseconds" }}
management.endpoints.web.exposure.include={{key "service/signaldetector/management/endpoints/web/exposure/include" }}
management.endpoints.jmx.exposure.include={{key "service/signaldetector/management/endpoints/jmx/exposure/include" }}
management.endpoint.health.enable={{key "service/signaldetector/management/endpoints/health/enabled" }}
management.endpoints.web.base-path = /measure
management.server.port={{key "service/signaldetector/management/server/port" }}
server.port={{key "service/signaldetector/server/port" }}
spring.jmx.enabled={{key "service/signaldetector/jmx/enabled" }}

# ==========================================================
# Local Cache Details
# ==========================================================

# Data is retrieved from local cache if mode is 0, and the redis cache if mode is 1.
redis.cache.mode={{ key "service/signaldetector/redis/cache/mode" }}

accounts.configuration.cache.expire.interval.minutes={{key "service/signaldetector/accounts/configurationcache/expire/interval/minutes" }}
accounts.configuration.cache.max.size={{key "service/signaldetector/accounts/configurationcache/max/size" }}

application.configuration.cache.expire.interval.minutes={{key "service/signaldetector/application/configurationcache/expire/interval/minutes" }}
application.configuration.cache.max.size={{key "service/signaldetector/application/configurationcache/max/size" }}

component.kpis.configuration.cache.expire.interval.minutes={{key "service/signaldetector/component/kpis/configurationcache/expire/interval/minutes" }}
component.kpis.configuration.cache.max.size={{key "service/signaldetector/component/kpis/configurationcache/max/size" }}

component.kpi.configuration.cache.expire.interval.minutes={{key "service/signaldetector/component/kpi/configurationcache/expire/interval/minutes" }}
component.kpi.configuration.cache.max.size={{key "service/signaldetector/component/kpi/configurationcache/max/size" }}

service.configuration.cache.expire.interval.minutes={{key "service/signaldetector/service/configurationcache/expire/interval/minutes" }}
service.configuration.cache.max.size={{key "service/signaldetector/service/configurationcache/max/size" }}

service.maintenance.configuration.cache.expire.interval.minutes={{key "service/signaldetector/service/maintenance/configurationcache/expire/interval/minutes" }}
service.maintenance.configuration.cache.max.size={{key "service/signaldetector/service/maintenance/configurationcache/max/size" }}

instance.maintenance.configuration.cache.expire.interval.minutes={{key "service/signaldetector/instance/maintenance/configurationcache/expire/interval/minutes" }}
instance.maintenance.configuration.cache.max.size={{key "service/signaldetector/instance/maintenance/configurationcache/max/size" }}

instance.kpis.configuration.cache.expire.interval.minutes={{key "service/signaldetector/instance/kpis/configurationcache/expire/interval/minutes" }}
instance.kpis.configuration.cache.max.size={{key "service/signaldetector/instance/kpis/configurationcache/max/size" }}

service.neighbours.configuration.cache.expire.interval.minutes={{key "service/signaldetector/service/neighbours/configurationcache/expire/interval/minutes" }}
service.neighbours.configuration.cache.max.size={{key "service/signaldetector/service/neighbours/configurationcache/max/size" }}

heal.types.configuration.cache.expire.interval.minutes={{key "service/signaldetector/heal/types/configurationcache/expire/interval/minutes" }}
heal.types.configuration.cache.max.size={{key "service/signaldetector/heal/types/configurationcache/max/size" }}

dropped.anomaly.checker.scheduler.seconds={{ "service/signaldetector/dropped/anomaly/checker/scheduler/interval/seconds" }}
failed.open.signal.queue.max.size={{ "service/signaldetector/failed/open/signal/queue/max/size" }}