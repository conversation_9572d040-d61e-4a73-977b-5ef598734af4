package com.heal.signal.detector.process;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.Account;
import com.heal.signal.detector.TestMain;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.util.LocalQueues;
import com.heal.signal.detector.util.TestUtil;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.*;

import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;

class BatchSignalProcessorTest extends TestMain {
    @InjectMocks
    BatchSignalProcessor batchSignalProcessor;

    @Mock
    SignalRepo signalRepo;

    @Mock
   LocalQueues localQueues;

    @Test
    void processBatchJobEvent() throws Exception {
        String accountId = "sample_account_identifier";
        Mockito.when(signalRepo.getOpenSignals(accountId, true)).thenThrow(new Exception());

        Map<String, String> metaDataMap = new HashMap<>();
        String groupName = "sample_group_name";
        metaDataMap.put("group_name", groupName);

        Map<String, Double> threasholdMap = new HashMap<>();
        Calendar time = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        time.set(Calendar.SECOND, 0);
        time.set(Calendar.MILLISECOND, 0);
        long startTime = time.getTimeInMillis();
        long endTime = time.getTimeInMillis();
        String batchJobId = "";
        String kpiId = "sample_kpi_identifier";
        String anomalyEventId = "AE-B-" + batchJobId + "-" + kpiId + "-" + endTime / 1000;
        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(startTime, endTime, metaDataMap, threasholdMap,
                anomalyEventId, "sample_account_identifier", "sample_application_identifier", "SOR",
                "not equals", "sample_kpi_identifier", "", false, "Severe", true);
        Account account = Account.builder().identifier(accountId).id(9).build();

        batchSignalProcessor.processBatchJobEvent(anomalyEvent, account);

        verify(localQueues).addToFailedOpenSignalQueue(argThat(pojo ->
                pojo.getSignalType() == SignalType.BATCH_JOB &&
                        pojo.getAnomalyEvent() == anomalyEvent &&
                        pojo.getAccount() == account
        ));

    }

}