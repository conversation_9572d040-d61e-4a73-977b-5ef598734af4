package com.heal.signal.detector.process;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicKpiEntity;
import com.heal.signal.detector.TestMain;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.util.LocalQueues;
import com.heal.signal.detector.util.TestUtil;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;

class WarningSignalProcessorTest extends TestMain {
    @InjectMocks
    WarningSignalProcessor warningSignalProcessor;

    @Mock
    SignalRepo signalRepo;

    @Mock
    private LocalQueues localQueues;

    private final String accountId = "sample_account_identifier";

    private final String groupName = "sample_group_name";
    private final String kpiId = "sample_kpi_identifier";

    @Test
    void processBatchJobEvent() throws Exception {
        Mockito.when(signalRepo.getOpenSignals(accountId, true)).thenThrow(new Exception());

        Map<String, String> metaDataMap = new HashMap<>();
        metaDataMap.put("group_name", groupName);

        Map<String, Double> threasholdMap = new HashMap<>();
        Calendar time = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        time.set(Calendar.SECOND, 0);
        time.set(Calendar.MILLISECOND, 0);
        long startTime = time.getTimeInMillis();
        long endTime = time.getTimeInMillis();
        String instance = "sampleInstance";
        String anomalyEventId = "AE-W-" + instance + "-" + kpiId + "-" + endTime / 1000;
        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(startTime, endTime, metaDataMap, threasholdMap,
                anomalyEventId, "sample_account_identifier", "sample_application_identifier", "SOR",
                "not equals", "sample_kpi_identifier", "", false, "Severe", false);
        Account account = Account.builder().identifier(accountId).id(9).build();

        BasicKpiEntity kpiEntity = BasicKpiEntity.builder().identifier(kpiId).build();

        warningSignalProcessor.processEarlyWarningEvent(anomalyEvent, kpiEntity, account);

        verify(localQueues).addToFailedOpenSignalQueue(argThat(pojo ->
                pojo.getSignalType() == SignalType.EARLY_WARNING &&
                        pojo.getAnomalyEvent() == anomalyEvent &&
                        pojo.getAccount() == account
        ));

    }

}