package com.heal.signal.detector.process;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.cache.CacheWrapper;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.pojos.FailedOpenSignalPojo;
import com.heal.signal.detector.pojos.SignalChanges;
import com.heal.signal.detector.pojos.SignalHelper;
import com.heal.signal.detector.pojos.SignalStatus;
import com.heal.signal.detector.service.ForwarderToQueue;
import com.heal.signal.detector.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ProblemSignalProcessor {
    @Autowired
    Commons commons;

    @Autowired
    HealthMetrics metrics;

    @Autowired
    WarningSignalProcessor warningProcessor;

    @Autowired
    ForwarderToQueue forwarder;

    @Autowired
    SignalRepo signalRepo;

    @Autowired
    CacheWrapper wrapper;

    @Autowired
    LocalQueues localQueues;

    public void processProblemEvent(AnomalyEventProtos.AnomalyEvent anomalyEvent, BasicKpiEntity kpiEntity, Account account) {

        long st = System.currentTimeMillis();
        Set<SignalDetails> openSignals;
        try {
            openSignals = signalRepo.getOpenSignals(account.getIdentifier(), true);
        } catch (Exception e) {
            log.error("Error occurred while fetching open signals so pushing in failed queue will be processing it later " +
                            "for accountIdentifier: {}, anomalyId: {}",
                    account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
            localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder().signalType(SignalType.PROBLEM)
                    .anomalyEvent(anomalyEvent)
                    .account(account)
                    .kpiEntity(kpiEntity)
                    .build());
            return;
        }
        try {
            String serviceId = anomalyEvent.getKpis().getSvcId(0);
            Service service = wrapper.getServiceDetailsByIdentifier(anomalyEvent.getAccountId(), serviceId);
            if (service == null) {
                log.error("Signal will not be processed. Reason: Invalid service identifier:{}, Anomaly event details:{}", serviceId, anomalyEvent);
                return;
            }

            Map<String, String> metaData = commons.getSignalMetaData(anomalyEvent, SignalType.PROBLEM);
            Set<SignalDetails> signals = commons.getOpenSignals(account.getIdentifier(), openSignals, serviceId);

            log.debug("Open signals in problem processor, anomaly id:{}, signal ids: {}", anomalyEvent.getAnomalyId(), signals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining()));
            Set<BasicEntity> destServicesMap = wrapper.getNeighbours(account.getIdentifier(), serviceId);
            if(destServicesMap.isEmpty()){
                log.warn("Could not find service neighbours from redis for service {} of the account {}", service, account.getIdentifier());
            }
            /*Set<BasicEntity> destServicesMap = redisUtilities.getNeighbours(account.getIdentifier(), serviceId);*/
            destServicesMap.forEach(destService -> signals.addAll(commons.getOpenSignals(account.getIdentifier(), openSignals, destService.getIdentifier())));

            if (signals.isEmpty()) {
                log.info("Problem wont be created because the first anomaly event in entry service will be created as early warning.");
                warningProcessor.processEarlyWarningEvent(anomalyEvent, kpiEntity, account);
                return;
            }

            log.info("Number of signals:{} found for anomalyId:{}", signals.size(), anomalyEvent.getAnomalyId());
            int higherSeverityId = wrapper.getSeverityId("Severe");

            SignalHelper ewSignalHelper = new SignalHelper();
            ewSignalHelper.getAnomalies().add(anomalyEvent.getAnomalyId());
            ewSignalHelper.getRcaAnomalies().add(anomalyEvent.getAnomalyId());
            ewSignalHelper.getRcaServices().add(serviceId);

            SignalHelper pSignalHelper = new SignalHelper();
            int severityId = wrapper.getSeverityId(anomalyEvent.getKpis().getThresholdSeverity());
            pSignalHelper.getAnomalies().add(anomalyEvent.getAnomalyId());
            pSignalHelper.getRcaAnomalies().add(anomalyEvent.getAnomalyId());
            pSignalHelper.getRcaServices().add(serviceId);

            Set<String> serviceIds = new HashSet<>();

            boolean isValidAnomaly = true;

            //Get all related signals for anomaly we received, then populate the helper(early/problem) objects.
            for (SignalDetails signalDetails : signals) {

                if (anomalyEvent.getEndTimeGMT() < signalDetails.getStartedTime()) {
                    log.error("We will drop this anomaly because anomaly time:{} is less than the signal id:{}, " +
                            "start time:{} for anomaly:{}", anomalyEvent.getEndTimeGMT(), signalDetails.getSignalId(), signalDetails.getStartedTime(), anomalyEvent);
                    isValidAnomaly = false;
                    metrics.updateErrors();
                    break;
                }
                SignalChanges signalChanges = SignalChanges.builder()
                        .isSeverityChanged(false)
                        .isServiceAdded(false)
                        .build();

                signalDetails.getMetadata().putAll(metaData);
                if (signalDetails.getSignalType().equalsIgnoreCase(SignalType.EARLY_WARNING.name())) {
                    ewSignalHelper.getSignals().add(signalDetails.getSignalId());
                    ewSignalHelper.getAnomalies().addAll(signalDetails.getAnomalies());
                    ewSignalHelper.getAffectedServices().addAll(signalDetails.getServiceIds());
                    if (signalDetails.getRootCauseAnomalyIds() != null) {
                        ewSignalHelper.getRcaAnomalies().addAll(signalDetails.getRootCauseAnomalyIds());
                    }
                    if (signalDetails.getRootCauseServiceIds() != null) {
                        ewSignalHelper.getRcaServices().addAll(signalDetails.getRootCauseServiceIds());
                    }
                    // Check the signal is updating from default to severe
                    if (higherSeverityId != signalDetails.getSeverityId() && higherSeverityId == severityId) {
                        signalDetails.setSeverityId(severityId);
                        signalChanges.setSeverityChanged(true);
                    }

                    log.trace("Service ids:{} for signalId:{}", signalDetails.getServiceIds(), signalDetails.getSignalId());
                    serviceIds.addAll(signalDetails.getServiceIds());

                    signalChanges.setServiceAdded(!signalDetails.getServiceIds().contains(serviceId));
                    ewSignalHelper.getSignalDetailChanges().put(signalDetails.getSignalId(), signalChanges);
                    ewSignalHelper.getSignalDetailsMap().put(signalDetails.getSignalId(), signalDetails);
                } else if (signalDetails.getServiceIds().contains(serviceId)) {
                    pSignalHelper.getSignals().add(signalDetails.getSignalId());
                    // Check the signal is updating from default to severe
                    if (higherSeverityId != signalDetails.getSeverityId() && higherSeverityId == severityId) {
                        signalDetails.setSeverityId(severityId);
                        signalChanges.setSeverityChanged(true);
                    }

                    signalChanges.setServiceAdded(!signalDetails.getServiceIds().contains(serviceId));

                    pSignalHelper.getSignalDetailChanges().put(signalDetails.getSignalId(), signalChanges);
                    pSignalHelper.getSignalDetailsMap().put(signalDetails.getSignalId(), signalDetails);
                }
            }

            if (!isValidAnomaly) {
                return;
            }

            serviceIds.add(serviceId);

            Set<String> problems = new HashSet<>();

            // Create a problem
            if (pSignalHelper.getSignals().isEmpty()) {

                String signalId = commons.createSignalId("P", account.getId(), Integer.parseInt(anomalyEvent.getKpis().getKpiId()), service.getId(), anomalyEvent.getEndTimeGMT() / 1000);
                log.info("No problem signal is found. So, we will create a new problem signal for anomalyId:{}, accountId:{}, serviceId:{}, serviceIds size:{}, signalId:{}.",
                        anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), serviceId, serviceIds.size(), signalId);
                SignalDetails signalDetails = commons.createSignal(signalId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getEndTimeGMT(), anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(),
                        ewSignalHelper.getRcaAnomalies(), SignalType.PROBLEM, serviceIds, ewSignalHelper.getRcaServices(),
                        ewSignalHelper.getAnomalies(), serviceId, ewSignalHelper.getSignals(), severityId, metaData);

                AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
                        kpiEntity.getCategoryDetails().getIdentifier(), service.getIdentifier(), SignalType.PROBLEM,
                        severityId, anomalyEvent.getEndTimeGMT(), true, anomalyEvent.getAnomalyId(),
                        anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
                        anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap());

                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary);
                boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
                        Collections.singleton(signalId), anomalyEvent.getAccountId());
                if (queueUpdateStatus) {
                    log.trace("Anomaly updated into scheduler queue for problem signal, signalId:{}, anomalyId:{}", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
                } else {
                    log.error("Anomaly not updated into scheduler queue for problem signal, signalId:{}, anomalyId:{}, because of this anomaly wont be update with signal id.", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
                }
//                signalRepo.serviceSignalDetailsCreate(account.getIdentifier(), signalId, signalDetails.getServiceIds());

                metrics.updateSignalOpenCount(1);
                metrics.updateSnapshots(signalDetails.getSignalId() + "_CREATED", 1);
                if (insertStatus) {
                    forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, false,
                            false, false, false, anomalySummary));
                }
                log.info("Problem signal is created for anomalyId:{}, signal details:{}", anomalyEvent.getAnomalyId(), signalDetails);
                problems.add(signalId);
            } else {
                // Update the problem with current anomaly details
                pSignalHelper.getSignals().forEach(pSignalId -> {
                    SignalDetails signalDetails = pSignalHelper.getSignalDetailsMap().get(pSignalId);
                    SignalChanges signalChanges = pSignalHelper.getSignalDetailChanges().get(pSignalId);

                    AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
                            kpiEntity.getCategoryDetails().getIdentifier(), service.getIdentifier(), SignalType.PROBLEM,
                            signalChanges.isSeverityChanged() ? severityId : signalDetails.getSeverityId(), anomalyEvent.getEndTimeGMT(), true, anomalyEvent.getAnomalyId(),
                            anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
                            anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap());

                    boolean updateStatus = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(), SignalStatus.OPEN.name(),
                            signalDetails.getMetadata(), anomalyEvent.getAnomalyId(), pSignalId, account.getIdentifier(),
                            signalDetails.getSeverityId(), anomalySummary, signalDetails.getStartedTime());
                    boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
                            Collections.singleton(pSignalId), account.getIdentifier());
                    if (queueUpdateStatus) {
                        log.trace("Anomaly updated into scheduler queue for problem signal, signalId:{}, anomalyId:{}", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
                    } else {
                        log.error("Anomaly not updated into scheduler queue for problem signal, signalId:{}, anomalyId:{}, because of this anomaly wont be update with signal id.", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
                    }
                    metrics.updateSignalUpdateCount(1);
                    metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);

                    if (updateStatus) {
                        forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, false,
                                signalChanges.isSeverityChanged(), signalChanges.isServiceAdded(), false, anomalySummary));
                    }
                    log.info("Problem signal is updated for anomalyId:{}, signal details:{}", anomalyEvent.getAnomalyId(), signalDetails);
                    problems.add(pSignalId);
                });
            }

            // upgrade early warning and update problems as related signals.
            log.debug("Upgrade the early warnings for signals:{}, problems:{}", ewSignalHelper.getSignals(), problems);
            ewSignalHelper.getSignals().forEach(ewSignalId -> {
                SignalDetails signalDetails = ewSignalHelper.getSignalDetailsMap().get(ewSignalId);
                SignalChanges signalChanges = ewSignalHelper.getSignalDetailChanges().get(ewSignalId);

                signalDetails.getMetadata().put("end_time", anomalyEvent.getEndTimeGMT() + "");
                if (signalDetails.getRelatedSignals() != null) {
                    signalDetails.getRelatedSignals().addAll(problems);
                } else {
                    signalDetails.setRelatedSignals(problems);
                }

                signalDetails.setCurrentStatus(SignalStatus.UPGRADED.name());
                boolean updateStatus = signalRepo.signalDetailsUpdateStatus(anomalyEvent.getEndTimeGMT(), signalDetails.getCurrentStatus(),
                        signalDetails.getMetadata(), ewSignalId, signalDetails.getRelatedSignals(), anomalyEvent.getAccountId(),
                        signalDetails.getStartedTime(), true);

                metrics.updateSignalUpdateCount(1);
                metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);

                if (updateStatus) {
                    forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, true,
                            signalChanges.isSeverityChanged(), signalChanges.isServiceAdded(), true, null));
                }
                log.info("PSP:Early warning signal id:{} is upgraded to problem id:{} for anomalyId:{}, signal details:{}", signalDetails.getSignalId(), problems, anomalyEvent.getAnomalyId(), signalDetails);
            });
        } catch (Exception e) {
            log.error("Error occurred while processing anomaly event. anomalyEvent:{}, account:{}, kpiEntity:{}", anomalyEvent, account, kpiEntity, e);
            metrics.updateErrors();
        } finally {
            long processTime = System.currentTimeMillis() - st;
            log.debug("{}:Anomaly process time for problem signal is {} ms.", anomalyEvent.getAnomalyId(), processTime);
            metrics.updateProcessDetails("ProblemAnomalyProcessTime", processTime);
            metrics.updateProcessDetailsCounter("ProblemAnomalyProcessCounter", processTime);
        }
    }

}
