package com.heal.signal.detector.util;

import com.heal.signal.detector.pojos.FailedOpenSignalPojo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Queue;
import java.util.concurrent.ArrayBlockingQueue;

@Slf4j
@Component
public class LocalQueues {

    @Value("${failed.open.signal.queue.max.size:10000}")
    private int failedOpenSignalQueueMaxSize;

    @Autowired
    HealthMetrics metrics;

    private Queue<FailedOpenSignalPojo> failedOpenSignalQueue;

    @PostConstruct
    public void init() {
        log.info("Initializing failedOpenSignalQueue with sizes: {}", failedOpenSignalQueueMaxSize);
        failedOpenSignalQueue = new ArrayBlockingQueue<>(failedOpenSignalQueueMaxSize);
    }

    public void addToFailedOpenSignalQueue(FailedOpenSignalPojo failedOpenSearchRequestPojo) {
        try {
            boolean isAdded = failedOpenSignalQueue.offer(failedOpenSearchRequestPojo);

            if (isAdded) {
                log.debug("Successfully added FailedOpenSignalPojo to the queue.");
                metrics.setFailedQueueSize(failedOpenSignalQueue.size());
            } else {
                log.error("Failed to add to FailedOpenSignalQueue. Queue has reached its max capacity: {}", failedOpenSearchRequestPojo);
            }
        } catch (Exception e) {
            log.error("Exception occurred while adding to FailedOpenSignalQueue: {}", e.getMessage(), e);
            metrics.updateErrors();
        }
    }

    public Queue<FailedOpenSignalPojo> getFailedQueue() {
        return failedOpenSignalQueue;
    }

}
