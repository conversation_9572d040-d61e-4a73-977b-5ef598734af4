package com.heal.signal.detector.scheduler;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.cache.CacheWrapper;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.pojos.SignalStatus;
import com.heal.signal.detector.service.ForwarderToQueue;
import com.heal.signal.detector.util.Commons;
import com.heal.signal.detector.util.HealthMetrics;
import com.heal.signal.detector.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SignalChecker {

    @Autowired
    private HealthMetrics healthMetrics;

    @Autowired
    RedisUtilities redisUtilities;

    @Autowired
    Commons commons;

    @Autowired
    SignalRepo signalRepo;

    @Autowired
    ForwarderToQueue forwarder;

    @Autowired
    CacheWrapper wrapper;

    @Value("${info.signal.close.window.time.minutes:5}")
    private int infoSignalIdleTimeInMin;

    @Value("${batch.job.signal.close.window.time.minutes:5}")
    private int batchSignalIdleTimeInMin;

    @Value("${signal.close.window.time.minutes:15}")
    private int signalIdleTimeInMin;

    @Async(value = "ThreadPoolTaskExecutorSignalChecker")
    @Scheduled(initialDelay = 1000, fixedRateString = "${signal.cleaner.scheduler.milliseconds:30000}")
    public void checkSignals() {
        log.trace("Signal checker scheduler method called.");

        Set<SignalDetails> signalDetailList = new HashSet<>();
        try {
            // Getting all the account details.
            List<Account> accounts = wrapper.getAccounts();
            if (accounts.isEmpty()) {
                log.error("Could not find account details from redis");
                return;
            }

            log.debug("Total number of accounts from redis cache is:{}", accounts.size());
            //Skipping global account
            signalDetailList = accounts.stream()
                    .filter(a -> !a.getIdentifier().equalsIgnoreCase("e573f852-5057-11e9-8fd2-b37b61e52317"))
                    .map(a -> {
                        // ServiceId, <SignalId, SignalTime>
                        Map<String, Map<String, Long>> serviceSignals = redisUtilities.getOpenSignalsInRedis(a.getIdentifier());

                        log.debug("Total number of service signals from redis cache is {}, account:{}", serviceSignals.size(), a.getIdentifier());

                        //Loop all the signals for account and remove the closed signals from redis cache.
                        serviceSignals.forEach((serviceId, value) -> value.forEach((signalId, signalTime) -> {
                            boolean isSignalsIndexPresent = signalRepo.isSignalsIndexPresent(a.getIdentifier(), signalTime, signalTime);
                            if (isSignalsIndexPresent) {
                                SignalDetails signalDetails = signalRepo.getSignalById(signalId, signalTime, a.getIdentifier());
                                log.trace("Check the SignalId:{}, startTime:{}, AccountId:{}, signal details:{}", signalId, signalTime, a.getIdentifier(), signalDetails);
                                if (signalDetails != null && !signalDetails.getCurrentStatus().equalsIgnoreCase("OPEN")) {
                                    log.debug("Removing the closed signal from the redis cache. AccountId:{}, SignalId:{}, SignalTime:{}, ServiceId:{}",
                                            a.getIdentifier(), signalId, signalTime, serviceId);
                                    redisUtilities.updateServiceSignal(a.getIdentifier(), signalId, signalTime, serviceId, signalDetails.getCurrentStatus());
                                }
                            } else {
                                log.debug("Removing the signal from the redis cache because it's corresponding OS Index is not present." +
                                        " AccountId:{}, SignalId:{}, SignalTime:{}, ServiceId:{}", a.getIdentifier(), signalId, signalTime, serviceId);
                                redisUtilities.updateServiceSignal(a.getIdentifier(), signalId, signalTime, serviceId, SignalStatus.CLOSED.name());
                            }
                        }));
                        Set<SignalDetails> signalDetails;
                        try {
                             signalDetails = signalRepo.getOpenSignals(a.getIdentifier(), false);
                        } catch (Exception e) {
                            log.error("Error occurred while fetching open signals for accountIdentifier: {}", a.getIdentifier(), e);
                            return null;
                        }
                        if (signalDetails.isEmpty()) {
                            log.debug("No open signals found.");
                            return null;
                        }

                        return signalDetails;
                    })
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error while checking the signal close details and remove the signals from redis cache.", e);
        }

        // Get the open signals for all the accounts
        if (signalDetailList.isEmpty()) {
            log.debug("No open signals not found.");
            return;
        }
        log.debug("No of open signals:{}", signalDetailList.size());

        signalDetailList.forEach(signalDetails -> {
            try {
                if (signalDetails.getStartedTime() == null || signalDetails.getUpdatedTime() == null) {
                    log.error("Start time or update time is null for signalId:{}, startTime:{}, updateTime:{}.", signalDetails.getSignalId(), signalDetails.getStartedTime(), signalDetails.getUpdatedTime());
                    healthMetrics.updateErrors();
                    return;
                }
                long idleTime = commons.getSignalIdealTime(signalDetails.getSignalType(), infoSignalIdleTimeInMin, batchSignalIdleTimeInMin, signalIdleTimeInMin);
                long latestTime = Math.max(signalDetails.getUpdatedTime(), signalDetails.getTxnAnomalyTime() == null ? 0 : signalDetails.getTxnAnomalyTime());
                long currentTime = Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeInMillis();
                log.info("signal id : {}, updatedTime : {}, txnUpdatedTime : {}, currentTime:{} and meta data : {}", signalDetails.getSignalId(),
                        signalDetails.getUpdatedTime(), signalDetails.getTxnAnomalyTime(), currentTime, signalDetails.getMetadata());

                Map<String, String> newMetaData = new HashMap<>();
                if (!signalDetails.getMetadata().containsKey("end_time")) {
                    newMetaData.put("end_time", String.valueOf(latestTime + idleTime));
                }

                String accountId = (signalDetails.getMetadata() != null && signalDetails.getMetadata().containsKey("account_id")) ?
                        signalDetails.getMetadata().get("account_id") : null;

                if (accountId == null) {
                    log.error("Account id does not exists for signalId:{}.", signalDetails.getSignalId());
                    healthMetrics.updateErrors();
                    return;
                }

                boolean isMaintenanceShouldExclude = false;
                if (signalDetails.getMetadata().containsKey("lastMaintenanceExcluded")) {
                    long lastMaintenanceExcluded = Long.parseLong(signalDetails.getMetadata().getOrDefault("lastMaintenanceExcluded", "0"));
                    if (lastMaintenanceExcluded != 0L) {
                        log.info("curTime : {}, last maintenance exclude time :{}, SIGNAL_IDLE_TIME : {}", currentTime, lastMaintenanceExcluded, idleTime);
                        isMaintenanceShouldExclude = !((currentTime - idleTime) > lastMaintenanceExcluded);
                    }
                }

                boolean isMaintenance = false;
                if (!isMaintenanceShouldExclude) {
                    isMaintenance = commons.isServicesUnderMaintenance(accountId, signalDetails.getServiceIds(), signalDetails.getSignalId());
                }

                if (isMaintenance) {
                    newMetaData.put("closing_reason", "Signal closed as all impacted services are put under maintenance.");
                    newMetaData.put("end_time", String.valueOf(latestTime));
                } else {
                    newMetaData.put("closing_reason", "Signal closed as no new event is received on any of the impacted services in last " + idleTime / 60000 + " minutes.");
                    newMetaData.put("end_time", String.valueOf(latestTime + idleTime));
                }
                if (((currentTime - latestTime) > idleTime) || (signalDetails.getMetadata() != null &&
                        signalDetails.getMetadata().containsKey("end_time")) || isMaintenance) {
                    signalDetails.setCurrentStatus(SignalStatus.CLOSED.name());
                    signalDetails.getMetadata().putAll(newMetaData);
                    boolean isUpdated = signalRepo.closeSignal(signalDetails, accountId);
                    log.info("Dead signal : {} is successfully closed.", signalDetails.getSignalId());
                    healthMetrics.updateSignalCloseCount();
                    healthMetrics.updateSnapshots(signalDetails.getSignalId() + "_CLOSED", 1);

                    if (isUpdated) {
                        forwarder.sendSignalMessages(commons.getSignalProto(accountId, signalDetails, true,
                                false, false, true, null));
                    }
                } else {
                    forwarder.sendSignalMessages(commons.getSignalProto(accountId, signalDetails, true,
                            false, false, false, null));
                }
            } catch (Exception e) {
                healthMetrics.updateErrors();
                log.error("Error while checking the signal close details. signal id:{}", signalDetails.getSignalId(), e);
            }
        });
    }

}
