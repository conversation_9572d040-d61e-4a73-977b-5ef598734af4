package com.heal.signal.detector.config;

import com.heal.signal.detector.util.HealthMetrics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ExecutorConfig {
    @Autowired
    private HealthMetrics healthMetrics;

    @Value("${scheduler.signalChecker.thread.pool.core.size:1}")
    private int corePoolSizeSignalChecker;
    @Value("${scheduler.signalChecker.thread.pool.max.size:1}")
    private int maxPoolSizeSignalChecker;
    @Value("${scheduler.signalChecker.thread.pool.queue.capacity:1}")
    private int queueCapacitySignalChecker;

    @Value("${scheduler.pushToOS.thread.pool.core.size:1}")
    private int corePoolSizePushToOS;
    @Value("${scheduler.pushToOS.thread.pool.max.size:1}")
    private int maxPoolSizePushToOS;
    @Value("${scheduler.pushToOS.thread.pool.queue.capacity:1}")
    private int queueCapacityPushToOS;

    @Value("${scheduler.pushUpdateQueriesToOS.thread.pool.core.size:1}")
    private int corePoolSizePushUpdateQueriesToOS;
    @Value("${scheduler.pushUpdateQueriesToOS.thread.pool.max.size:1}")
    private int maxPoolSizePushUpdateQueriesToOS;
    @Value("${scheduler.pushUpdateQueriesToOS.thread.pool.queue.capacity:1}")
    private int queueCapacityPushUpdateQueriesToOS;

    @Value("${scheduler.healthMetrics.thread.pool.core.size:1}")
    private int corePoolSizeHealthMetrics;
    @Value("${scheduler.healthMetrics.thread.pool.max.size:1}")
    private int maxPoolSizeHealthMetrics;
    @Value("${scheduler.healthMetrics.thread.pool.queue.capacity:1}")
    private int queueCapacityHealthMetrics;

    @Value("${scheduler.droppedAnomalyChecker.thread.pool.core.size:1}")
    private int corePoolSizeDroppedAnomalyChecker;
    @Value("${scheduler.droppedAnomalyChecker.thread.pool.max.size:1}")
    private int maxPoolSizeDroppedAnomalyChecker;
    @Value("${scheduler.droppedAnomalyChecker.thread.pool.queue.capacity:1}")
    private int queueCapacityDroppedAnomalyChecker;
    @Value("${scheduler.droppedAnomalyChecker.thread.pool.execute.max.time.sec:30}")
    private int maxTimeDroppedAnomalyChecker;


    @Bean("ThreadPoolTaskDroppedAnomalyChecker")
    public ThreadPoolTaskExecutor getAsyncExecutorDroppedAnomalyChecker() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSizeDroppedAnomalyChecker);
        executor.setMaxPoolSize(maxPoolSizeDroppedAnomalyChecker);
        executor.setThreadNamePrefix("SD-Scheduler-DroppedAnomalyChecker-Worker-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(maxTimeDroppedAnomalyChecker);
        executor.setQueueCapacity(queueCapacityDroppedAnomalyChecker);
        executor.setRejectedExecutionHandler((runnable, threadPoolExecutor) -> healthMetrics.updateThreadRejectedTaskCount(executor.getThreadNamePrefix()));
        executor.initialize();

        return executor;
    }


    @Bean("ThreadPoolTaskExecutorSignalChecker")
    public ThreadPoolTaskExecutor getAsyncExecutorSignalChecker() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSizeSignalChecker);
        executor.setMaxPoolSize(maxPoolSizeSignalChecker);
        executor.setThreadNamePrefix("SD-Scheduler-SignalChecker-Worker-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(10);
        executor.setQueueCapacity(queueCapacitySignalChecker);
        executor.setRejectedExecutionHandler((runnable, threadPoolExecutor) -> healthMetrics.updateThreadRejectedTaskCount(executor.getThreadNamePrefix()));
        executor.initialize();

        return executor;
    }

    @Bean("ThreadPoolTaskExecutorPushToOS")
    public ThreadPoolTaskExecutor getAsyncExecutorPushToOS() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSizePushToOS);
        executor.setMaxPoolSize(maxPoolSizePushToOS);
        executor.setThreadNamePrefix("SD-Scheduler-PushToOS-Worker-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(10);
        executor.setQueueCapacity(queueCapacityPushToOS);
        executor.setRejectedExecutionHandler((runnable, threadPoolExecutor) -> healthMetrics.updateThreadRejectedTaskCount(executor.getThreadNamePrefix()));
        executor.initialize();

        return executor;
    }

    @Bean("ThreadPoolTaskExecutorPushUpdateQueriesToOS")
    public ThreadPoolTaskExecutor getAsyncExecutorPushUpdateQueriesToOS() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSizePushUpdateQueriesToOS);
        executor.setMaxPoolSize(maxPoolSizePushUpdateQueriesToOS);
        executor.setThreadNamePrefix("SD-Scheduler-PushUpdateQueriesToOS-Worker-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(10);
        executor.setQueueCapacity(queueCapacityPushUpdateQueriesToOS);
        executor.setRejectedExecutionHandler((runnable, threadPoolExecutor) -> healthMetrics.updateThreadRejectedTaskCount(executor.getThreadNamePrefix()));
        executor.initialize();

        return executor;
    }

    @Bean("ThreadPoolTaskExecutorHealthMetrics")
    public ThreadPoolTaskExecutor getAsyncExecutorHealthMetrics() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSizeHealthMetrics);
        executor.setMaxPoolSize(maxPoolSizeHealthMetrics);
        executor.setThreadNamePrefix("SD-Scheduler-HealthMetrics-Worker-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(10);
        executor.setQueueCapacity(queueCapacityHealthMetrics);
        executor.setRejectedExecutionHandler((runnable, threadPoolExecutor) -> healthMetrics.updateThreadRejectedTaskCount(executor.getThreadNamePrefix()));
        executor.initialize();

        return executor;
    }
}
