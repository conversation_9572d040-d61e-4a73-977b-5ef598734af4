package com.heal.signal.detector.util;


import lombok.Getter;
import lombok.Setter;
import org.springframework.jmx.export.annotation.ManagedAttribute;
import org.springframework.jmx.export.annotation.ManagedResource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@ManagedResource(objectName = "SignalDetector:name=ApplicationInfo")
public class HealthMetrics {
    private int signalInputQueueReadData = 0;
    private int signalOutputQueueWriteData = 0;
    private int errors = 0;
    private int maintenanceEvents = 0;
    @Setter
    private String readSignalQueueName;
    @Setter
    private String writeSignalQueueName;
    private int signalOpenCount = 0;
    private int signalCloseCount = 0;
    private int signalUpdateCount = 0;
    private int redisKeysNotFound = 0;
    private Map<String, Integer> snapshots = new HashMap<>();
    private final Map<String, Long> processDetails = new HashMap<>();
    @Setter
    private String externalSignalInputQueueName;
    private int externalSignalInputQueueReadData = 0;

    private int aiopsSignalOutputQueueWriteData = 0;
    @Setter
    private String writeAIOPsSignalQueueName;
    private final Map<String, Long> threadRejectedTaskCountMap = new HashMap<>();

    @Setter
    @Getter
    private int failedQueueSize = 0;


    @ManagedAttribute
    public int getSignalInputQueueReadData() {
        return signalInputQueueReadData;
    }

    public void updateSignalInputQueueReadData() {
        signalInputQueueReadData++;
    }

    @ManagedAttribute
    public int getSignalOutputQueueWriteData() {
        return signalOutputQueueWriteData;
    }

    public void updateSignalOutputQueueWriteData() {
        signalOutputQueueWriteData++;
    }

    @ManagedAttribute
    public int getErrors() {
        return errors;
    }

    public void updateErrors() {
        errors++;
    }

    @ManagedAttribute
    public int getMaintenanceEvents() {
        return maintenanceEvents;
    }

    public void updateMaintenanceEvents() {
        maintenanceEvents++;
    }

    @ManagedAttribute
    public String getReadSignalQueueName() {
        return readSignalQueueName;
    }

    @ManagedAttribute
    public String getWriteSignalQueueName() {
        return writeSignalQueueName;
    }

    @ManagedAttribute
    public int getSignalOpenCount() {
        return signalOpenCount;
    }

    public void updateSignalOpenCount(int count) {
        signalOpenCount += count;
    }

    @ManagedAttribute
    public int getSignalCloseCount() {
        return signalCloseCount;
    }

    public void updateSignalCloseCount() {
        signalCloseCount++;
    }

    @ManagedAttribute
    public int getSignalUpdateCount() {
        return signalUpdateCount;
    }

    public void updateSignalUpdateCount(int count) {
        signalUpdateCount += count;
    }

    public void resetSnapshots() {
        snapshots = new HashMap<>();
    }

    @ManagedAttribute
    public Map<String, Integer> getSnapshots() {
        return snapshots;
    }

    public void updateSnapshots(String keyName, int value) {
        snapshots.put(keyName, snapshots.getOrDefault(keyName, 0) + value);
    }

    @ManagedAttribute
    public int getRedisKeysNotFound() {
        return redisKeysNotFound;
    }

    public void updateRedisKeysNotFound() {
        redisKeysNotFound++;
    }

    @ManagedAttribute
    public Map<String, Long> getProcessDetails() {
        return processDetails;
    }

    public void updateProcessDetailsCounter(String keyName, long value) {
        if (value > 4000) {
            processDetails.put(keyName, processDetails.getOrDefault(keyName, 0L) + 1);
        }
    }

    @ManagedAttribute
    public String getExternalSignalInputQueueName() {
        return externalSignalInputQueueName;
    }

    @ManagedAttribute
    public int getExternalSignalInputQueueReadData() {
        return externalSignalInputQueueReadData;
    }

    public void updateExternalSignalInputQueueReadData() {
        this.externalSignalInputQueueReadData++;
    }

    @ManagedAttribute
    public int getAIOPsSignalOutputQueueWriteData() {
        return aiopsSignalOutputQueueWriteData;
    }

    public void updateAIOPsSignalOutputQueueWriteData() {
        aiopsSignalOutputQueueWriteData++;
    }

    @ManagedAttribute
    public String getWriteAIOPsSignalQueueName() {
        return writeAIOPsSignalQueueName;
    }


    public void updateProcessDetails(String keyName, long value) {
        processDetails.put(keyName, value);
        processDetails.put(keyName + "Count", processDetails.getOrDefault(keyName + "Count", 0L) + 1);
        if (processDetails.getOrDefault(keyName + "Min", 0L) > value) {
            processDetails.put(keyName + "Min", value);
        }

        if (processDetails.getOrDefault(keyName + "Max", 0L) < value) {
            processDetails.put(keyName + "Max", value);
        }
    }

    public void updateThreadRejectedTaskCount(String keyName) {
        threadRejectedTaskCountMap.put(keyName, threadRejectedTaskCountMap.getOrDefault(keyName, 0L) + 1);
    }

    public String printRejectedTaskCount() {
        StringBuilder sb = new StringBuilder();
        sb.append("Rejected tasks count:- ");
        threadRejectedTaskCountMap.forEach((key, value) -> sb.append(key).append(" : ").append(value).append(", "));
        if (!threadRejectedTaskCountMap.isEmpty()) {
            return sb.delete(sb.length() - 2, sb.length()).append("\n").toString();
        } else {
            return sb.append("\n").toString();
        }
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Read Count Signal Input Queue : ").append(getSignalInputQueueReadData()).append(", ");
        sb.append("Write Count Signal Output Queue : ").append(getSignalOutputQueueWriteData()).append(", ");
        sb.append("Errors Count : ").append(getErrors()).append(", ");
        sb.append("Read Signal Queue name : ").append(getReadSignalQueueName()).append(", ");
        sb.append("Write Signal Queue name : ").append(getWriteSignalQueueName()).append(", ");
        sb.append("Events under Maintenance Count : ").append(getMaintenanceEvents()).append(", ");
        sb.append("Open Signal Count : ").append(getSignalOpenCount()).append(", ");
        sb.append("Closed Signal Count : ").append(getSignalCloseCount()).append(", ");
        sb.append("Update Signal Count : ").append(getSignalUpdateCount()).append(", ");
        sb.append("Current Snapshots size : ").append(getSnapshots().size()).append(", ");
        sb.append("Redis keys not found count : ").append(getRedisKeysNotFound()).append(", ");
        sb.append("Current ProcessDetails size : ").append(getProcessDetails().size()).append(", ");
        sb.append("External Signal Queue Name : ").append(getExternalSignalInputQueueName()).append(", ");
        sb.append("External Signal Queue Read Count : ").append(getExternalSignalInputQueueReadData()).append(", ");
        sb.append("AIOPS Write Signal Queue name : ").append(getWriteAIOPsSignalQueueName()).append(", ");
        sb.append("AIOPS Write Output Queue Signal Count : ").append(getAIOPsSignalOutputQueueWriteData()).append(", ");
        sb.append("Failed Open Signal Queue Size : ").append(getFailedQueueSize());
        return sb.toString();
    }
}
