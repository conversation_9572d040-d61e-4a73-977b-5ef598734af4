# ==========================================================
# RabbitMQ server configuration
# Basic configuration needed to communicate with RabbitMQ.
# HTTP mode of communication will be utilized in this case.
# ==========================================================
spring.rabbitmq.addresses=localhost:5673,localhost:5674
spring.rabbitmq.username=guest
spring.rabbitmq.password=
spring.rabbitmq.ssl.enabled=false
spring.rabbitmq.ssl.algorithm=TLSv1.2

anomaly.messages.queue.name=anomaly-event-signal-messages
mle.signal.messages.queue.name=mle-signal-messages
signal.messages.queue.name=signal-messages
aiops.signal.messages.queue.name=signal-messages-aiops


# ==========================================================
# OpenSearch Details
# ==========================================================
opensearch.nodes=**************:9200
opensearch.username=admin
opensearch.password=
opensearch.protocol=http
opensearch.max.connections.per.route=10
opensearch.max.connections.total=20
opensearch.connection.timeout.secs=20
opensearch.connection.socket.timeout.secs=30
opensearch.anomalies.index=heal_anomalies
opensearch.signals.index=heal_signals
opensearch.data.push.schedule.initial.delay=2
opensearch.data.push.schedule.interval=5
opensearch.batch.size=500
opensearch.batch.queue.max.size=10000

# ==========================================================
#Redis Server Configuration
# ==========================================================
#spring.redis.cluster.nodes=192.168.13.101:9001,192.168.13.101:9002,192.168.13.102:9001,192.168.13.102:9002,192.168.13.103:9001,192.168.13.103:9002
spring.redis.cluster.nodes=redis-node1.appnomic:9001,redis-node1.appnomic:9002,redis-node2.appnomic:9001,redis-node2.appnomic:9002,redis-node3.appnomic:9001,redis-node3.appnomic:9002
spring.redis.ssl=true
spring.redis.password=

# ==========================================================
# Cassandra ttl configuration - in days
# ==========================================================
spring.data.cassandra.userSignalTTL=180
spring.data.cassandra.signalDetailsTTL=180
spring.data.cassandra.serviceSignalDetailsTTL=180
spring.data.cassandra.accountSignalTTL=180
spring.data.cassandra.signalSummaryTTL=180
spring.data.cassandra.signalInstanceDetailsTTL=180
spring.data.cassandra.signalRequestDetailsTTL=180
spring.data.cassandra.userRemainderNotificationDetailsTTL=180

# ==========================================================
# Thread Pool Configuration
# ==========================================================
thread.pool.core.size=100
thread.pool.max.size=200

# ==========================================================
# Signal Severity for Aggregated KPI data
# ==========================================================
signal.severity.high=Severe
signal.severity.low=Default
signal.cleaner.scheduler.milliseconds=30000
problem.signal.kpis=397,398,399,400,401,402,403,405,406

# ==========================================================
# Batch Job Persistence Suppression Details
# ==========================================================
batch.job.suppression.value=3
batch.job.collection.interval=60

# ==========================================================
# Violated Event Out Of Bound Details
# ==========================================================
violatedData.outofbound.valueInMins=5

# ==========================================================
# Health Metrics Details
# ==========================================================
health.metrics.update.interval.milliseconds=60000
health.metrics.log.interval.milliseconds=10000
management.endpoints.web.exposure.include=*
management.endpoints.jmx.exposure.include=*
management.endpoint.health.enabled=true
management.endpoints.web.base-path=/measure
management.server.port=8989
spring.jmx.enabled=true

# =========================================================
# Local Cache Details
# =========================================================
configuration.cache.expire.interval.minutes=10
configuration.cache.max.size=500
dropped.anomaly.checker.scheduler.seconds=60
failed.open.signal.queue.max.size=1000